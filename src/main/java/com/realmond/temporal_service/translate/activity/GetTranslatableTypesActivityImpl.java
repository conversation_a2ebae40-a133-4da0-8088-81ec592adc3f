package com.realmond.temporal_service.translate.activity;

import com.realmond.temporal_service.translate.svc.TranslateService;
import io.temporal.spring.boot.ActivityImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ActivityImpl(taskQueues = "translation-task-queue")
public class GetTranslatableTypesActivityImpl implements GetTranslatableTypesActivity {

    private final Logger LOGGER = LoggerFactory.getLogger(GetTranslatableTypesActivityImpl.class);
    private final TranslateService translateService;

    public GetTranslatableTypesActivityImpl(@Autowired TranslateService translateService) {
        this.translateService = translateService;
    }

    @Override
    public List<List<String>> getTranslatableContentTypes() {
        LOGGER.info("Fetching translatable content types");
        return translateService.getTranslatableContentTypes();
    }
}
