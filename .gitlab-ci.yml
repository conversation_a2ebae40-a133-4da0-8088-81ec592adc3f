# BEGIN job-specific configuration
.job.base:
  timeout: 30 minutes
  before_script:
    - if [[ ! -z  "${PROJECT_NAME}" ]]; then cd $PROJECT_NAME; fi

.job.release:
  image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond/pulumi-node-18-bullseye-3.82.1-2e74917:ga
  stage: release
  variables:
    REPO: https://gitlab-ci-token:${PROJECT_ACCESS_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    GIT_SSL_NO_VERIFY: "true"
  before_script:
    - git remote set-url origin $REPO
  script:
    - pip install commitizen
    - cz bump --changelog --yes
  #    - export VERSION=$(git tag -l | head -1)
  #    - export CHANGELOG=$(npm run release --dry-run | sed -e '1,5d' | head -n -5)
  after_script:
    - git push -o ci.skip --follow-tags origin HEAD:${CI_COMMIT_REF_NAME}
    - git push origin --tags
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
      when: never
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
#  release: # See https://docs.gitlab.com/ee/ci/yaml/#release for available properties
#    tag_name: $VERSION
#    description: $CHANGELOG

.job.test:
  image:
    name: openjdk:21-slim
  stage: test
  extends:
    - .job.base
  script:
    - ./mvnw test

.job.build-docker:
  variables:
    AWS_ACCESS_KEY_ID: $DOCKER_BUILD_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $DOCKER_BUILD_AWS_SECRET_ACCESS_KEY
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [ "" ]
  stage: build
  script:
    - '/busybox/sh -c env > /kaniko/.env'
    - '/busybox/sh -c echo "{ \"credStore\": \"ecr\" }" > /kaniko/.docker/config.json'
    - /kaniko/executor ${BUILD_ARGS} --context $IMAGE_CTX --dockerfile $DOCKERFILE --destination "${DESTINATION}:${CI_COMMIT_TAG:-$CI_COMMIT_SHORT_SHA}" --destination "${DESTINATION}:latest"

.job.preview:
  image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond/pulumi-node-18-bullseye-3.82.1-2e74917:ga
  variables:
    AWS_ACCESS_KEY_ID: $DEPLOY_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $DEPLOY_AWS_SECRET_ACCESS_KEY
  stage: preview
  before_script:
    - cd pulumi
    - yarn install
  script:
    - pulumi login $AWS_STATE_BACKEND_URL
    - pulumi stack select $STACK_NAME
    - pulumi preview --color always --non-interactive --diff --tracing http://gitlab.realmond.dev:9411/api/v1/spans| tee >(ansi2html > preview.html)
    #    for troubleshooting
  #    - pulumi preview --color always --non-interactive --diff --logtostderr --logflow -v=11 --tracing http://gitlab.realmond.dev:9411/api/v1/spans | tee >(ansi2html > preview.html)
  artifacts:
    expire_in: 2 weeks
    expose_as: preview
    paths:
      - pulumi/preview.html

.job.converge:
  image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond/pulumi-node-18-bullseye-3.82.1-2e74917:ga
  variables:
    AWS_ACCESS_KEY_ID: $DEPLOY_AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY: $DEPLOY_AWS_SECRET_ACCESS_KEY
  stage: deploy
  before_script:
    - cd pulumi
    - yarn install
  script:
    - pulumi login $AWS_STATE_BACKEND_URL
    - pulumi stack select $STACK_NAME
    - pulumi refresh --yes --color always --non-interactive --diff --tracing http://gitlab.realmond.dev:9411/api/v1/spans| tee >(ansi2html > refresh.html)
    - pulumi up --yes --color always --non-interactive --diff --tracing http://gitlab.realmond.dev:9411/api/v1/spans| tee >(ansi2html > up.html)
    #      for troubleshooting
  #    - pulumi refresh --yes --color always --non-interactive --diff --logtostderr --logflow -v=11 --tracing http://gitlab.realmond.dev:9411/api/v1/spans | tee >(ansi2html > refresh.html)
  #    - pulumi up --yes --color always --non-interactive --diff --logtostderr --logflow -v=11 --tracing http://gitlab.realmond.dev:9411/api/v1/spans | tee >(ansi2html > up.html)
  artifacts:
    name: result
    expire_in: 2 weeks
    paths:
      - pulumi/refresh.html
      - pulumi/up.html

.job.notify:
  image: quay.io/curl/curl:latest
  stage: notify
  script:
    - sh bin/notify.sh "${NOTIFY_MSG}"

.job.approve:
  stage: approve
  variables:
    GIT_STRATEGY: none
  script:
    - echo "the pipeline $CI_PIPELINE_ID is approved"
  when: manual
  allow_failure: false

#
# END job-specific configuration
# BEGIN image-specific configuration
.image.workflow-worker:
  variables:
    IMAGE_CTX: .
    DOCKERFILE: Dockerfile
    DESTINATION: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond-app/workflow-worker
# END image-specific configuration

# BEGIN project-specific configuration

.project.pulumi:
  image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond/pulumi-node-18-bullseye-3.82.1-2e74917:ga
  variables:
    PROJECT_NAME: pulumi
  rules:
    - changes:
        - pulumi/**/*
#
# END project-specific configuration

# BEGIN git lifecycle
.git.merge-request:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

.ci.disable:
  rules:
    - when: never

.git.main:
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_TAG == null || $CI_COMMIT_TAG == "")

.git.release:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v(?:\d+.){2}(?:\d+)$/

.gitlab.deploy[prod]:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v(?:\d+.){2}(?:\d+)$/
# END git lifecycle

# BEGIN environment-specific configuration

.environment.prod:
  variables:
    STACK_NAME: prod
  environment:
    name: prod


.environment.staging:
  variables:
    STACK_NAME: staging
  environment:
    name: staging

# END environment-specific configuration

# BEGIN actual pipeline definition

default:
  image: node:18.17.1-alpine

stages:
  - test
  - build
  - release
  - preview
  - approve
  - deploy
  - notify

test:
  extends:
    - .job.test
    - .git.merge-request

test[pulumi]:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      changes:
        - pulumi/**/*
  extends:
    - .job.test
    - .project.pulumi
    - .git.merge-request

build[staging]:
  extends:
    - .job.build-docker
    - .image.workflow-worker
    - .git.main

deploy[staging]:
  needs:
    - build[staging]
  extends:
    - .project.pulumi
    - .job.converge
    - .git.main
    - .environment.staging

notify[staging][success]:
  when: on_success
  needs:
    -  deploy[staging]
  variables:
    ENVIRONMENT: staging
    NOTIFY_MSG: 'Deploy status: ✅'
  extends:
    - .git.main
    - .job.notify

notify[staging][failure]:
  when: on_failure
  needs:
    -  deploy[staging]
  variables:
    ENVIRONMENT: staging
    NOTIFY_MSG: 'Deploy status: ❌'
  extends:
    - .git.main
    - .job.notify

# prod

release:
  extends:
    - .job.release

build[prod]:
  extends:
    - .job.build-docker
    - .image.workflow-worker
    - .gitlab.deploy[prod]

preview[prod]:
  needs:
    - build[prod]
  extends:
    - .project.pulumi
    - .job.preview
    - .environment.prod
    - .gitlab.deploy[prod]

approve[prod]:
  needs:
    - preview[prod]
  extends:
    - .job.approve
    - .environment.prod
    - .gitlab.deploy[prod]

deploy[prod]:
  needs:
    - approve[prod]
  extends:
    - .project.pulumi
    - .job.converge
    - .gitlab.deploy[prod]
    - .environment.prod

notify[prod][success]:
  when: on_success
  needs:
    -  deploy[prod]
  variables:
    ENVIRONMENT: production
    NOTIFY_MSG: 'Deploy status: ✅'
  extends:
    - .gitlab.deploy[prod]
    - .job.notify

notify[prod][failure]:
  when: on_failure
  needs:
    -  deploy[prod]
  variables:
    ENVIRONMENT: production
    NOTIFY_MSG: 'Deploy status: ❌'
  extends:
    - .gitlab.deploy[prod]
    - .job.notify
# END actual pipeline definition
