package com.realmond.temporal_service.etl.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "dynamic_table") // Default table name, will be overridden dynamically
public class DynamicRecord {

    @Id
    @Column(length = 256)
    private String urn;

    @Column(name = "source_urn", nullable = false, length = 256)
    private String sourceUrn;

    @Column(columnDefinition = "jsonb", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Object data;

    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Object metadata;

    @Column(name = "sync_execution_id", nullable = false, length = 256)
    private String syncExecutionId;

    @Column(name = "synced_at", nullable = false)
    private ZonedDateTime syncedAt;
}
