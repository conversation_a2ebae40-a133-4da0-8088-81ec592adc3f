package com.realmond.temporal_service.crawler.err;

public final class RetryableCrawlerException extends CrawlerException {
    public RetryableCrawlerException(String message) {
        super(message);
    }

    public RetryableCrawlerException(String message, Throwable cause) {
        super(message, cause);
    }

    public RetryableCrawlerException(Throwable cause) {
        super(cause);
    }

    public RetryableCrawlerException(
          String message,
          Throwable cause,
          boolean enableSuppression,
          boolean writableStackTrace
    ) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
