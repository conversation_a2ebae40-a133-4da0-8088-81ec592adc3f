package com.realmond.temporal_service.translate.activity.strategy;

import com.realmond.temporal_service.translate.dto.SingleTranslateRequest;
import com.realmond.temporal_service.translate.svc.TranslateService;
import io.temporal.activity.Activity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SequentialTranslateStrategy implements TranslateStrategy {

    private final TranslateService translateService;

    @Autowired
    public SequentialTranslateStrategy(TranslateService translateService) {
        this.translateService = translateService;
    }

    @Override
    public TranslationResult processEntities(List<Long> entityIds,
                                           String contentType,
                                           String sourceLocale,
                                           String targetLocale) {

        int successCount = 0;
        Map<Long, String> errors = new HashMap<>();

        for (int i = 0; i < entityIds.size(); i++) {
            // Send heartbeat from main thread regularly
            if (i % 5 == 0) {
                Activity.getExecutionContext().heartbeat(
                        String.format("Processing translations sequentially. Progress: %d/%d",
                                i, entityIds.size())
                );
            }

            Long id = entityIds.get(i);
            try {
                SingleTranslateRequest request = new SingleTranslateRequest(
                        contentType, id, sourceLocale, targetLocale, true);
                translateService.translate(request);
                successCount++;
                log.debug("Successfully translated {} entity {} to {}", contentType, id, targetLocale);
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                errors.put(id, errorMsg);
                log.error("Error translating {} entity {}: {}", contentType, id, errorMsg);
            }
        }

        return new TranslationResult(successCount, errors);
    }
}
