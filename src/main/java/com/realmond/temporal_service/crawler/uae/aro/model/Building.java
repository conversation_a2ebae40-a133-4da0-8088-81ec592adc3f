package com.realmond.temporal_service.crawler.uae.aro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Represents basic information about a building returned by
 * GET /api/v1/buildings endpoint.
 *
 * The schema is derived from live calls to the ARO.ae API:
 * {
 *   "id": 178100,
 *   "building_template_id": 4464,
 *   "number": "Pier Point 2",
 *   "position": {
 *       "type": "Point",
 *       "coordinates": [ 55.286027763, 25.267120938, 10 ]
 *   },
 *   "category": "Apartments",
 *   "price_from": { "currency_code": 784, "amount": 3055888 },
 *   "availabilities_count": 1,
 *   "height": null,
 *   "target_height": null
 * }
 */
@Data
public class Building {
    private Integer id;
    @JsonProperty("building_template_id")
    private Integer buildingTemplateId;

    /** Human-readable building number / name. */
    private String number;

    private Position position;

    /** Category of the building (e.g. "Apartments", "Villas", …). */
    private String category;

    @JsonProperty("price_from")
    private Price priceFrom;

    @JsonProperty("availabilities_count")
    private Integer availabilitiesCount;

    /** Existing height in metres (may be null). */
    private Double height;

    /** Planned/target height in metres (may be null). */
    @JsonProperty("target_height")
    private Double targetHeight;

    /**
     * GeoJSON-like position object.
     */
    @Data
    public static class Position {
        private String type;
        private List<Double> coordinates;
    }
} 