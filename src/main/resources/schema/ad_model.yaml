---
"$schema": http://json-schema.org/draft-07/schema#
title: ad
type: object
definitions:
  purpose:
    type: string
    description: ad purpose
    enum:
      - for-sale
      - for-rent
  rent_frequency:
    type: string
    description: rent frequency. should be specified if ad purpose is for-rent
    enum:
      - yearly
      - monthly
      - weekly
      - daily
  price:
    $ref: common/price_model.yaml
properties:
  ad_urn:
    type: string
    description: Unique Ad name used as unique id.
  developer_urn:
    type: string
    description: Unique developer name used as unique id.
  project_urn:
    type: string
    description: Unique project name used as unique id.
  source_urn:
    type: string
    description: Unique source name used as unique id
  external_id:
    type: string
  building_urn:
    type: string
    description: Unique building name used as unique id
  area_sqm:
    type: number
    description: Total area in square meters
  bedrooms:
    type: integer
    description: The number of bedrooms in the unit.
    minimum: 0
  bedrooms_title:
    type: string
    description: A custom title for bedrooms setup
  maiden_rooms:
    type: integer
    description: The number of utility/maiden rooms in the unit.
  bathrooms:
    type: integer
    description: The number of bathrooms in the unit.
    minimum: 0
  furnished:
    type: string
    description: furnishing status of the unit.
    enum:
      - partial
      - full
      - none
  completion_status:
    $ref: common/construction_status_model.yaml
  completion_date:
    type: string
    description: The expected or actual completion date of the unit.
    format: date
  purpose:
    $ref: "#/definitions/purpose"
  rent_frequency:
    $ref: "#/definitions/rent_frequency"
  view_type:
    type: string
  price:
    $ref: "#/definitions/price"
  floor:
    $ref: common/floor_model.yaml
  freehold:
    type: boolean
  description:
    type: string
  property_type:
    $ref: common/property_type_model.yaml
  images:
    type: array
    description: A collection of images associated with the ad.
    items:
      $ref: common/image_model.yaml
  payment_plans:
    type: array
    items:
      oneOf:
        - $ref: common/payment_plan_model.yaml
        - type: "object"
          title: payment plan reference
          properties:
            payment_plan_urn:
              type: string
          required:
            - payment_plan_urn
  floor_plans:
    type: array
    items:
      oneOf:
        - $ref: common/floor_plan_model.yaml
        - type: object
          title: floor plan reference
          properties:
            floor_plan_urn:
              type: string
          required:
            - floor_plan_urn
  parking:
    type: integer
    minimum: 0
  additional_data:
    additionalProperties: true

required:
  - developer_urn
  - project_urn
  - external_id
  - ad_urn
  - source_urn
