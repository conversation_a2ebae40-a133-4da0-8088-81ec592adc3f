# DAMAC Properties Parser Testing and Improvements

This document outlines the testing approach for the `DamacPropertyParserService` class and the improvements made to fix issues with the property parsing functionality.

## Issues Fixed

### 1. Image Filtering
- Added logic to filter and limit images specific to the property being parsed
- Images are now limited to a maximum of three per property
- Implemented helper method `isPropertySpecificImage` to check if an image belongs to the current property
- Image selection prioritizes carousel images first, then falls back to thumbnails

### 2. Price Parsing
- Enhanced price parsing to handle formatting and currency properly
- Added robustness to extract numeric values from price text with formatting
- Fixed an issue where price could be null by ensuring a default price model is always created
- Added comprehensive logging to track price extraction process
- Implemented fallback logic to extract digits if the standard parsing fails
- Fixed scientific notation issue: Prevented large numbers from being incorrectly formatted (e.g., 4.998004998E11 instead of 499,800)
- Added sanity checks for unreasonably large price values with fallback to simpler digit extraction

### 3. Field Formatting
- Fixed field values by removing prefixes (e.g., "View Type", "Price/SQFT")
- Improved extraction of unit type and view type values
- Added fallback mechanisms for missing view types, using project name as a default
- Implemented proper error handling for parsing exceptional cases

### 4. Additional Data Mapping
- Ensured that all additional data fields are properly extracted and mapped
- Added more robust checking for values before adding them to the additional data map
- Fixed issue with missing values in the minimal builder case

### 5. Enhanced Logging
- Added comprehensive debug logging throughout the parsing process
- Implemented entry/exit logs for major methods to track execution flow
- Added detailed logs for critical operations like price parsing, image filtering, and field extraction
- Improved exception handling with better error messages and stack traces
- Added summary logs at the end of each property parsing for easy monitoring

## Test Structure

The test class `DamacPropertyParserServiceTest` includes several types of tests:

1. **Mock-based Tests** - Using Mockito to simulate HTML elements:
   - `testImageParsing` - Verifies image filtering and selection logic
   - `testPriceParsing` - Validates price extraction and model construction
   - `testAdditionalDataParsing` - Tests field cleaning and additional data mapping

2. **Real HTML Test** - `testWithRealHtml` validates parsing logic with actual HTML:
   - Uses a real HTML file to test the end-to-end parsing
   - Checks that property-specific images are correctly extracted
   - Validates price extraction from real data
   - Verifies field formatting for view type and other fields

## Running Tests

To run the parser tests:

1. Ensure the HTML file exists at `/Users/<USER>/workflows/DAMAC - Unit Search.html`
2. Execute the test script:
   ```
   ./run-parser-tests.sh
   ```
   or run the tests directly:
   ```
   mvn test -Dtest=com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserServiceTest
   ```
3. To run just the real HTML test (which validates most improvements):
   ```
   mvn test -Dtest=com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserServiceTest#testWithRealHtml
   ```
4. To enable debug logging for tests:
   ```
   mvn test -Dtest=com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserServiceTest -Dorg.slf4j.simpleLogger.defaultLogLevel=DEBUG
   ```

## Logging Enhancements

The parser now includes comprehensive logging to help with debugging and monitoring:

### Method Entry/Exit Logging
```java
// Method entry 
log.info("Starting to parse properties from current page with wait time {} seconds", waitTimeSeconds);

// Method exit with summary
log.info("Successfully parsed {} properties, with {} errors", successCount, errorCount);
```

### Price Parsing Logs
```java
// Raw price extraction
log.debug("Extracted raw price text: {}", priceText);
log.debug("Extracted numeric price value: '{}'", priceValue);

// Price parsing result
log.debug("Successfully parsed price value: {}", value);
log.debug("Created price model: currency={}, value={}", price.getCurrency(), price.getValue());
```

### Image Filtering Logs
```java
// Image search initialization
log.debug("Starting to extract images for property: {} ({})", unit, projectName);
log.debug("Unit identifier for image filtering: {}", unitIdentifier);

// Image filtering decisions
log.debug("Checking carousel image: {}", imgSrc);
log.debug("Image {} is{} specific to property", imgSrc, isSpecific ? "" : " not");

// Image extraction summary
log.debug("Total property-specific images found: {}", imageModels.size());
```

### Field Processing Logs
```java
// View type extraction
log.debug("Raw view type text: '{}'", viewTypeText);
log.debug("Extracted view type after removing prefix: '{}'", viewType);

// Fallback values
log.debug("Setting default view type to project name: '{}'", viewType);
```

### Property Summary Log
```java
// Final property summary
log.info("Successfully parsed property: project={}, unit={}, type={}, bedrooms={}, area={}, price={}, images={}",
    projectName, unit, propertyType, bedrooms, areaSqm, price.getValue(), imageModels.size());
```

## Parsing Improvements

### Clean Numeric Extraction
- Enhanced methods to extract numeric values from text:
  ```java
  // Extract only digits from formatted text
  String numericPart = priceText.replaceAll("[^0-9.]", "");
  ```

### Price Format Handling
- Implemented smart handling of price formatting to prevent scientific notation issues:
  ```java
  // Special case for our specific format: "499800 AED 499,800"
  String[] parts = text.trim().split("\\s+");
  if (parts.length > 0) {
      String firstPart = parts[0];
      
      // Check if the first part is entirely numeric
      if (firstPart.matches("\\d+")) {
          log.debug("First part is numeric: '{}'", firstPart);
          return firstPart;
      }
      
      // Special case: if first part is a currency code (like "AED")
      if (firstPart.matches("[A-Za-z]+") && parts.length > 1) {
          // Take the second part and extract numeric value
          String secondPart = parts[1];
          String numericSecondPart = secondPart.replaceAll("[^0-9]", "");
          return numericSecondPart;
      }
  }
  ```
- Handles various price formats reliably:
  ```java
  // Format examples
  "499800 AED 499,800" → "499800"
  "AED 476,000" → "476000"
  "2,500,000" → "2500000"
  ```
- Fixed scientific notation issue where prices like 499,800 were incorrectly shown as 4.998004998E11

### Image Filtering
- Implemented logic to identify property-specific images:
  ```java
  // Only include images specific to this property
  if (isPropertySpecificImage(imgSrc, unitIdentifier, projectIdentifier)) {
      // Add image to the model
  }
  ```

### Field Value Cleaning
- Added methods to clean field values:
  ```java
  // Remove prefix from field value
  if (viewTypeText.contains("View Type")) {
      viewType = viewTypeText.replace("View Type", "").trim();
  }
  ```

### Default Value Handling
- Added defaults and fallbacks for important fields:
  ```java
  // Set default view type if not found
  if (viewType == null && projectName != null) {
      viewType = projectName;
  }
  
  // Ensure price exists
  if (price == null) {
      price = new PriceModel();
      price.setCurrency("AED");
      price.setValue(0.0);
  }
  ```

## Future Improvements

1. **Caching Mechanism**
   - Implement a cache for parsed properties to avoid redundant processing

2. **Pagination Handling**
   - Enhance the crawler to handle pagination more robustly

3. **Error Recovery**
   - Add more graceful error recovery for parsing failures
   - Implement retry mechanisms for transient issues

4. **Performance Optimization**
   - Review and optimize extraction methods to reduce processing time
   - Consider parallelizing property parsing when appropriate

5. **Mock Test Enhancement**
   - Fix the mock setup in unit tests to provide better test coverage
   - Use better mockito patterns to avoid issues with argument matchers 
   
6. **Log Analysis**
   - Add log analysis tools to monitor property parsing performance
   - Implement metrics collection from logs to track success rates 

## Logging Configuration

A custom logging configuration has been added to provide more control over the parser logs. The configuration is defined in `src/main/resources/logback-spring.xml` and includes:

1. **Dedicated Parser Log File**
   - All parser-specific logs are written to a separate `parser.log` file
   - Logs are automatically rotated by date and size (max 10MB per file)
   - Historical logs are archived in the `logs/archived` directory

2. **Environment-Specific Settings**
   - **Development**: Debug level enabled for all crawler components
   - **Production**: Info level for the parser to reduce log volume
   - **Test**: Trace level for the parser to capture all details during testing

3. **Console Output**
   - Colored console output for better readability
   - Includes timestamp, thread, level, and class information

### How to Configure Log Levels

The logging levels can be adjusted in the following ways:

1. **Application Properties**:
   ```properties
   logging.level.com.realmond.temporal_service.crawler.uae.damacproperties=DEBUG
   ```

2. **Environment Variables**:
   ```shell
   export LOGGING_LEVEL_COM_REALMOND_TEMPORAL_SERVICE_CRAWLER_DAMACPROPERTIES=DEBUG
   ```

3. **JVM Arguments**:
   ```shell
   java -Dlogging.level.com.realmond.temporal_service.crawler.uae.damacproperties=DEBUG -jar application.jar
   ```

4. **Spring Profiles**:
   The logging configuration automatically adjusts based on the active Spring profile:
   ```shell
   java -Dspring.profiles.active=dev -jar application.jar
   ```

### Log File Locations

- Main application log: `./logs/application.log`
- Parser-specific log: `./logs/parser.log`
- Archived logs: `./logs/archived/` 
