package com.realmond.temporal_service.etl.repository;

import com.realmond.temporal_service.etl.model.DynamicRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface DynamicRepository extends JpaRepository<DynamicRecord, String> {

    /**
     * Find records by source URN
     * @param sourceUrn the source URN to search for
     * @return list of matching records
     */
    List<DynamicRecord> findBySourceUrn(String sourceUrn);

    /**
     * Find records by sync execution ID
     * @param syncExecutionId the sync execution ID to search for
     * @return list of matching records
     */
    List<DynamicRecord> findBySyncExecutionId(String syncExecutionId);

    /**
     * Count records by source URN
     * @param sourceUrn the source URN to count
     * @return count of matching records
     */
    long countBySourceUrn(String sourceUrn);
}
