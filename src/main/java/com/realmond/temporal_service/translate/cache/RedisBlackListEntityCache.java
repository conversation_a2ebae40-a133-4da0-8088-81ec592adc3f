package com.realmond.temporal_service.translate.cache;

import com.realmond.temporal_service.config.RedisConfigProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * A Redis-backed implementation of the BlackListEntityCache.
 * Uses Redis Sets to store the blacklisted entity IDs.
 */
@SuppressWarnings("ConstantValue")
@Component
@Profile("redis-black-list-cache")
public class RedisBlackListEntityCache implements BlackListEntityCache {

    private static final Logger logger = LoggerFactory.getLogger(RedisBlackListEntityCache.class);

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisConfigProperties properties;

    public RedisBlackListEntityCache(RedisTemplate<String, Object> redisTemplate, RedisConfigProperties properties) {
        this.redisTemplate = redisTemplate;
        this.properties = properties;
        logger.info("Initialized Redis black list entity cache with host: {}, port: {}",
                properties.getHost(), properties.getPort());
    }

    private String getRedisKey(String key) {
        return properties.getKeyPrefix() + key;
    }

    @Override
    public Set<Long> get(String key) {
        Set<Object> members = redisTemplate.opsForSet().members(getRedisKey(key));
        if (members == null) {
            return null;
        }
        return members.stream()
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(Collectors.toSet());
    }

    @Override
    public void addToSet(String key, Long value) {
        String redisKey = getRedisKey(key);
        redisTemplate.opsForSet().add(redisKey, value);
        redisTemplate.expire(redisKey, properties.getExpirationHours(), TimeUnit.HOURS);
        logger.info("Added value {} to key {}", value, key);
    }

    @Override
    public boolean removeFromSet(String key, Long value) {
        String redisKey = getRedisKey(key);
        Long removedCountMaybe = redisTemplate.opsForSet().remove(redisKey, value);
        Boolean removed = removedCountMaybe != null && removedCountMaybe > 0;

        if (Boolean.TRUE.equals(removed)) {
            logger.info("Removed value {} from key {}", value, key);

            // Check if the set is now empty
            Long size = redisTemplate.opsForSet().size(redisKey);
            if (size != null && size == 0) {
                redisTemplate.delete(redisKey);
                logger.info("Removed key {} as its Set became empty", key);
            }
            return true;
        }
        return false;
    }

    @Override
    public void put(String key, Set<Long> value) {
        if (value == null) {
            throw new IllegalArgumentException("Value cannot be null");
        }

        String redisKey = getRedisKey(key);
        // Delete existing key if it exists
        redisTemplate.delete(redisKey);

        // Add all values to the set
        if (!value.isEmpty()) {
            redisTemplate.opsForSet().add(redisKey,
                    value.toArray(new Object[0]));
            redisTemplate.expire(redisKey, properties.getExpirationHours(), TimeUnit.HOURS);
        }

        logger.info("Added/Updated key: {}", key);
    }

    @Override
    public Set<Long> remove(String key) {
        String redisKey = getRedisKey(key);
        Set<Long> result = get(key);
        if (result != null) {
            redisTemplate.delete(redisKey);
            logger.info("Removed key: {}", key);
        }
        return result;
    }

    @Override
    public void clear() {
        Set<String> keys = redisTemplate.keys(properties.getKeyPrefix() + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        logger.info("Cache cleared manually.");
    }

    @Override
    public int size() {
        Set<String> keys = redisTemplate.keys(properties.getKeyPrefix() + "*");
        return keys != null ? keys.size() : 0;
    }

    @Override
    public boolean containsKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(getRedisKey(key)));
    }

}
