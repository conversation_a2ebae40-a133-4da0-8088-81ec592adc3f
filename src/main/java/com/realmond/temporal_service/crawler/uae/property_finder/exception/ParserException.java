package com.realmond.temporal_service.crawler.uae.property_finder.exception;

/**
 * Base exception class for all parser-related exceptions.
 * Provides a consistent way to handle errors in the parser component.
 */
public class ParserException extends RuntimeException {

    /**
     * Creates a new ParserException with the specified message.
     *
     * @param message The error message
     */
    public ParserException(String message) {
        super(message);
    }

    /**
     * Creates a new ParserException with the specified message and cause.
     *
     * @param message The error message
     * @param cause The cause of the exception
     */
    public ParserException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Creates a new ParserException with the specified cause.
     *
     * @param cause The cause of the exception
     */
    public ParserException(Throwable cause) {
        super(cause);
    }
}
