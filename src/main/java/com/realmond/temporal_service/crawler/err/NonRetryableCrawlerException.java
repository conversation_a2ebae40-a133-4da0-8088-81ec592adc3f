package com.realmond.temporal_service.crawler.err;

public final class NonRetryableCrawlerException extends CrawlerException {
    public NonRetryableCrawlerException(String message) {
        super(message);
    }

    public NonRetryableCrawlerException(String message, Throwable cause) {
        super(message, cause);
    }

    public NonRetryableCrawlerException(Throwable cause) {
        super(cause);
    }

    public NonRetryableCrawlerException(
          String message,
          Throwable cause,
          boolean enableSuppression,
          boolean writableStackTrace
    ) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public final static NonRetryableCrawlerException PARSE_ALL_PAGES_NOT_SUPPORTED = new NonRetryableCrawlerException("fetching all entries is not supported");
    public final static NonRetryableCrawlerException INCREMENTAL_FETCH_NOT_SUPPORTED = new NonRetryableCrawlerException("incremental fetch is not supported");
}
