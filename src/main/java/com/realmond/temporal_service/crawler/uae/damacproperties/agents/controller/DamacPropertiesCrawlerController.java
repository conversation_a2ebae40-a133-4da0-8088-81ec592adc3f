package com.realmond.temporal_service.crawler.uae.damacproperties.agents.controller;

import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.CrawlingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for managing Damac Properties crawler operations.
 * Provides endpoints to trigger crawling manually.
 */
@Slf4j
@RestController
@RequestMapping("/api/crawler/damac-properties")
public class DamacPropertiesCrawlerController {

    private final CrawlingService crawlingService;

    public DamacPropertiesCrawlerController(@Qualifier("propertiesCrawler") CrawlingService crawlingService) {
        this.crawlingService = crawlingService;
    }

    /**
     * Endpoint to trigger the crawler manually via the startCrawling method.
     * This method follows the same pattern as the scheduled execution.
     *
     * @return ResponseEntity with status and message
     */
    @PostMapping("/trigger")
    public ResponseEntity<String> triggerCrawler() {
        log.info("Manual crawler trigger requested");
        try {
            crawlingService.startCrawling();
            return ResponseEntity.ok("Crawler triggered successfully");
        } catch (Exception e) {
            log.error("Error triggering crawler: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Failed to trigger crawler: " + e.getMessage());
        }
    }

    /**
     * Endpoint to directly execute the crawlProperties method.
     * This bypasses any additional logic in the startCrawling method.
     *
     * @return ResponseEntity with status and message
     */
    @PostMapping("/execute")
    public ResponseEntity<String> executeCrawler() {
        log.info("Direct crawler execution requested");
        try {
            crawlingService.crawlProperties();
            return ResponseEntity.ok("Crawler executed successfully");
        } catch (Exception e) {
            log.error("Error executing crawler: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Failed to execute crawler: " + e.getMessage());
        }
    }

    /**
     * Endpoint to check the status of the crawler.
     *
     * @return ResponseEntity with status message
     */
    @GetMapping("/status")
    public ResponseEntity<String> getStatus() {
        // This is a simple status endpoint that could be enhanced with actual status information
        return ResponseEntity.ok("Crawler service is running");
    }
}
