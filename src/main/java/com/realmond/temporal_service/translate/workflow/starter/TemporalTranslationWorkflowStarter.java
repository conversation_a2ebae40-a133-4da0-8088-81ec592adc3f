package com.realmond.temporal_service.translate.workflow.starter;

import com.realmond.temporal_service.translate.workflow.Common;
import com.realmond.temporal_service.translate.workflow.TranslateCMSWorkflow;
import io.temporal.api.enums.v1.WorkflowExecutionStatus;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowExecutionDescription;
import io.temporal.client.WorkflowOptions;
import io.temporal.client.WorkflowStub;
import io.temporal.worker.WorkerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TemporalTranslationWorkflowStarter {

    private final WorkflowClient workflowClient;
    private final WorkerFactory workerFactory;

    @Autowired
    public TemporalTranslationWorkflowStarter(
            WorkflowClient workflowClient,
            WorkerFactory workerFactory
    ) {
        this.workflowClient = workflowClient;
        this.workerFactory = workerFactory;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void runIfNotFound() {
        log.info("starting workflow {}", TranslateCMSWorkflow.WORKFLOW_ID);
        workerFactory.start();
        WorkflowStub untypedStub = workflowClient.newUntypedWorkflowStub(TranslateCMSWorkflow.WORKFLOW_ID);
        try {
            WorkflowExecutionDescription describe = untypedStub.describe();
            WorkflowExecutionStatus status = describe.getWorkflowExecutionInfo().getStatus();
            if (status == WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_RUNNING) {
                log.info("found running workflow ({}). skipping workflow creation", TranslateCMSWorkflow.WORKFLOW_ID);
            } else {
                log.info("workflow ({}) exists but is not running (status: {}). launching a new instance",
                        TranslateCMSWorkflow.WORKFLOW_ID, status);
                run();
            }
        } catch (Exception e) {
            // Workflow doesn't exist, start a new one
            log.info("workflow ({}) not found. launching a new instance", TranslateCMSWorkflow.WORKFLOW_ID);
            run();
        }
    }

    private void run() {
        log.info("workflow ({}) is not running. launching a new instance", TranslateCMSWorkflow.WORKFLOW_ID);
        TranslateCMSWorkflow newWorkflowStub = workflowClient.newWorkflowStub(
                TranslateCMSWorkflow.class,
                WorkflowOptions.newBuilder()
                        .setWorkflowId(TranslateCMSWorkflow.WORKFLOW_ID)
                        .setTaskQueue(Common.QUEUE)
                        .build()
        );

        try {
            WorkflowClient.start(newWorkflowStub::syncUntranslatedEntities);
        } catch (RuntimeException e) {
            log.error("failed to launch workflow ({})", TranslateCMSWorkflow.WORKFLOW_ID);
            System.exit(1);
        }
    }
}
