package com.realmond.temporal_service.crawler.uae.aro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Represents a project summary from the projects list API.
 */
@Data
public class ProjectSummary {
    private Integer id;
    private String title;
    private String slug;
    @JsonProperty("price_from")
    private Price priceFrom;
    private Developer developer;
    private List<String> images;
    @JsonProperty("is_favorite")
    private Boolean isFavorite;
    @JsonProperty("handover_date")
    private ZonedDateTime handoverDate;
    private Object center; // Coordinates or null
    @JsonProperty("in_folder")
    private Boolean inFolder;
    @JsonProperty("pricing_category")
    private String pricingCategory;
    private Integer rating;
} 