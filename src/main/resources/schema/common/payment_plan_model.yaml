---
"$schema": http://json-schema.org/draft-07/schema#
title: payment plan
type: object
definitions:
  payment_step:
    type: object
    properties:
      name:
        type: string
      frequency:
        type: string
        enum:
          - single
          - weekly
          - biweekly
          - monthly
          - bimonthly
          - quarterly
      stage:
        type: string
        enum:
          - on_booking
          - on_signing_spa
          - post_booking
          - on_handover
          - post_handover
      percentage:
        type: number
        minimum: 0
        maximum: 100
      description:
        type: string
    required:
      - name
      - percentage
      - frequency
properties:
  name:
    type: string
    description: The name of the payment plan.
  project_urn:
    type: string
    description: unique project urn the payment plan is applicable to
  payment_steps:
    type: array
    items:
      $ref: "#/definitions/payment_step"
required:
  - name
  - project_urn
