package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.client.PropertyFinderClient;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ProjectRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.ProjectRepository;
import io.temporal.activity.Activity;
import io.temporal.spring.boot.ActivityImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of PropertyFinderParserActivity.
 * Uses existing PropertyFinder parser components to fetch and parse project data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ActivityImpl(taskQueues = "property-parsing-task-queue")
public class PropertyFinderParserActivityImpl implements PropertyFinderParserActivity {

    private final SharedURLParser sharedURLParser;
    private final ProjectDetailParser projectDetailParser;
    private final PropertyFinderClient propertyFinderClient;
    private final ProjectRepository projectRepository;

    @Override
    public List<String> fetchAllPages() {
        log.info("Fetching all project URLs from PropertyFinder");
        List<String> allShareUrls = new ArrayList<>();

        try {
            int pageNumber = 1;
            boolean hasMorePages = true;

            while (hasMorePages) {
                // Report heartbeat to Temporal
                Activity.getExecutionContext().heartbeat("Fetching PropertyFinder page " + pageNumber);

                // Fetch the HTML content for the current page
                String htmlContent = propertyFinderClient.fetchProjectsList(pageNumber);

                // Parse share URLs from the HTML content
                List<String> shareUrls = sharedURLParser.parseShareUrls(htmlContent);

                if (shareUrls.isEmpty()) {
                    log.info("No more projects found on page {}. Stopping pagination.", pageNumber);
                    hasMorePages = false;
                } else {
                    log.info("Found {} project URLs on page {}", shareUrls.size(), pageNumber);
                    allShareUrls.addAll(shareUrls);
                    pageNumber++;
                }
            }

            log.info("Total project URLs fetched from PropertyFinder: {}", allShareUrls.size());
        } catch (Exception e) {
            log.error("Error fetching project URLs from PropertyFinder: {}", e.getMessage(), e);
        }

        return allShareUrls;
    }

    @Override
    public List<ProjectModel> parsePage(String shareUrl) {
        log.info("Parsing PropertyFinder project with URL: {}", shareUrl);
        List<ProjectModel> result = new ArrayList<>();

        try {
            // Report heartbeat to Temporal
            Activity.getExecutionContext().heartbeat("Parsing PropertyFinder project " + shareUrl);

            // Fetch the project HTML content
            String htmlContent = propertyFinderClient.fetchProjectDetail(shareUrl);

            // Parse the project details
            Optional<ProjectModel> projectModel = projectDetailParser.parseProjectDetails(htmlContent, shareUrl);

            if (projectModel.isPresent()) {
                ProjectModel project = projectModel.get();
                result.add(project);

                // Save to repository
                ProjectRecord record = new ProjectRecord();
                record.setUrn(project.getProjectUrn());
                record.setData(project);
                record.setEmittedAt(ZonedDateTime.now());
                projectRepository.save(record);

                log.info("Successfully parsed and saved PropertyFinder project: {}", shareUrl);
            } else {
                log.warn("Failed to parse PropertyFinder project: {}", shareUrl);
            }
        } catch (Exception e) {
            log.error("Error parsing PropertyFinder project {}: {}", shareUrl, e.getMessage(), e);
        }

        return result;
    }

    @Override
    public List<ProjectModel> parseAllPages() {
        log.info("parseAllPages called for PropertyFinder - using pagination instead");
        // For PropertyFinder, we use the pagination approach instead of parsing all at once
        // This method is implemented for interface compatibility
        return Collections.emptyList();
    }
}
