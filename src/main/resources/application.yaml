# ===================================================================
# Damac Properties Crawler Configuration
# ===================================================================
#
# PROFILE CONFIGURATION:
# To use local browser mode instead of Browserless Docker container:
#   - Run with: java -jar app.jar --spring.profiles.active=local
#   - Or set environment variable: SPRING_PROFILES_ACTIVE=local
#
# DEBUG MODE:
#   - Add the 'debug' parameter: --damacproperties.browserless.debug=true
#   - This enables trace recording, screenshots on errors, and verbose logging
#
# This local profile will automatically download and use a local browser instance
# through Playwright instead of connecting to a Browserless Docker container.

# Server configuration
server:
  port: ${SERVER_PORT:8080}  # Use standard port for local development

# Strapi API Configuration
strapi:
  api:
    url: http://localhost:1337/api
    token: dd7f6a7278f62cb210abd95e64269fc2c3d0349a8e133a1ce3d7bc98e2c56392a8696e9fdec45cbaeb4fd43fb6b45a695051eb6c084661e21898153e5ef0c5c546d0d802fe64e5763e3269b600651dd1972357108d1f1b6d4227f38e0ed069d15339135cbca52fb977f7e78e3bea2fdc997b6399cdbbe865edaa04629b1042c7
spring:
  datasource:
    url: *****************************************
    driverClassName: org.postgresql.Driver
    username: temporal
    password: temporal
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update  # Changed to update to preserve data between restarts
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        default_schema: public  # Use public schema instead of strapi
  # Add profiles configuration
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:default}
  temporal:
    connection:
      target: "localhost:7233"
    namespace: default
    workers:
      - task-queue: workflow-worker-queue
        virtual-threads:
          using-virtual-threads: true
          using-virtual-threads-on-activity-worker: true
          using-virtual-threads-on-local-activity-worker: true
          using-virtual-threads-on-nexus-worker: true
          using-virtual-threads-on-workflow-worker: true
        rate-limits:
          max-task-queue-activities-per-second: 100
          max-worker-activities-per-second: 5
        capacity:
          max-concurrent-activity-executors: 5
          max-concurrent-activity-task-pollers: 5
          max-concurrent-local-activity-executors: 5
          max-concurrent-nexus-task-executors: 5
          max-concurrent-nexus-task-pollers: 5
          max-concurrent-workflow-task-executors: 5
          max-concurrent-workflow-task-pollers: 5
      - task-queue: property-parsing-task-queue
        virtual-threads:
          using-virtual-threads: true
          using-virtual-threads-on-activity-worker: true
          using-virtual-threads-on-local-activity-worker: true
          using-virtual-threads-on-nexus-worker: true
          using-virtual-threads-on-workflow-worker: true
        rate-limits:
          max-task-queue-activities-per-second: 100
          max-worker-activities-per-second: 5
        capacity:
          max-concurrent-activity-executors: 5
          max-concurrent-activity-task-pollers: 5
          max-concurrent-local-activity-executors: 5
          max-concurrent-nexus-task-executors: 5
          max-concurrent-nexus-task-pollers: 5
          max-concurrent-workflow-task-executors: 5
          max-concurrent-workflow-task-pollers: 5
      - task-queue: damac-parsing-task-queue
        virtual-threads:
          using-virtual-threads: true
          using-virtual-threads-on-activity-worker: true
          using-virtual-threads-on-local-activity-worker: true
          using-virtual-threads-on-nexus-worker: true
          using-virtual-threads-on-workflow-worker: true
        rate-limits:
          max-task-queue-activities-per-second: 100
          max-worker-activities-per-second: 5
        capacity:
          max-concurrent-activity-executors: 5
          max-concurrent-activity-task-pollers: 5
          max-concurrent-local-activity-executors: 5
          max-concurrent-nexus-task-executors: 5
          max-concurrent-nexus-task-pollers: 5
          max-concurrent-workflow-task-executors: 5
          max-concurrent-workflow-task-pollers: 5
    workflow-cache:
      max-instances: 10
      max-threads: 1
      using-virtual-workflow-threads: true
    workers-auto-discovery:
      packages: com.realmond.temporal_service
    start-workers: true # Enable Temporal workers for local development

translation:
  source-locale: en
  target-locales:
    - ru
    - ar
  page-size: 1000
  sync-cycle-sleep-minutes: 45
  reactive-concurrency-level: 16
  reactive-batch-size: 100
  content-type-strategies:

redis:
  cache:
    host: localhost
    port: 6379  # Updated to match the Redis port in docker-compose
    # password: optional-password
    database: 0
    key-prefix: "blacklist:"
    expiration-hours: 24

management:
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /api
  endpoint:
    health:
      show-details: always

# Damac Properties Crawler Configuration
damacproperties:
  crawler:
    username: <EMAIL>
    password: Privet:123
    page-wait-time: 5
    cron: "0 0 */12 * * *"
    max-pages: 20
  browserless:
    url: ${BROWSERLESS_URL:ws://localhost:3000}
    token: ${BROWSERLESS_TOKEN:}
    timeout: 120000
    stealth-mode: true
    user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    # Connection and recovery settings
    reconnect-tries: 3
    reconnect-delay: 5000
    page-timeout: 60000
    keepalive-interval: 15000
    # Additional stability settings
    navigation-timeout: 45000
    websocket-keep-alive: true
    max-concurrent-pages: 1
    # Browser mode - determines whether to use local browser or browserless
    mode: remote
    # Debug settings
    debug: ${PLAYWRIGHT_DEBUG:false}
    debug-port: 9222
    debug-logs-dir: ${user.home}/.crawler/debug-logs
    trace-dir: ${user.home}/.crawler/traces
    screenshot-on-error: true
    har-capture: false

# Property Parser Configuration
property:
  parser:
    cron: "0 0 0 * * *"  # Run daily at midnight

# Damac Parser Configuration
damac:
  parser:
    cron: "0 */10 * * * *"  # Run every 10 minutes

# PropertyFinder Configuration
propertyfinder:
  base-url: https://www.propertyfinder.ae
  max-retries: 3
  retry-delay-ms: 2000

# ARO.ae Configuration
aro:
  base-url: https://aro.ae
  max-retries: 3
  retry-delay-ms: 2000


# Logging
logging:
  level:
    com:
      realmond:
        temporal_service:
          crawler:
            damacproperties: DEBUG
          parser: DEBUG
    root: INFO
  file:
    name: logs/application.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# S3 Storage Configuration for Supabase
storage:
  s3:
    endpoint: https://dcgthzyiarwgjjxpjlog.supabase.co
    region: ${S3_REGION:eu-central-1}
    bucket: ${S3_BUCKET:realmond}
    access-key: ${S3_ACCESS_KEY:e16071ae569ae7a0b580ef2980a612a3}
    secret-key: ${S3_SECRET_KEY:c0c5ad2fc2044c4739a6e75f88991b9a10e5d934fc0196e235ea96472c91e021}
    base-path: ${S3_BASE_PATH:damac-properties}
    enabled: true

---
# Local browser profile configuration
spring:
  config:
    activate:
      on-profile: local

damacproperties:
  browserless:
    # Override the main config to use local browser instead of Browserless Docker container
    mode: local
    # Local browser settings
    headless: true  # Set to false for debugging to see the browser in action
    # Enhanced debugging when using local mode
    debug-logs-dir: ./debug-logs
    trace-dir: ./traces
