---
"$schema": http://json-schema.org/draft-07/schema#
title: floor plan
type: object
properties:
  floorplan_urn:
    type: string
    description: A unique identifier for the floor plan.
  area:
    $ref: area_model.yaml
  bedrooms:
    type: number
    description: The number of bedrooms in the floor plan.
    minimum: 0
  building_urn:
    type: string
    description: A unique identifier for the building.
  project_urn:
    type: string
    description: A unique identifier for the project.
  image:
    $ref: image_model.yaml
  level_images:
    type: array
    items:
      type: object
      properties:
        level:
          type: string
          description: level can be relative (e.g. in G, P, B) so we use string for now
        image:
          $ref: image_model.yaml
  title:
    type: string
    description: A title of the floor plan.
  levels:
    type: integer
    description: number of levels in the unit
  property_type:
    $ref: property_type_model.yaml
  source_urn:
    type: string
  external_id:
    type: string
required:
  - external_id
  - source_urn
  - floorplan_urn
  - project_urn
  - bedrooms
  - image
