secretsprovider: awskms:///arn:aws:kms:eu-west-1:766763938229:key/186c60a3-c75d-4988-a498-94d5a65b7a2e?region=eu-west-1&awssdk=v2
encryptedkey: AQICAHiM0QldSb4B6YRgA7PAExyH6ls+d1DPHfsGezPTEKDPKAHu+g1SruOLR0iNVC9uycAbAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMcyhKCq1Vwv1djTp5AgEQgDsEoK6Qg5RhDkpTiUu9WRqqb4l+RT3/1OCHT9LGgmY+HdMIA2SN3qDw/UK3yE+eOtgnxhtKqB85MkdFiQ==
config:
  workflow-worker:config:
    image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond-app/workflow-worker
    imageVersion: latest
    namespace: workflow
    containerEnv:
      STRAPI_API_TOKEN:
        secure: v1:iDgQCq6bXnn9gzIR:ukG0DcTXk/14E7Rhd88HUzI7lGuCpQpeKXGOCxqbGqv3gKGBFYSroRWLLHpEF6zev6qgsbOmhbTrdnbD44snp5zuvYvooH9VoJJMxJDhhObiLjBFUrCWBHo+Y4Mw+EQREG7fWIfIJK9mcqq8ohe1oj+OCOnIDE5zLGJqROEGJg3LBIc7sb40nYsesuZ767rhqQUoBBC50dukSsfeoP0ew7dbNM4+vdFCTUx3oM4GcqiGmpjZ8Lkbi3tBjwd44iTyWGfs9oBHk1yEMueH4s12z/gVEjOh38x66KqGT4BzTdSNyPS9bITQd05TuX+9tPdamRxw5RfRiVg16SYEX+EWnw3C1OoAy85ID56sALC4SUU=
      REDIS_CACHE_PASSWORD:
        secure: v1:o7NWqDl0scYFT8Uj:9wPYyirESMSnieCfmYajmRMVkdXO+l7pxt9WeG0UzKiVMbNbtUwqDU/jf3XiZEPNf5dL3OGte0Mfl7meuNeqEFTbraznMzPXjG9CceWA/kA=
    applicationYaml:
      spring:
        temporal:
          connection:
            target: temporal-helm-chart-8657e95f-frontend.temporal.svc.cluster.local:7233
      strapi:
        api:
          url: http://cms-ctrl-intra.strapi-ctrl.svc.cluster.local:1337/api
      redis:
        cache:
          host: web-redis-master.web-redis.svc.cluster.local
          port: 6379
          database: 1
          key-prefix: "cms-translate-wf:entity-blacklist:"
          expiration-hours: 24
  workflow-worker:kubeconfig:
    secure: v1:tREr2xo4OzLNiZQ6: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
