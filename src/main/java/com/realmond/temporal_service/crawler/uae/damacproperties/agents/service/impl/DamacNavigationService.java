package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.Page;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.options.LoadState;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.BrowserManager;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.NavigationService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of NavigationService for Damac Properties using Playwright
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DamacNavigationService implements NavigationService {

    private final BrowserManager browserManager;
    private static final String BASE_URL = "https://agents.damacproperties.com/AP_UnitSearch?langCode=en_US";

    @Override
    public void configureFilters(Page page, int waitTimeSeconds) {
        log.debug("Configuring filters for property search");

        try {
            // Navigate to the base URL if needed
            browserManager.navigateTo(page, BASE_URL);

            // Wait for the page to load
            browserManager.waitForLoadState(page, LoadState.NETWORKIDLE, waitTimeSeconds * 1000);

            // Wait for the unit search panel to appear
            waitForUnitSearchPanel(page, waitTimeSeconds);

            log.debug("Unit search panel found, proceeding with filter configuration");

            // Handle popups/cookie banners
            handlePopups(page);

            try {
                // Set pagination to 100 items per page (matching Python implementation)
                log.debug("Setting pagination to 100 items per page");
                if (browserManager.elementExists(page, "#page\\:form\\:countpanel > div > button", 5000)) {
                    browserManager.click(page, "#page\\:form\\:countpanel > div > button");
                    browserManager.sleep(1000);

                    if (browserManager.elementExists(page, "#page\\:form\\:countpanel > div > div > ul > li:nth-child(10) > a", 3000)) {
                        browserManager.click(page, "#page\\:form\\:countpanel > div > div > ul > li:nth-child(10) > a");
                        log.info("Set pagination to 100 items per page");
                    } else {
                        log.warn("Could not find the 100 items option in pagination dropdown");
                    }
                } else {
                    log.warn("Could not find pagination dropdown button");
                }

                browserManager.sleep(waitTimeSeconds * 1000);  // Wait for changes to apply

                // Sort by project (matching Python implementation)
                log.debug("Setting sort order to sort by project");
                if (browserManager.elementExists(page, "div.filterDropBox > div.btn.filterDropdown > button", 5000)) {
                    browserManager.click(page, "div.filterDropBox > div.btn.filterDropdown > button");
                    browserManager.sleep(1000);

                    if (browserManager.elementExists(page, "div.filterDropBox > div.btn.filterDropdown > div > ul > li:nth-child(3) > a", 3000)) {
                        browserManager.click(page, "div.filterDropBox > div.btn.filterDropdown > div > ul > li:nth-child(3) > a");
                        log.info("Set sort order to 'by project'");
                    } else {
                        log.warn("Could not find the 'by project' option in sort dropdown");
                    }
                } else {
                    log.warn("Could not find sort dropdown button");
                }

                // Apply filters by clicking the apply button (matching Python implementation)
                log.debug("Applying filters");
                if (browserManager.elementExists(page, "div.unitSearchFilterFooter > button:nth-child(2)", 5000)) {
                    browserManager.click(page, "div.unitSearchFilterFooter > button:nth-child(2)");
                    log.info("Applied filters successfully");
                } else {
                    log.warn("Could not find the apply button");
                }

                // Wait for filters to be applied and page to reload
                browserManager.waitForLoadState(page, LoadState.NETWORKIDLE, waitTimeSeconds * 1000);
                browserManager.sleep(2000);  // Extra wait to ensure everything has loaded
            } catch (Exception e) {
                log.warn("Error while configuring pagination and sorting: {}", e.getMessage());
                // Continue with default settings
            }

            // Check if property listings appear after applying filters
            log.debug("Checking for property listings after applying filters");
            boolean listingsFound = false;

            // Try both selectors for property listings (combining Python and Java approaches)
            String[] tableSelectors = {
                "table#projectSearchResultId > tbody > tr",  // Python selector
                ".unitSearchResultsTable tbody tr",          // Java selector
                "table.unitSearchResultsTable > tbody > tr", // Another variation
                "#projectSearchResultId"                     // Just the table ID
            };

            for (String selector : tableSelectors) {
                log.debug("Trying to find property listings with selector: {}", selector);
                if (browserManager.elementExists(page, selector, 3000)) {
                    log.info("Found property listings with selector: {}", selector);
                    listingsFound = true;

                    // Take a screenshot of the found listings for debugging
                    try {
                        String screenshotPath = "property-listings-found-" + System.currentTimeMillis() + ".png";
                        page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(screenshotPath)).setFullPage(true));
                        log.debug("Property listings screenshot saved to {}", screenshotPath);
                    } catch (Exception e) {
                        log.warn("Failed to take screenshot of property listings: {}", e.getMessage());
                    }

                    break;
                }
            }

            if (!listingsFound) {
                // Take a screenshot for debugging
                String screenshotPath = "no-listings-found-" + System.currentTimeMillis() + ".png";
                page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(screenshotPath)).setFullPage(true));
                log.warn("No property listings found with any selector. Screenshot saved to {}", screenshotPath);

                // Try refreshing the page
                log.info("Refreshing the page and trying again");
                browserManager.navigateTo(page, page.url());
                browserManager.waitForLoadState(page, LoadState.NETWORKIDLE, waitTimeSeconds * 1000);
                browserManager.sleep(5000);

                // Handle popups again after refresh
                handlePopups(page);

                // Check again after refresh
                for (String selector : tableSelectors) {
                    log.debug("Trying to find property listings after refresh with selector: {}", selector);
                    if (browserManager.elementExists(page, selector, 3000)) {
                        log.info("Found property listings after refresh with selector: {}", selector);
                        listingsFound = true;
                        break;
                    }
                }

                if (!listingsFound) {
                    log.warn("Still no property listings found after refresh");
                    // Continue without throwing exception to allow for further attempts
                }
            }

            log.info("Filter configuration completed");
        } catch (Exception e) {
            log.error("Error configuring filters: {}", e.getMessage());
            String errorScreenshot = "filter-config-error-" + System.currentTimeMillis() + ".png";
            page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(errorScreenshot)));
            log.error("Screenshot saved to {}", errorScreenshot);
            throw new CrawlerException("Failed to configure filters", e);
        }
    }

    @Override
    public boolean goToNextPage(Page page, int waitTimeSeconds) {
        log.debug("Checking for next page button...");
        String nextButtonSelector = "button.btn.btn-next";
        ElementHandle nextButton = page.querySelector(nextButtonSelector);

        if (nextButton == null) {
            log.info("Next page button not found. Assuming end of pagination.");
            return false;
        }

        // Check if enabled *before* potentially lengthy data extraction
        Boolean isEnabled = (Boolean) page.evaluate("button => !button.disabled", nextButton);
        if (isEnabled == null || !isEnabled) {
            log.info("Next page button found but is disabled. Assuming end of pagination.");
            return false;
        }

        // --- Store state BEFORE clicking ---
        String firstRowTextBefore = "";
        String currentPageTextBefore = ""; // Still useful for logging
        try {
            ElementHandle firstRow = page.querySelector("table#projectSearchResultId > tbody > tr:first-child");
            if (firstRow != null) {
                firstRowTextBefore = firstRow.textContent().trim();
                // Log only a snippet to avoid huge log lines
                log.debug("First row text before click (first 100 chars): {}",
                    firstRowTextBefore.substring(0, Math.min(firstRowTextBefore.length(), 100)).replaceAll("\\\\s+", " "));
            } else {
                log.warn("Could not find first row before click to store state.");
            }
            ElementHandle activePageElement = page.querySelector("li.active");
            if (activePageElement != null) {
                currentPageTextBefore = activePageElement.textContent().trim();
                log.debug("Current page indicator before click: {}", currentPageTextBefore);
            }
        } catch (Exception e) {
            log.warn("Error storing state before click: {}", e.getMessage());
            // Non-fatal, continue but verification might be less accurate
        }

        // --- Click and Wait ---
        try {
            log.debug("Scrolling next button into view...");
            // Using scrollIntoViewIfNeeded often works better than JS scrollIntoView
            nextButton.scrollIntoViewIfNeeded();
            page.waitForTimeout(500); // Short wait for scroll animation

            log.debug("Clicking next page button...");
            nextButton.click();

            log.debug("Waiting for network idle after click...");
            // Wait for network requests triggered by the click to complete
            page.waitForLoadState(LoadState.NETWORKIDLE, new Page.WaitForLoadStateOptions().setTimeout(waitTimeSeconds * 2000));

            // Add an explicit wait for potential JS rendering after network idle
            log.debug("Adding extra wait ({}ms) for rendering...", 1500);
            page.waitForTimeout(1500); // Adjust if necessary

        } catch (Exception e) {
            log.error("Error during click or wait: {}. Attempting verification anyway.", e.getMessage(), e);
            // If click failed, verification will likely fail, but let's try
        }

        // --- Verify state AFTER clicking ---
        log.debug("Verifying page state after click and wait...");
        try {
            // 1. Primary Check: Wait for the first row content to *actually* change
            if (!firstRowTextBefore.isEmpty()) {
                log.info("Waiting for first row content to change from its previous state...");
                String waitForContentChangeScript = String.format(
                    "(selector, oldText) => { " +
                    "  const element = document.querySelector(selector); " +
                    // Check if element exists and its trimmed text is different from oldText
                    "  return element && element.textContent.trim() !== oldText; " +
                    "}",
                    "table#projectSearchResultId > tbody > tr:first-child", // Escaping not needed here
                    firstRowTextBefore.replace("'", "\\'") // Escape single quotes in oldText for JS string
                                       .replace("\\", "\\\\") // Escape backslashes
                );

                try {
                    // Wait up to waitTimeSeconds (e.g., 10 seconds) for the content to differ
                    page.waitForFunction(waitForContentChangeScript,
                                        "table#projectSearchResultId > tbody > tr:first-child", // Pass selector as arg
                                        new Page.WaitForFunctionOptions().setTimeout(waitTimeSeconds * 1500) // Extend timeout slightly
                                    );
                    log.info("First row content successfully changed. Page navigation successful.");
                    logOptionalPageNumberChange(page, currentPageTextBefore);
                    return true; // Content changed!

                } catch (com.microsoft.playwright.TimeoutError timeoutError) {
                    // Content did NOT change within the timeout
                    log.warn("First row content did NOT change within the timeout ({}ms).", waitTimeSeconds * 1500);
                    // Proceed to check secondary conditions (button state, no-data message)
                } catch (Exception waitEx) {
                    log.error("Error during waitForFunction for content change: {}. Proceeding to secondary checks.", waitEx.getMessage());
                    // Proceed to check secondary conditions
                }
            } else {
                // We couldn't get the 'before' state, so we can't reliably wait for change.
                log.warn("Cannot wait for content change because before state was empty. Proceeding directly to secondary checks.");
                // Add a small fixed wait just in case, before secondary checks
                 page.waitForTimeout(1000);
            }

            // If waitForFunction timed out or couldn't run, check other end conditions:
            // 2. Secondary Check: Is the next button now disabled/gone?
            log.debug("Content unchanged or comparison failed/skipped, checking next button state...");
            ElementHandle nextButtonAfter = page.querySelector(nextButtonSelector); // Re-query the button
            if (nextButtonAfter == null) {
                log.info("Next page button is GONE. Assuming end of pagination.");
                return false;
            }
            Boolean isEnabledAfter = (Boolean) page.evaluate("button => !button.disabled", nextButtonAfter);
            if (isEnabledAfter != null && !isEnabledAfter) {
                log.info("Next page button is now DISABLED. Assuming end of pagination.");
                return false;
            } else if (isEnabledAfter == null){
                 log.warn("Could not determine if next button is enabled after click.");
            } else {
                 log.warn("Next button still present and appears enabled after content check timeout.");
            }

            // 3. Final Check: Look for 'no data' message
            boolean noDataDisplayed = page.querySelector(".no-data") != null;
            if (noDataDisplayed) {
                log.info("No data message found after checking button state. End of pagination.");
                return false;
            }

            // If we reach here: waitForFunction timed out/failed, button still enabled/present, no 'no data' msg
            log.error("goToNextPage failed verification: Content did not change within timeout, button still enabled, and no 'no-data' message found.");
            captureScreenshot(page, "error-verification-failed-timeout");
            return false; // Failed to confirm page change or end condition

        } catch (Exception verificationEx) {
            log.error("Unexpected error during page state verification: {}", verificationEx.getMessage(), verificationEx);
            captureScreenshot(page, "error-verification-exception");
            return false; // Verification failed critically
        }
    }

    // Helper to log page number change without impacting success logic
    private void logOptionalPageNumberChange(Page page, String currentPageTextBefore) {
         try {
             ElementHandle activePageAfter = page.querySelector("li.active");
             if(activePageAfter != null) {
                 String currentPageTextAfter = activePageAfter.textContent().trim();
                 if (!currentPageTextBefore.isEmpty() && !currentPageTextBefore.equals(currentPageTextAfter)) {
                     log.debug("Page indicator also changed from '{}' to '{}'", currentPageTextBefore, currentPageTextAfter);
                 } else if (!currentPageTextBefore.isEmpty()) {
                     log.warn("Page indicator ('li.active') did NOT change from '{}', despite content change.", currentPageTextBefore);
                 } else {
                     log.debug("New page indicator is '{}'", currentPageTextAfter);
                 }
             } else {
                 log.warn("Could not find page indicator ('li.active') after successful content change.");
             }
         } catch (Exception e) {
              log.debug("Could not log page number change: {}", e.getMessage());
         }
    }

    // Helper to capture screenshot with context
    private void captureScreenshot(Page page, String context) {
         String screenshotPath = String.format("%s-%d.png", context, System.currentTimeMillis());
         try {
             page.screenshot(new Page.ScreenshotOptions()
                 .setPath(java.nio.file.Paths.get(screenshotPath))
                 .setFullPage(true)); // Capture full page for better context
             log.error("Screenshot saved for context '{}': {}", context, screenshotPath);
         } catch(Exception ssEx) {
             log.error("Failed to save screenshot for context '{}': {}", context, ssEx.getMessage());
         }
    }

    /**
     * Helper method to handle popups like cookie consent
     */
    private void handlePopups(Page page) {
        log.debug("Checking for popups to handle");

        // Check for cookie consent
        String[] cookieSelectors = {
            "button[id*='cookie']",
            "button[id*='accept']",
            ".cookie-banner button",
            "[data-testid='cookie-accept']",
            ".cookie-consent button",
            "#onetrust-accept-btn-handler",
            ".accept-cookies",
            ".gdpr-banner button"
        };

        for (String selector : cookieSelectors) {
            if (browserManager.elementExists(page, selector, 1000)) {
                log.info("Found cookie consent with selector: {}", selector);
                try {
                    browserManager.click(page, selector);
                    log.info("Clicked cookie consent button");
                    browserManager.sleep(2000);
                    break;
                } catch (Exception e) {
                    log.warn("Failed to click cookie consent button: {}", e.getMessage());
                }
            }
        }

        // Check for other common popups
        String[] popupSelectors = {
            ".modal .close",
            ".popup .close",
            ".modal-close",
            "[data-testid='close-modal']",
            ".newsletter-popup .close",
            ".overlay .close-button"
        };

        for (String selector : popupSelectors) {
            if (browserManager.elementExists(page, selector, 1000)) {
                log.info("Found popup with selector: {}", selector);
                try {
                    browserManager.click(page, selector);
                    log.info("Clicked popup close button");
                    browserManager.sleep(1000);
                } catch (Exception e) {
                    log.warn("Failed to close popup: {}", e.getMessage());
                }
            }
        }
    }

    private void waitForUnitSearchPanel(Page page, int waitTimeSeconds) {
        log.debug("Waiting for unit search panel to appear");

        // Try multiple selectors for the unit search panel, starting with the original one
        String[] unitSearchPanelSelectors = {
            "#unitSearchPanel",
            ".unitSearchPanel",
            "div[id*='unitSearchPanel']",
            "[id*='unitSearchPanel']",
            "[class*='unitSearchPanel']"
        };

        boolean found = false;
        for (String selector : unitSearchPanelSelectors) {
            try {
                log.debug("Trying selector: {}", selector);
                browserManager.waitForSelector(page, selector, waitTimeSeconds * 1000);
                log.info("Found unit search panel with selector: {}", selector);
                found = true;
                break;
            } catch (Exception e) {
                log.debug("Selector {} not found: {}", selector, e.getMessage());
            }
        }

        if (!found) {
            log.error("Unit search panel not found after trying multiple selectors");
            String errorScreenshot = "unit-search-panel-error-" + System.currentTimeMillis() + ".png";
            page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(errorScreenshot)));
            log.error("Screenshot saved to {}", errorScreenshot);
            throw new CrawlerException("Element not found: #unitSearchPanel");
        }
    }
}

