package com.realmond.temporal_service.crawler.uae.aro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Represents unit statistics grouped by bedroom count.
 */
@Data
public class UnitStats {
    private Integer bedrooms;
    @JsonProperty("price_from")
    private Price priceFrom;
    @JsonProperty("size_from")
    private SizeInfo sizeFrom;
    private Integer count;

    @Data
    public static class SizeInfo {
        private String measurement;
        private Double value;
    }
} 