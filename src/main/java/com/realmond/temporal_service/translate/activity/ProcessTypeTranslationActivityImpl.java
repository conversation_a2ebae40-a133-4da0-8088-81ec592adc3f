package com.realmond.temporal_service.translate.activity;

import com.realmond.temporal_service.config.TranslationConfigProperties;
import com.realmond.temporal_service.translate.dto.UnTranslatedEntities;
import com.realmond.temporal_service.translate.activity.strategy.TranslateStrategy;
import com.realmond.temporal_service.translate.activity.strategy.TranslateStrategy.TranslationResult;
import com.realmond.temporal_service.translate.activity.strategy.TranslateStrategyFactory;
import com.realmond.temporal_service.translate.svc.TranslateService;
import io.temporal.activity.Activity;
import io.temporal.spring.boot.ActivityImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ActivityImpl(taskQueues = "translation-task-queue")
public class ProcessTypeTranslationActivityImpl implements ProcessTypeTranslationActivity {

    private final TranslateService translateService;
    private final String sourceLocale;
    private final TranslationConfigProperties config;
    private final TranslateStrategyFactory strategyFactory;

    @Autowired
    public ProcessTypeTranslationActivityImpl(
            TranslateService translateService,
            TranslationConfigProperties config,
            TranslateStrategyFactory strategyFactory) {
        this.translateService = translateService;
        this.sourceLocale = config.getSourceLocale();
        this.config = config;
        this.strategyFactory = strategyFactory;
    }

    @Override
    public int processTypeTranslation(String contentType, String targetLocale, int pageSize) {
        int page = 1;
        int totalProcessed = 0;

        // Get the appropriate strategy for this content type
        String strategyName = config.getStrategyForContentType(contentType);
        TranslateStrategy strategy = strategyFactory.getStrategy(strategyName);
        log.info("Using {} strategy for content type: {}", strategyName, contentType);

        while (true) {
            Activity.getExecutionContext().heartbeat("Processing page " + page);

            UnTranslatedEntities response = translateService.getUntranslatedEntityIds(
                    contentType, targetLocale, 1, pageSize);
            List<Long> entityIds = response.getData();
            log.info("Content type {} page {} returned {} untranslated IDs.", contentType, page, entityIds.size());

            if (entityIds.isEmpty()) {
                log.info("Received 0 entityIds");
                break;
            }

            // Process entities using the selected strategy
            TranslationResult result = strategy.processEntities(
                    entityIds, contentType, sourceLocale, targetLocale);

            totalProcessed += result.getSuccessCount();

            // Log errors if any
            Map<Long, String> errors = result.getErrors();
            if (!errors.isEmpty()) {
                log.error("Page {} had {} errors out of {} entities for content type {}",
                        page, errors.size(), entityIds.size(), contentType);
            }

            // Check if we've processed all available entities
            if (entityIds.size() < pageSize) {
                log.info("Count of returned entities ({}) is less than pageSize({}). Completing processing",
                        entityIds.size(), pageSize);
                break;
            }
            page++;
        }
        return totalProcessed;
    }
}

