package com.realmond.temporal_service.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Service for generating random browser fingerprints to avoid detection when scraping websites.
 * This helps to simulate different browsers and devices for each request.
 */
@Slf4j
@Component
public class FingerprintGenerator {

    private static final Random random = new Random();

    // Common user agents for different browsers and platforms
    private static final List<String> USER_AGENTS = Arrays.asList(
            // Chrome on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            // Chrome on macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            // Firefox on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
            // Firefox on macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0",
            // Safari on macOS
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            // Edge on Windows
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
    );

    // Common viewport sizes for different devices
    private static final List<ViewportSize> VIEWPORT_SIZES = Arrays.asList(
            new ViewportSize(1920, 1080), // Desktop Full HD
            new ViewportSize(1680, 1050), // Desktop
            new ViewportSize(1440, 900),  // Desktop
            new ViewportSize(1366, 768),  // Laptop
            new ViewportSize(1280, 800),  // Laptop
            new ViewportSize(1024, 768)   // Tablet
    );

    // Common languages and locales
    private static final List<String> LANGUAGES = Arrays.asList(
            "en-US,en;q=0.9",
            "en-GB,en;q=0.9",
            "en-CA,en;q=0.9",
            "en-AU,en;q=0.9",
            "ru-RU,ru;q=0.9,en;q=0.8",
            "ar-AE,ar;q=0.9,en;q=0.8",
            "fr-FR,fr;q=0.9,en;q=0.8",
            "de-DE,de;q=0.9,en;q=0.8",
            "es-ES,es;q=0.9,en;q=0.8"
    );

    /**
     * Generates a random browser fingerprint with user agent, viewport size, and other properties.
     * @return A BrowserFingerprint object with randomized properties
     */
    public BrowserFingerprint generateRandomFingerprint() {
        String userAgent = getRandomUserAgent();
        ViewportSize viewportSize = getRandomViewportSize();
        String language = getRandomLanguage();
        String platform = extractPlatformFromUserAgent(userAgent);
        
        BrowserFingerprint fingerprint = new BrowserFingerprint();
        fingerprint.setUserAgent(userAgent);
        fingerprint.setViewportWidth(viewportSize.width);
        fingerprint.setViewportHeight(viewportSize.height);
        fingerprint.setLanguage(language);
        fingerprint.setPlatform(platform);
        fingerprint.setColorDepth(24);
        fingerprint.setPixelRatio(getRandomPixelRatio());
        fingerprint.setTimezoneOffset(getRandomTimezoneOffset());
        fingerprint.setSessionId(UUID.randomUUID().toString());
        
        log.debug("Generated random fingerprint: {}", fingerprint);
        return fingerprint;
    }

    /**
     * Gets a random user agent from the predefined list.
     * @return A random user agent string
     */
    public String getRandomUserAgent() {
        return USER_AGENTS.get(random.nextInt(USER_AGENTS.size()));
    }

    /**
     * Gets a random viewport size from the predefined list.
     * @return A random ViewportSize object
     */
    private ViewportSize getRandomViewportSize() {
        return VIEWPORT_SIZES.get(random.nextInt(VIEWPORT_SIZES.size()));
    }

    /**
     * Gets a random language from the predefined list.
     * @return A random language string
     */
    private String getRandomLanguage() {
        return LANGUAGES.get(random.nextInt(LANGUAGES.size()));
    }

    /**
     * Extracts the platform information from a user agent string.
     * @param userAgent The user agent string
     * @return The platform string (Windows, Macintosh, etc.)
     */
    private String extractPlatformFromUserAgent(String userAgent) {
        if (userAgent.contains("Windows")) {
            return "Windows";
        } else if (userAgent.contains("Macintosh")) {
            return "Macintosh";
        } else if (userAgent.contains("Linux")) {
            return "Linux";
        } else if (userAgent.contains("Android")) {
            return "Android";
        } else if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            return "iOS";
        } else {
            return "Unknown";
        }
    }

    /**
     * Generates a random pixel ratio (1, 1.5, 2, or 2.5).
     * @return A random pixel ratio
     */
    private double getRandomPixelRatio() {
        double[] ratios = {1.0, 1.5, 2.0, 2.5};
        return ratios[random.nextInt(ratios.length)];
    }

    /**
     * Generates a random timezone offset between -12 and +14 hours.
     * @return A random timezone offset in minutes
     */
    private int getRandomTimezoneOffset() {
        // Timezone offsets range from -12 to +14 hours
        return (random.nextInt(27) - 12) * 60;
    }

    /**
     * Generates a random fingerprint and attaches common browser headers to the given Feign
     * {@link feign.RequestTemplate}. This centralises the header logic so that any HTTP
     * client can obtain consistent, realistic browser headers via the shared generator.
     *
     * @param template Feign request template that will receive headers
     * @return The {@link BrowserFingerprint} that was generated and applied
     */
    public BrowserFingerprint applyBrowserHeaders(feign.RequestTemplate template) {
        BrowserFingerprint fingerprint = generateRandomFingerprint();

        template.header("User-Agent", fingerprint.getUserAgent());
        template.header("Accept", "application/json, text/plain, */*");
        template.header("Accept-Language", fingerprint.getLanguage());
        template.header("Accept-Encoding", "gzip, deflate, br");
        template.header("Connection", "keep-alive");
        template.header("Sec-Fetch-Dest", "empty");
        template.header("Sec-Fetch-Mode", "cors");
        template.header("Sec-Fetch-Site", "same-origin");
        template.header("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"");
        template.header("Sec-Ch-Ua-Mobile", "?0");
        template.header("Sec-Ch-Ua-Platform", "\"" + fingerprint.getPlatform() + "\"");

        // Add random cache-control header for variability
        if (random.nextBoolean()) {
            template.header("Cache-Control", "max-age=0");
        } else {
            template.header("Pragma", "no-cache");
            template.header("Cache-Control", "no-cache");
        }

        // Occasionally add a referer
        if (random.nextBoolean()) {
            template.header("Referer", "https://www.google.com/");
        }

        log.debug("Applied fingerprint to request: {}", fingerprint);
        return fingerprint;
    }

    /**
     * Inner class representing a viewport size.
     */
    private static class ViewportSize {
        private final int width;
        private final int height;

        public ViewportSize(int width, int height) {
            this.width = width;
            this.height = height;
        }
    }
}
