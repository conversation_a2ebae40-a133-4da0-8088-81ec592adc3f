package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.LoadState;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.AuthenticationService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.BrowserManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of AuthenticationService for Damac Properties using Playwright
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DamacAuthenticationService implements AuthenticationService {

    private final BrowserManager browserManager;

    // Match the exact Python selectors for consistency
    private static final String USERNAME_SELECTOR = "#ap_login\\:formId\\:username";
    private static final String PASSWORD_SELECTOR = "#ap_login\\:formId\\:password";
    private static final String LOGIN_BUTTON_SELECTOR = "div.loginDiv > div.login-details-leftBlock > button";

    // Adding the base URL which was missing - this is crucial
    private static final String LOGIN_URL = "https://agents.damacproperties.com/AP_UnitSearch?langCode=en_US";

    // Keep the existing selectors as fallback
    private final String[] usernameSelectors = {
        USERNAME_SELECTOR,
        "#username",
        "input[name='username']",
        "input[type='email']",
        "input[placeholder*='email']",
        "input[placeholder*='username']",
        "[data-testid='username-input']"
    };

    private final String[] passwordSelectors = {
        PASSWORD_SELECTOR,
        "#password",
        "input[name='password']",
        "input[type='password']",
        "[data-testid='password-input']"
    };

    private final String[] loginButtonSelectors = {
        LOGIN_BUTTON_SELECTOR,
        "button[type='submit']",
        "input[type='submit']",
        "button.login-button",
        ".login-form button",
        "[data-testid='login-button']",
        "button:has-text('Login')",
        "button:has-text('Sign In')"
    };

    @Override
    public boolean isAuthenticated(Page page) {
        log.debug("Checking if session is authenticated");

        try {
            // Navigate to the login URL to check authentication status
            log.debug("Navigating to login URL to check authentication: {}", LOGIN_URL);

            // Store original URL to return after check
            String originalUrl = page.url();

            // Navigate to the login page
            browserManager.navigateTo(page, LOGIN_URL);
            browserManager.waitForLoadState(page, LoadState.DOMCONTENTLOADED, 10000);
            browserManager.sleep(1000);  // Short pause to let the page settle

            // Check for login elements
            boolean loginFormFound = browserManager.elementExists(page, USERNAME_SELECTOR, 3000) ||
                                    browserManager.elementExists(page, LOGIN_BUTTON_SELECTOR, 3000);

            // Navigate back to original URL if different
            if (!page.url().equals(originalUrl)) {
                log.debug("Returning to original URL: {}", originalUrl);
                browserManager.navigateTo(page, originalUrl);
            }

            if (loginFormFound) {
                log.info("Not authenticated, login form elements found on login page");
                return false;
            } else {
                log.info("Authenticated, no login form elements found on login page");
                return true;
            }
        } catch (Exception e) {
            log.warn("Error checking authentication status: {}", e.getMessage());

            // Take a screenshot to show what's on the page when we have an error
            try {
                String errorScreenshot = "auth-check-error-" + System.currentTimeMillis() + ".png";
                page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(errorScreenshot)));
                log.warn("Authentication check error screenshot saved to: {}", errorScreenshot);
            } catch (Exception se) {
                log.warn("Failed to take screenshot during auth check: {}", se.getMessage());
            }

            // In case of error, assume not authenticated to trigger login attempt
            return false;
        }
    }

    @Override
    public void authenticate(Page page, String username, String password, int pageWaitTimeSeconds) {
        log.info("Attempting to authenticate with provided credentials");

        if (username == null || username.isEmpty() || password == null || password.isEmpty()) {
            log.warn("Username or password is empty, skipping authentication");
            return;
        }

        // Take a screenshot before authentication
        try {
            String beforeAuthScreenshot = "before-auth-" + System.currentTimeMillis() + ".png";
            page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(beforeAuthScreenshot)));
            log.debug("Took screenshot before authentication: {}", beforeAuthScreenshot);
        } catch (Exception e) {
            log.warn("Failed to take pre-auth screenshot: {}", e.getMessage());
        }

        int retryCount = 0;
        int maxRetries = 3;
        Exception lastException = null;

        while (retryCount < maxRetries) {
            try {
                // First, navigate to the login URL - this was missing in the original implementation
                log.info("Navigating to login URL: {}", LOGIN_URL);
                try {
                    browserManager.navigateTo(page, LOGIN_URL);
                } catch (BrowserlessManager.BrowserReconnectedException e) {
                    // Browser was reconnected, update our page reference
                    log.info("Browser reconnected during login navigation, updating page reference");
                    page = e.getNewPage();
                    browserManager.navigateTo(page, LOGIN_URL);
                }

                // Wait for page to load
                browserManager.waitForLoadState(page, LoadState.DOMCONTENTLOADED, pageWaitTimeSeconds * 1000);
                browserManager.sleep(2000); // Short pause after page load

                // Check if we're already logged in
                if (isAuthenticated(page)) {
                    log.info("Already authenticated, skipping login process");
                    return;
                }

                // Wait for the username field to appear (similar to Python's wait_page_for)
                log.debug("Waiting for username field: {}", USERNAME_SELECTOR);
                try {
                    browserManager.waitForSelector(page, USERNAME_SELECTOR, pageWaitTimeSeconds * 1000);
                } catch (Exception e) {
                    // Take a screenshot to show what's on the page if selector isn't found
                    try {
                        String noSelectorScreenshot = "selector-error-" + System.currentTimeMillis() + ".png";
                        page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(noSelectorScreenshot)));
                        log.warn("Username selector not found, screenshot saved to: {}", noSelectorScreenshot);
                    } catch (Exception se) {
                        log.warn("Failed to take screenshot for missing selector: {}", se.getMessage());
                    }

                    // Try alternative selectors
                    log.debug("Trying alternative username selectors");
                    boolean foundAlternative = false;
                    for (String altSelector : usernameSelectors) {
                        if (browserManager.elementExists(page, altSelector, 2000)) {
                            log.info("Found alternative username selector: {}", altSelector);
                            foundAlternative = true;
                            // Continue with this selector
                            browserManager.typeWithDelay(page, altSelector, username);
                            break;
                        }
                    }

                    if (!foundAlternative) {
                        throw new CrawlerException("Could not find any username field on the page");
                    }
                }

                // Only enter username if we didn't already find an alternative above
                if (!USERNAME_SELECTOR.equals(usernameSelectors[0]) || browserManager.elementExists(page, USERNAME_SELECTOR, 1000)) {
                    // Enter username with character-by-character typing (matching Python implementation)
                    log.debug("Entering username using typeWithDelay");
                    browserManager.typeWithDelay(page, USERNAME_SELECTOR, username);
                }

                // Enter password with character-by-character typing
                for (String passwordSelector : passwordSelectors) {
                    if (browserManager.elementExists(page, passwordSelector, 2000)) {
                        log.debug("Entering password using typeWithDelay on selector: {}", passwordSelector);
                        browserManager.typeWithDelay(page, passwordSelector, password);
                        break;
                    }
                }

                // Take a small pause before clicking submit
                browserManager.sleep(1000);

                // Click login button
                try {
                    log.debug("Clicking submit button");
                    browserManager.click(page, LOGIN_BUTTON_SELECTOR);
                } catch (Exception e) {
                    // If main button fails, try alternatives
                    log.warn("Failed to click primary login button, trying alternatives");

                    boolean buttonClicked = false;
                    for (String buttonSelector : loginButtonSelectors) {
                        if (browserManager.elementExists(page, buttonSelector, 1000)) {
                            try {
                                browserManager.click(page, buttonSelector);
                                log.info("Clicked alternative login button: {}", buttonSelector);
                                buttonClicked = true;
                                break;
                            } catch (Exception be) {
                                log.warn("Failed to click login button {}: {}", buttonSelector, be.getMessage());
                            }
                        }
                    }

                    if (!buttonClicked) {
                        throw new CrawlerException("Failed to click any login button");
                    }
                }

                // Wait for authentication to complete - longer than before
                log.debug("Waiting after submitting auth form");
                page.waitForTimeout(pageWaitTimeSeconds * 1000);

                log.info("Submitted authentication form");

                // Verify authentication was successful
                if (isAuthenticated(page)) {
                    log.info("Authentication successful");
                    return;
                } else {
                    if (retryCount < maxRetries - 1) {
                        log.warn("Authentication may have failed - login form still present. Retrying...");
                        retryCount++;
                        browserManager.sleep(3000 * retryCount); // Increasing backoff
                        continue;
                    } else {
                        log.warn("Authentication failed after {} attempts - login form still present", maxRetries);
                        String failedAuthScreenshot = "auth-failed-" + System.currentTimeMillis() + ".png";
                        try {
                            page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(failedAuthScreenshot)));
                            log.warn("Failed authentication screenshot saved to {}", failedAuthScreenshot);
                        } catch (Exception se) {
                            log.warn("Failed to take screenshot of authentication failure: {}", se.getMessage());
                        }
                        throw new CrawlerException("Authentication failed - login form still present after " + maxRetries + " attempts");
                    }
                }
            } catch (Exception e) {
                lastException = e;

                // Handle reconnection exceptions
                if (e instanceof BrowserlessManager.BrowserReconnectedException) {
                    log.info("Browser reconnected during authentication, updating page reference and retrying");
                    page = ((BrowserlessManager.BrowserReconnectedException) e).getNewPage();
                    retryCount++;
                    continue;
                }

                // For other exceptions
                log.error("Error during authentication attempt {}: {}", retryCount + 1, e.getMessage());

                try {
                    String errorScreenshot = "auth-error-" + System.currentTimeMillis() + ".png";
                    page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(errorScreenshot)));
                    log.error("Authentication error screenshot saved to {}", errorScreenshot);
                } catch (Exception se) {
                    log.warn("Failed to take screenshot of authentication error: {}", se.getMessage());
                }

                retryCount++;

                if (retryCount < maxRetries) {
                    log.info("Retrying authentication, attempt {} of {}", retryCount + 1, maxRetries);
                    browserManager.sleep(5000 * retryCount); // Increasing backoff
                } else {
                    throw new CrawlerException("Failed to authenticate after " + maxRetries + " attempts: " + e.getMessage(), e);
                }
            }
        }

        // If we reach here, all retries have failed
        throw new CrawlerException("Authentication failed after " + maxRetries + " attempts", lastException);
    }

    @Override
    public void logout(Page page) {
        log.info("Attempting to logout");
        try {
            // Try different logout URLs
            String[] logoutUrls = {
                "https://agents.damacproperties.com/secur/logout.jsp",
                "https://www.damacproperties.com/en/logout",
                "https://www.damacproperties.com/logout"
            };

            for (String url : logoutUrls) {
                try {
                    browserManager.navigateTo(page, url);
                    browserManager.sleep(1500);
                    log.info("Navigated to logout URL: {}", url);
                    break;
                } catch (Exception e) {
                    log.warn("Error navigating to logout URL {}: {}", url, e.getMessage());
                }
            }

            // Also try clicking logout buttons if they exist
            String[] logoutSelectors = {
                ".logout-button",
                "a[href*='logout']",
                "button:contains('Logout')",
                "button:contains('Sign Out')",
                "[data-testid='logout-button']"
            };

            for (String selector : logoutSelectors) {
                try {
                    if (browserManager.elementExists(page, selector, 1000)) {
                        browserManager.click(page, selector);
                        browserManager.sleep(1500);
                        log.info("Clicked logout button with selector: {}", selector);
                        break;
                    }
                } catch (Exception e) {
                    log.warn("Error clicking logout button {}: {}", selector, e.getMessage());
                }
            }

            log.info("Logout attempt completed");
        } catch (Exception e) {
            log.warn("Error during logout: {}", e.getMessage());
        }
    }
}
