secretsprovider: awskms:///arn:aws:kms:eu-west-1:623459828345:key/9c3a6b78-43e4-463a-935c-a5bbb373f029?region=eu-west-1&awssdk=v2
encryptedkey: AQICAHiTGai7v1hPMakNk6763UD8q+3xQeRU+R9BeBiSzxt4tgF/6GzQq390obPA1OyweWIDAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMil5M6HfViO0O45QXAgEQgDvaxN9pp+Ihjy66egdFmyfKqZfQh3uXUmCRzPXjhfupMdxVY8hGeV/AkppHV4kv5xikUNe1aWvAcK3xNg==
config:
  workflow-worker:config:
    image: 557013851399.dkr.ecr.eu-west-1.amazonaws.com/realmond-app/workflow-worker
    imageVersion: latest
    namespace: workflow
    containerEnv:
      STRAPI_API_TOKEN:
        secure: v1:Q+AU9Vh9+dG9Mhnd:mleCQz2Ct/r+SY4KCsKsu7o2frCSXWpEgkPY8oAtKaCQBwWBlV1f7y3xeyI9jzSCokbRaIQY/Vb2Fa+ULw0V53bJOSawMaG2PhKdLvQlghq/Pv4o3WQH/Xm9Sj84EoRxH0lM1EJ3HRTzk35em4xJM6KG/jxnzHNh+gR/Z3UCpsBYbzT9V82yvX/WB1OqSWGsP0/g8hbNHsE3ZLaRI4AGKQuvwzUAdKvT76P0p5szzp7igSuw8/VBrcCkP72j96+6AtEDSMOuZnhkgrGZeu38AAMnsb2uPaw3mnp0kRjBB4CHRENtazyTGj2LxmjugiDFyVgfHRSLe+gkxnwxqxgVdaM09gDWcfa1cXY1rGWt8M4=
      REDIS_CACHE_PASSWORD:
        secure: v1:cvv9DPhs8oPwuY/J:k27MfJlARELb4d0N5q5hYHZzrawanVCruGiPWhOZ9IFu2nLlDwm1o3DBKIWTjdpBuQsoeHcVm93yCA8E1S/exn8izct5l4dbo4LKv9YB2iY=
    applicationYaml:
      spring:
        temporal:
          connection:
            target: temporal-helm-chart-49900e30-frontend.temporal.svc.cluster.local:7233
      strapi:
        api:
          url: http://cms-ctrl-intra.strapi-ctrl.svc.cluster.local:1337/api
      redis:
        cache:
          host: web-redis-master.web-redis.svc.cluster.local
          port: 6379
          database: 1
          key-prefix: "cms-translate-wf:entity-blacklist:"
          expiration-hours: 24
  workflow-worker:kubeconfig:
    secure: v1:8LOhDuTEk/vAgIrI: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
