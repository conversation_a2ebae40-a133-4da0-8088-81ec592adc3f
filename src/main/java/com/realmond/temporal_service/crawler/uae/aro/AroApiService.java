package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.AdModel;
import com.realmond.etl.model.AmenityModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.PriceModel;
import com.realmond.etl.model.ProjectModel;
import com.realmond.etl.model.ProjectStats;
import com.realmond.etl.model.UnitStats;
import com.realmond.etl.model.common.BrochureModel;
import com.realmond.etl.model.common.LocationModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Service for processing ARO.ae data using typed REST API responses.
 * Eliminates HTML parsing in favor of structured JSON data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroApiService {

    private final AroFeignRestClient aroClient;
    private final AroEnrichmentService enrichmentService;
    private final AroSettings settings;

    /**
     * Result of project conversion including both the ProjectModel and raw API metadata.
     */
    private record ProjectConversionResult(Optional<ProjectModel> projectModel,
                                           Map<String, Object> rawApiMetadata) {
    }

    private static final Map<String, AdModel.PropertyTypeModel> PROPERTY_TYPE_MAPPING = Map.of(
          "Apartments",
          AdModel.PropertyTypeModel.APARTMENT,
          "Non-Residential",
          AdModel.PropertyTypeModel.COMMERCIAL_FALLBACK,
          "Residential",
          AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK,
          "Townhouse",
          AdModel.PropertyTypeModel.TOWNHOUSE,
          "Villa",
          AdModel.PropertyTypeModel.VILLA
    );

    /**
     * Fetches the total number of pages from ARO.ae using the API.
     *
     * @return The total number of pages
     */
    public int fetchProjectPagesCount() {
        try {
            // Fetch the first page to get pagination information
            AroApiResponse<ProjectSummary> response = aroClient.getProjects(
                  1,
                  settings.getPageSize()
            );

            if (response.getPaging() != null && response.getPaging().getTotal() != null) {
                int totalPages = response.getPaging().getTotal();

                log.info(
                      "Total projects: {}, page size: {}, total pages: {}",
                      response.getTotal(),
                      response.getPaging().getSize(),
                      totalPages
                );
                return totalPages;
            } else {
                log.warn("No pagination information found in response, defaulting to 1 page");
                return 1;
            }
        } catch (Exception e) {
            log.error("Error fetching project pages count: {}", e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching pages count", e);
        }
    }

    /**
     * Fetches and parses all projects from a single page using the typed API.
     *
     * @param page The page number to fetch and parse
     *
     * @return List of CrawlRecord containing all parsed projects from the page
     */
    public List<CrawlRecord<ProjectModel>> fetchProjectsPage(int page) {
        log.info("Fetching ARO.ae page: {}", page);
        List<CrawlRecord<ProjectModel>> result = new ArrayList<>();

        try {
            // Fetch the projects page using typed API
            AroApiResponse<ProjectSummary> response = aroClient.getProjects(
                  page,
                  settings.getPageSize()
            );

            if (response.getData() == null || response.getData().isEmpty()) {
                log.warn("No projects found on page {}", page);
                return result;
            }

            log.info("Found {} projects on page {}", response.getData().size(), page);

            // Process each project on the page
            for (ProjectSummary projectSummary : response.getData()) {
                try {
                    // Fetch detailed project information
                    ProjectDetail projectDetail = aroClient.getProjectBySlug(projectSummary.getSlug());

                    // Convert the typed response to ProjectModel and get raw API data
                    ProjectConversionResult conversionResult = convertToProjectModelWithMetadata(projectDetail,
                                                                                                 projectSummary
                    );

                    if (conversionResult.projectModel().isPresent()) {
                        ProjectModel project = conversionResult.projectModel().get();

                        // Wrap in CrawlRecord with raw API data in metadata
                        CrawlRecord<ProjectModel> crawlRecord = wrapProjectModelInCrawlRecord(project,
                                                                                              conversionResult.rawApiMetadata()
                        );
                        result.add(crawlRecord);

                        log.info(
                              "Successfully processed ARO.ae project: {}",
                              projectSummary.getSlug()
                        );
                    } else {
                        log.warn(
                              "Failed to convert ARO.ae project to ProjectModel: {}",
                              projectSummary.getSlug()
                        );
                    }
                } catch (Exception e) {
                    log.error(
                          "Error processing ARO.ae project {}: {}",
                          projectSummary.getSlug(),
                          e.getMessage(),
                          e
                    );
                    // Continue with next project even if one fails
                }
            }

            log.info("Successfully processed page {} with {} projects", page, result.size());
        } catch (Exception e) {
            log.error("Error fetching ARO.ae page {}: {}", page, e.getMessage(), e);
            throw new RetryableCrawlerException("Error fetching page: " + page, e);
        }

        return result;
    }

    /**
     * Converts ARO API ProjectDetail to ProjectModel with raw API metadata.
     * This method includes all raw API data in the metadata for the CrawlRecord.
     *
     * @param detail  The typed project detail from the API
     * @param summary The project summary from the listing API
     *
     * @return ProjectConversionResult containing both ProjectModel and raw API metadata
     */
    private ProjectConversionResult convertToProjectModelWithMetadata(
          ProjectDetail detail,
          ProjectSummary summary
    ) {
        Map<String, Object> metadata = new HashMap<>();
        Map<String, Object> rawData = new HashMap<>();

        try {
            // Store raw API responses in raw_data
            rawData.put("project_summary", summary);
            rawData.put("project_detail", detail);

            // Get enriched data and store raw responses
            AroEnrichmentService.EnrichedProjectInfo enrichedInfo = null;
            try {
                enrichedInfo = enrichmentService.enrichProjectDetail(detail);
                rawData.put("enriched_info", enrichedInfo);
                rawData.put("amenities", enrichedInfo.getAmenities());
                rawData.put("unit_stats", enrichedInfo.getUnitStats());
                rawData.put("buildings", enrichedInfo.getBuildings());
            } catch (Exception e) {
                log.warn(
                      "Failed to enrich project detail for {}: {}",
                      detail.getSlug(),
                      e.getMessage()
                );
                // Store empty lists as fallback when enrichment fails
                rawData.put("enriched_info", null);
                rawData.put("amenities", Collections.emptyList());
                rawData.put("unit_stats", Collections.emptyList());
                rawData.put("buildings", Collections.emptyList());
                rawData.put("enrichment_error", e.getMessage());

                // Create a minimal enriched info with empty lists for processing
                enrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
                      .projectDetail(detail)
                      .amenities(Collections.emptyList())
                      .unitStats(Collections.emptyList())
                      .buildings(Collections.emptyList())
                      .build();
            }

            // Wrap raw data under raw_data key
            metadata.put("raw_data", rawData);

            // Add metadata about the conversion process
            metadata.put("conversion_timestamp", java.time.Instant.now().toString());
            metadata.put("source_api", "aro.ae");
            metadata.put("api_version", "v1");

            // Convert to ProjectModel using existing logic
            Optional<ProjectModel> projectModel = convertToProjectModel(detail, enrichedInfo);

            return new ProjectConversionResult(projectModel, metadata);

        } catch (Exception e) {
            log.error(
                  "Error converting project detail to ProjectModel with metadata: {}",
                  e.getMessage(),
                  e
            );
            metadata.put("raw_data", rawData);
            metadata.put("conversion_error", e.getMessage());
            metadata.put("conversion_error_timestamp", java.time.Instant.now().toString());
            return new ProjectConversionResult(Optional.empty(), metadata);
        }
    }

    /**
     * Converts ARO API ProjectDetail to ProjectModel.
     * This method uses the enrichment service to get all additional data in one call.
     *
     * @param detail       The typed project detail from the API
     * @param enrichedInfo Pre-fetched enriched project information
     *
     * @return Optional ProjectModel
     */
    private Optional<ProjectModel> convertToProjectModel(
          ProjectDetail detail,
          AroEnrichmentService.EnrichedProjectInfo enrichedInfo
    ) {
        try {
            // --- Basic identifiers & metadata --------------------------------------------------
            String developerName = detail.getDeveloper() != null ? detail.getDeveloper()
                  .getTitle() : "unknown-developer";
            String developerSlug = slugify(developerName);
            String developerUrn = AroSettings.SOURCE_URN + ":developer:" + developerSlug;

            String projectSlug = detail.getSlug();
            String projectUrn = developerUrn + ":project:" + projectSlug;

            var builder = ProjectModel.builder();
            builder.withProjectUrn(projectUrn);
            builder.withDeveloperUrn(developerUrn);
            builder.withProjectSlug(projectSlug);
            builder.withSourceUrn(AroSettings.SOURCE_URN);
            builder.withTitle(detail.getTitle());
            builder.withDescription(detail.getDescription());
            builder.withCurrency("AED");

            // Set external ID
            builder.withExternalId(String.valueOf(detail.getId()));

            // --- Location --------------------------------------------------------------------
            if (detail.getAddress() != null && !detail.getAddress().isBlank()) {
                var locBuilder = LocationModel.builder()
                      .withAddress(detail.getAddress())
                      .withCity("Dubai")
                      .withState("Dubai")
                      .withCountry("AE");
                builder.withLocation(locBuilder.build());
            }

            // --- Images ----------------------------------------------------------------------
            List<ImageModel> images = new ArrayList<>();
            if (detail.getImages() != null) {
                for (String imgUrl : detail.getImages()) {
                    try {
                        ImageModel img = ImageModel.builder()
                              .withUrl(new java.net.URI(imgUrl))
                              .build();
                        images.add(img);
                    } catch (Exception ex) {
                        log.debug("Invalid image URI skipped: {}", imgUrl);
                    }
                }
            }
            if (!images.isEmpty()) {
                builder.withImages(images);
                builder.withCoverImage(images.get(0));
            }

            // --- Project status --------------------------------------------------------------
            if (detail.getHandoverDate() != null) {
                if (detail.getHandoverDate().isBefore(java.time.ZonedDateTime.now())) {
                    builder.withProjectStatus(AdModel.ConstructionStatusModel.FINISHED);
                } else {
                    builder.withProjectStatus(AdModel.ConstructionStatusModel.ACTIVE);
                }
            }

            // --- USE PROVIDED ENRICHED INFO ------------------------------------------------

            // --- Project Stats with enriched data -------------------------------------------
            ProjectStats.ProjectStatsBuilderBase statsBuilder = ProjectStats.builder();

            // Set completion date from handover date
            if (detail.getHandoverDate() != null) {
                String completionDateStr = detail.getHandoverDate().toLocalDate().toString();
                statsBuilder.withCompletionDate(completionDateStr);
            }

            // --- Property Types from Building data (as requested) ---------------------------
            List<AdModel.PropertyTypeModel> propertyTypes = extractPropertyTypesFromBuildings(
                  enrichedInfo.getBuildings());
            if (!propertyTypes.isEmpty()) {
                statsBuilder.withPropertyTypes(propertyTypes);
            } else {
                // Default fallback
                propertyTypes = List.of(AdModel.PropertyTypeModel.APARTMENT);
                statsBuilder.withPropertyTypes(propertyTypes);
            }

            // --- Unit stats & min price ---------------------------------------------------
            if (enrichedInfo.getUnitStats() != null && !enrichedInfo.getUnitStats().isEmpty()) {
                // Calculate minimum price
                double minPrice = enrichedInfo.getUnitStats()
                      .stream()
                      .filter(us -> us.getPriceFrom() != null &&
                            us.getPriceFrom().getAmount() != null)
                      .mapToDouble(us -> us.getPriceFrom().getAmount().doubleValue())
                      .min()
                      .orElse(Double.NaN);

                if (!Double.isNaN(minPrice)) {
                    PriceModel priceMinModel = PriceModel.builder()
                          .withValue(minPrice)
                          .withCurrency("AED")
                          .build();
                    statsBuilder.withPriceMin(priceMinModel);
                }

                // Map unit stats to internal model using discovered property types
                List<UnitStats> internalUnitStats = createUnitStatsWithPropertyTypes(
                      enrichedInfo.getUnitStats(),
                      propertyTypes
                );

                statsBuilder.withUnits(internalUnitStats);
            }

            builder.withProjectStats(statsBuilder.build());

            // --- Amenities -------------------------------------------------------------------
            if (enrichedInfo.getAmenities() != null && !enrichedInfo.getAmenities().isEmpty()) {
                List<AmenityModel> amenities = new ArrayList<>();
                for (com.realmond.temporal_service.crawler.uae.aro.model.Amenity am : enrichedInfo.getAmenities()) {
                    if (am.getTitle() != null && !am.getTitle().trim().isEmpty()) {
                        AmenityModel amenity = AmenityModel.builder()
                              .withLabel(am.getTitle())
                              .build();
                        amenities.add(amenity);
                    }
                }
                builder.withAmenities(amenities);
            }

            // --- Brochure --------------------------------------------------------------------
            if (Boolean.TRUE.equals(detail.getHasBrochure())) {
                String brochureLink = settings.getBaseUrl() +
                      "/api/v2/projects/" +
                      detail.getId() +
                      "/download-brochure";
                try {
                    BrochureModel brochure = BrochureModel.builder()
                          .withUrl(new java.net.URI(brochureLink))
                          .build();
                    builder.withBrochures(List.of(brochure));
                } catch (Exception ex) {
                    log.debug("Invalid brochure URI for project {}", detail.getSlug());
                }
            }

            // --- URLs ------------------------------------------------------------------------
            try {
                java.net.URI pageUri = new java.net.URI(settings.getBaseUrl() +
                                                              "/project/" +
                                                              projectSlug +
                                                              "/");
                builder.withUrls(List.of(pageUri));
            } catch (Exception ignored) {
                log.debug("Could not create project URL for {}", projectSlug);
            }

            return Optional.of(builder.build());
        } catch (Exception e) {
            log.error("Error converting project detail to ProjectModel: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Extracts property types from building data using the specified mapping.
     *
     * @param buildings List of buildings from the API
     *
     * @return List of mapped property types
     */
    private List<AdModel.PropertyTypeModel> extractPropertyTypesFromBuildings(List<Building> buildings) {
        Set<AdModel.PropertyTypeModel> propertyTypes = new HashSet<>();

        if (buildings != null && !buildings.isEmpty()) {
            for (Building building : buildings) {
                if (building.getCategory() != null && !building.getCategory().trim().isEmpty()) {
                    AdModel.PropertyTypeModel mappedType = PROPERTY_TYPE_MAPPING.get(building.getCategory());
                    if (mappedType != null) {
                        propertyTypes.add(mappedType);
                        log.debug(
                              "Mapped building category '{}' to property type '{}'",
                              building.getCategory(),
                              mappedType
                        );
                    } else {
                        log.debug(
                              "Unknown building category '{}', using residential fallback",
                              building.getCategory()
                        );
                        propertyTypes.add(AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK);
                    }
                }
            }
        }

        return new ArrayList<>(propertyTypes);
    }

    /**
     * Creates unit stats with appropriate property types based on building data.
     * Logic:
     * - If single property type: use it straight
     * - If multiple property types: duplicate stats for each residential type
     * - If no property types: use apartment as fallback
     *
     * @param apiUnitStats            Unit stats from ARO API
     * @param discoveredPropertyTypes Property types discovered from buildings
     *
     * @return List of internal unit stats with proper property types
     */
    private List<UnitStats> createUnitStatsWithPropertyTypes(
          List<com.realmond.temporal_service.crawler.uae.aro.model.UnitStats> apiUnitStats,
          List<AdModel.PropertyTypeModel> discoveredPropertyTypes
    ) {

        List<UnitStats> internalUnitStats = new ArrayList<>();

        if (apiUnitStats == null || apiUnitStats.isEmpty()) {
            return internalUnitStats;
        }

        // Determine which property types to use for unit stats
        List<AdModel.PropertyTypeModel> applicablePropertyTypes = getResidentialPropertyTypes(
              discoveredPropertyTypes);

        // If no residential types found, default to apartment
        if (applicablePropertyTypes.isEmpty()) {
            applicablePropertyTypes = List.of(AdModel.PropertyTypeModel.APARTMENT);
            log.debug("No residential property types found, defaulting to APARTMENT for unit stats");
        }

        // Create unit stats for each applicable property type
        for (com.realmond.temporal_service.crawler.uae.aro.model.UnitStats us : apiUnitStats) {
            for (AdModel.PropertyTypeModel propertyType : applicablePropertyTypes) {
                var usb = UnitStats.builder()
                      .withBedrooms(us.getBedrooms() != null ? us.getBedrooms()
                            .doubleValue() : null)
                      .withPropertyType(propertyType);

                if (us.getPriceFrom() != null && us.getPriceFrom().getAmount() != null) {
                    PriceModel unitPrice = PriceModel.builder()
                          .withValue(us.getPriceFrom().getAmount().doubleValue())
                          .withCurrency("AED")
                          .build();
                    usb.withPriceMin(unitPrice);
                }

                if (us.getSizeFrom() != null && us.getSizeFrom().getValue() != null) {
                    // Convert sqft to sqm (1 sqft = 0.092903 sqm)
                    double areaSqm = us.getSizeFrom().getValue() * 0.092903;
                    usb.withAreaRangeSqm(areaSqm);
                }

                internalUnitStats.add(usb.build());

                log.debug(
                      "Created unit stat for {} bedrooms with property type {}",
                      us.getBedrooms(),
                      propertyType
                );
            }
        }

        return internalUnitStats;
    }

    /**
     * Filters property types to only include residential types suitable for unit stats.
     *
     * @param propertyTypes All discovered property types
     *
     * @return Only residential property types
     */
    private List<AdModel.PropertyTypeModel> getResidentialPropertyTypes(List<AdModel.PropertyTypeModel> propertyTypes) {
        Set<AdModel.PropertyTypeModel> residentialTypes = Set.of(
              AdModel.PropertyTypeModel.APARTMENT,
              AdModel.PropertyTypeModel.VILLA,
              AdModel.PropertyTypeModel.TOWNHOUSE,
              AdModel.PropertyTypeModel.DUPLEX,
              AdModel.PropertyTypeModel.TRIPLEX,
              AdModel.PropertyTypeModel.PENTHOUSE,
              AdModel.PropertyTypeModel.HOTEL_APARTMENT,
              AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK
        );

        return propertyTypes.stream()
              .filter(residentialTypes::contains)
              .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * Converts a string into a URL friendly slug.
     */
    private String slugify(String input) {
        if (input == null) {
            return "";
        }
        String slug = input.toLowerCase();
        slug = slug.replaceAll("\\s+", "-");
        slug = slug.replaceAll("[^a-z0-9-]", "");
        slug = slug.replaceAll("-+", "-");
        slug = slug.replaceAll("^-|-$", "");
        return slug;
    }

    /**
     * Wraps a ProjectModel in a CrawlRecord.
     *
     * @param model        The project model to wrap
     * @param syncMetadata Additional metadata for the crawl record
     *
     * @return The wrapped CrawlRecord
     */
    private static CrawlRecord<ProjectModel> wrapProjectModelInCrawlRecord(
          ProjectModel model,
          Map<String, Object> syncMetadata
    ) {
        return new CrawlRecord<>(model, syncMetadata, model.getSourceUrn(), model.getProjectUrn());
    }
}
