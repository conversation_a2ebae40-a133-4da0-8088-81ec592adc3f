# ARO.ae Feign Client Setup

## Dependencies

Ensure your `pom.xml` includes the Spring Cloud OpenFeign dependency:

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>
```

## Configuration

### 1. Enable Feign Clients

Add `@EnableFeignClients` to your main application class or import the auto-configuration:

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.realmond.temporal_service.crawler.uae.aro")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

Or import the auto-configuration:

```java
@SpringBootApplication
@Import(AroFeignAutoConfiguration.class)
public class Application {
    // ...
}
```

### 2. Application Properties

Add the following properties to your `application.yml` or `application.properties`:

```yaml
# ARO.ae Configuration
aro:
  base-url: https://aro.ae
  default-page-size: 30
  max-retries: 3
  retry-delay-ms: 2000

# Feign Configuration
feign:
  client:
    config:
      aro-api:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: basic
```

### 3. Logging Configuration

To see Feign request/response logs, add to your logging configuration:

```yaml
logging:
  level:
    com.realmond.temporal_service.crawler.uae.aro.AroFeignRestClient: DEBUG
```

## Usage

Inject the Feign client or high-level services:

```java
@Service
public class MyService {
    
    @Autowired
    private AroFeignRestClient aroFeignClient; // Direct Feign client
    
    @Autowired
    private AroClient aroClient; // High-level business interface
    
    @Autowired
    private AroEnrichmentService enrichmentService; // Enrichment service
    
    public void example() {
        // Use any of the injected beans
        var projects = aroClient.fetchProjectsList(1);
        // ...
    }
}
```

## Testing

For unit tests, you can easily mock the Feign client:

```java
@ExtendWith(MockitoExtension.class)
class MyServiceTest {
    
    @Mock
    private AroFeignRestClient aroFeignClient;
    
    @InjectMocks
    private MyService myService;
    
    @Test
    void testExample() {
        // Mock the Feign client
        when(aroFeignClient.getProjects(1, 30))
            .thenReturn(mockResponse);
        
        // Test your service
        myService.example();
        
        // Verify interactions
        verify(aroFeignClient).getProjects(1, 30);
    }
}
```

## Troubleshooting

### 1. FeignException: Connection Refused

- Check that `aro.base-url` is correctly configured
- Verify network connectivity to ARO.ae

### 2. No qualifying bean of type 'AroFeignRestClient'

- Ensure `@EnableFeignClients` is present with the correct package
- Check that the Feign client interface is in the scanned package

### 3. JSON Parsing Errors

- Verify the response models match the actual API responses
- Check Jackson configuration for date/time parsing

### 4. Rate Limiting

- The client includes random delays and proper headers to avoid rate limiting
- If issues persist, increase `aro.retry-delay-ms` or reduce request frequency 