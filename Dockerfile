#############################
# Stage 1: Build the Application with Maven & Java 21
#############################
FROM openjdk:21-slim AS build

# Optional: Set a JAVA_ENV argument (default: production)
ARG JAVA_ENV=production
ENV JAVA_ENV=${JAVA_ENV}

# Set the working directory inside the container
WORKDIR /app

# Copy the Maven descriptor file to leverage Docker caching for dependencies
COPY pom.xml mvnw ./
COPY .mvn/wrapper/maven-wrapper.properties .mvn/wrapper/maven-wrapper.properties

# Download all dependencies ahead of time (improves caching)
RUN ./mvnw dependency:go-offline

# Copy the source code into the container
COPY src ./src

# Build the application, skipping tests
RUN ./mvnw clean package -DskipTests

#############################
# Stage 2: Create the Final Runtime Image with Java 21
#############################
FROM openjdk:21-slim

# Propagate the JAVA_ENV variable
ARG JAVA_ENV=production
ENV JAVA_ENV=${JAVA_ENV}

# Set the working directory for runtime
WORKDIR /app

# Copy the built JAR file from the build stage (adjust the path and pattern as needed)
COPY --from=build /app/target/*.jar app.jar
COPY src/main/resources/application.yaml application.yaml

# Expose the port your application listens on (commonly 8080; adjust if needed)
EXPOSE 8080

# Start the application
CMD ["java", "-jar", "app.jar"]
