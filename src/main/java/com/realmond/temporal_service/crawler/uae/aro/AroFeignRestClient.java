package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.uae.aro.model.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Feign client for ARO.ae REST API with typed responses.
 */
@FeignClient(
    name = "aro-api",
    url = "${crawler.uae.aro.base-url:https://aro.ae}",
    configuration = AroFeignConfiguration.class
)
public interface AroFeignRestClient {

    /**
     * Fetches paginated list of projects.
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return Paginated response containing project summaries
     */
    @GetMapping("/api/v1/projects")
    AroApiResponse<ProjectSummary> getProjects(
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches detailed project information by slug.
     * @param slug Project slug identifier
     * @return Detailed project information
     */
    @GetMapping("/api/v1/projects/{slug}")
    ProjectDetail getProjectBySlug(@PathVariable("slug") String slug);

    /**
     * Fetches amenities for a project.
     * @param projectId Project ID
     * @return List of amenities
     */
    @GetMapping("/api/v1/amenities")
    List<Amenity> getProjectAmenities(@RequestParam("projectId") int projectId);

    /**
     * Fetches unit statistics grouped by bedroom count.
     * @param projectId Project ID
     * @param pageNumber Page number (1-based)
     * @param pageSize Number of items per page
     * @return List of unit statistics
     */
    @GetMapping("/api/v1/listings/bedrooms")
    List<UnitStats> getUnitStats(
            @RequestParam("projectId") int projectId,
            @RequestParam("pageNumber") int pageNumber,
            @RequestParam("pageSize") int pageSize
    );

    /**
     * Fetches building details for a project.
     *
     * @param projectId Project ID
     * @param smt Size measurement type, e.g. "sqft" or "sqm"
     * @return List of buildings with typed structure
     */
    @GetMapping("/api/v1/buildings")
    List<Building> getProjectBuildings(
            @RequestParam("projectId") int projectId,
            @RequestParam("smt") String smt
    );
}
