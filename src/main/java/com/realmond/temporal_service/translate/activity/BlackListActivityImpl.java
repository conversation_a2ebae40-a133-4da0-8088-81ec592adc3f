package com.realmond.temporal_service.translate.activity;

import com.realmond.temporal_service.translate.cache.BlackListEntityCache;
import io.temporal.spring.boot.ActivityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ActivityImpl(taskQueues = "translation-task-queue")
public class BlackListActivityImpl implements BlackListActivity {

    private final BlackListEntityCache blackListEntityCache;

    @Autowired
    public BlackListActivityImpl(BlackListEntityCache blackListEntityCache) {
        this.blackListEntityCache = blackListEntityCache;
    }

    @Override
    public boolean isBlackListed(String contentType, Long entityId) {
        return blackListEntityCache.containsKey(contentType) &&
                blackListEntityCache.get(contentType).contains(entityId);
    }
}
