package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.*;
import com.realmond.etl.model.common.BrochureModel;
import com.realmond.etl.model.common.LocationModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.DeveloperRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.DeveloperRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Parser for ARO.ae project details.
 * Extracts project information from the HTML content of project detail pages.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroProjectDetailParser {

    private static final String SOURCE_URN = "aro.ae";
    private static final Pattern PRICE_PATTERN = Pattern.compile("([\\d,.]+)\\s*(?:K|M)?\\s*AED");
    private static final Pattern AREA_PATTERN = Pattern.compile("([\\d,.]+)\\s*sq(?:ft|\\s*ft|\\s*m)");
    private static final Pattern HANDOVER_DATE_PATTERN = Pattern.compile("Q([1-4])\\s+(\\d{4})");
    private static final String BASE_URL = "https://aro.ae";

    private final ObjectMapper objectMapper;
    private final DeveloperRepository developerRepository;

    /**
     * Parses project details from the HTML content of a project page.
     *
     * @param htmlContent The HTML content of the project detail page
     * @param projectSlug The project slug/identifier
     * @return Optional containing the parsed ProjectModel, or empty if parsing fails
     */
    public Optional<ProjectModel> parseProjectDetails(String htmlContent, String projectSlug) {
        if (htmlContent == null || htmlContent.isEmpty()) {
            log.warn("HTML content is null or empty. Cannot parse project details.");
            return Optional.empty();
        }

        try {
            Document doc = Jsoup.parse(htmlContent);

            // Extract project title
            String title = extractProjectTitle(doc);
            if (title == null || title.isEmpty()) {
                log.warn("Could not extract project title from HTML content");
                return Optional.empty();
            }

            // Extract developer information
            String developerName = extractDeveloperName(doc);
            String developerSlug = slugify(developerName);
            String developerUrn = SOURCE_URN + ":developer:" + developerSlug;
            String projectUrn = developerUrn + ":project:" + projectSlug;

            // Build the project model
            ProjectModel.ProjectModelBuilderBase builder = ProjectModel.builder();
            builder.withProjectUrn(projectUrn);
            builder.withDeveloperUrn(developerUrn);
            builder.withProjectSlug(projectSlug);
            builder.withSourceUrn(SOURCE_URN);
            builder.withTitle(title);
            builder.withDescription(extractDescription(doc));

            // Extract location
            LocationModel location = extractLocation(doc);
            builder.withLocation(location);

            // Extract images
            List<ImageModel> images = extractImages(doc);
            builder.withImages(images);

            // Set cover image (first image if available)
            if (!images.isEmpty()) {
                builder.withCoverImage(images.get(0));
            }

            // Extract project status
            AdModel.ConstructionStatusModel status = extractProjectStatus(doc);
            builder.withProjectStatus(status);

            // Extract project stats
            ProjectStats stats = extractProjectStats(doc);
            builder.withProjectStats(stats);

            // Set default currency
            builder.withCurrency("AED");

            // Extract amenities
            List<AmenityModel> amenities = extractAmenities(doc);
            builder.withAmenities(amenities);

            // Extract brochures
            List<BrochureModel> brochures = extractBrochures(doc);
            builder.withBrochures(brochures);

            // Set project URL
            List<URI> urls = new ArrayList<>();
            try {
                urls.add(new URI(BASE_URL + "/project/" + projectSlug + "/"));
            } catch (URISyntaxException e) {
                log.warn("Could not create project URL", e);
            }
            builder.withUrls(urls);

            // Extract and save developer information
            extractAndSaveDeveloper(developerName, developerUrn, doc);

            // Build and return the project model
            ProjectModel projectModel = builder.build();
            log.info("Successfully parsed project: {}", title);
            return Optional.of(projectModel);

        } catch (Exception e) {
            log.error("Error parsing project details: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Extracts the project title from the document.
     */
    private String extractProjectTitle(Document doc) {
        Element titleElement = doc.selectFirst("h1");
        if (titleElement != null) {
            return titleElement.text().trim();
        }

        // Fallback to meta title
        Element metaTitleElement = doc.selectFirst("meta[property=og:title]");
        if (metaTitleElement != null) {
            String metaTitle = metaTitleElement.attr("content").trim();
            // Remove " | ⁨Q4 2026⁩" suffix if present
            int pipeIndex = metaTitle.indexOf(" | ");
            if (pipeIndex > 0) {
                return metaTitle.substring(0, pipeIndex).trim();
            }
            return metaTitle;
        }

        return null;
    }

    /**
     * Extracts the developer name from the document.
     */
    private String extractDeveloperName(Document doc) {
        Elements developerElements = doc.select(".developer-name, [class*=developer]");
        for (Element element : developerElements) {
            String text = element.text().trim();
            if (!text.isEmpty()) {
                return text;
            }
        }

        // Try to find "Developed by" text
        Elements elements = doc.getElementsContainingText("Developed by");
        for (Element element : elements) {
            String text = element.text().trim();
            if (text.contains("Developed by")) {
                String developerName = text.substring(text.indexOf("Developed by") + "Developed by".length()).trim();
                // Remove any non-alphanumeric characters at the end
                developerName = developerName.replaceAll("[^a-zA-Z0-9\\s]+$", "").trim();
                if (!developerName.isEmpty()) {
                    return developerName;
                }
            }
        }

        return "Unknown Developer";
    }

    /**
     * Extracts the project description from the document.
     */
    private String extractDescription(Document doc) {
        // Look for a paragraph that's not part of a specific section
        Elements paragraphs = doc.select("p");
        for (Element p : paragraphs) {
            if (!p.parents().select("section, footer, header, .amenities, .location").isEmpty()) {
                continue; // Skip paragraphs in specific sections
            }

            String text = p.text().trim();
            if (text.length() > 50) { // Likely a description if it's long enough
                return text;
            }
        }

        // Try to find any large text block
        Elements textBlocks = doc.select("div:not(.amenities):not(.location):not(section):not(header):not(footer)");
        for (Element block : textBlocks) {
            String text = block.text().trim();
            if (text.length() > 100 && !text.contains("Download brochure") && !text.contains("Show all photos")) {
                return text;
            }
        }

        return null;
    }

    /**
     * Extracts the project location from the document.
     */
    private LocationModel extractLocation(Document doc) {
        String address = null;
        String city = "Dubai"; // Default city
        String cityDistrict = null;

        // Look for location information
        Elements locationElements = doc.select("h1 + div, .location");
        for (Element element : locationElements) {
            String text = element.text().trim();
            if (text.contains("Dubai") && text.length() < 100) {
                address = text;

                // Try to extract city district
                String[] parts = text.split(",");
                if (parts.length > 1) {
                    for (String part : parts) {
                        if (part.trim().length() > 0 && !part.trim().equals("Dubai") && !part.trim().equals("UAE")) {
                            cityDistrict = part.trim();
                            break;
                        }
                    }
                }

                break;
            }
        }

        if (address == null) {
            // Try to find location in breadcrumbs
            Elements breadcrumbs = doc.select(".breadcrumb-item, nav a");
            if (breadcrumbs.size() > 1) {
                for (Element crumb : breadcrumbs) {
                    String text = crumb.text().trim();
                    if (!text.equals("Main page") && !text.equals("Projects") && !text.equals(extractProjectTitle(doc))) {
                        cityDistrict = text;
                        address = city + ", " + cityDistrict;
                        break;
                    }
                }
            }
        }

        if (address != null) {
            return LocationModel.builder()
                    .withAddress(address)
                    .withCity(city)
                    .withState("Dubai")
                    .withCityDistrict(cityDistrict)
                    .withCountry("AE")
                    .build();
        }

        return null;
    }

    /**
     * Extracts images from the document.
     */
    private List<ImageModel> extractImages(Document doc) {
        List<ImageModel> images = new ArrayList<>();

        // Look for image elements
        Elements imageElements = doc.select("img[src*=cloudfront]");
        for (Element img : imageElements) {
            String src = img.attr("src");
            if (src != null && !src.isEmpty() && src.contains("cloudfront")) {
                try {
                    URI imageUri = new URI(src);
                    ImageModel image = ImageModel.builder()
                            .withUrl(imageUri)
                            .build();
                    images.add(image);
                } catch (URISyntaxException e) {
                    log.warn("Invalid image URI: {}", src, e);
                }
            }
        }

        return images;
    }

    /**
     * Extracts the project status from the document.
     */
    private AdModel.ConstructionStatusModel extractProjectStatus(Document doc) {
        // Look for handover date or completion date
        Elements elements = doc.getElementsContainingText("Handover date");
        if (!elements.isEmpty()) {
            return AdModel.ConstructionStatusModel.ACTIVE; // If handover date is mentioned, it's under construction
        }

        // Check for "Completed" text
        elements = doc.getElementsContainingText("Completed");
        if (!elements.isEmpty()) {
            for (Element element : elements) {
                if (element.text().contains("Completed")) {
                    return AdModel.ConstructionStatusModel.FINISHED;
                }
            }
        }

        // Default to ACTIVE if we can't determine
        return AdModel.ConstructionStatusModel.ACTIVE;
    }

    /**
     * Extracts project statistics from the document.
     */
    private ProjectStats extractProjectStats(Document doc) {
        ProjectStats.ProjectStatsBuilderBase statsBuilder = ProjectStats.builder();

        // Extract price information
        double minPrice = extractMinPrice(doc);
        if (minPrice > 0) {
            PriceModel priceMin = PriceModel.builder()
                    .withValue(minPrice)
                    .withCurrency("AED")
                    .build();
            statsBuilder.withPriceMin(priceMin);
        }

        // Extract property types
        List<AdModel.PropertyTypeModel> propertyTypes = extractPropertyTypes(doc);
        statsBuilder.withPropertyTypes(propertyTypes);

        // Extract completion date
        String completionDate = extractCompletionDate(doc);
        statsBuilder.withCompletionDate(completionDate);

        // Extract unit stats
        List<UnitStats> unitStatsList = extractUnitStats(doc);
        statsBuilder.withUnits(unitStatsList);

        return statsBuilder.build();
    }

    /**
     * Extracts the minimum price from the document.
     */
    private double extractMinPrice(Document doc) {
        // Look for price information
        Elements priceElements = doc.select("*:containsOwn(From)");
        for (Element element : priceElements) {
            String text = element.text().trim();
            Matcher matcher = PRICE_PATTERN.matcher(text);
            if (matcher.find()) {
                String priceStr = matcher.group(1).replaceAll("[^\\d.]", "");
                try {
                    return Double.parseDouble(priceStr);
                } catch (NumberFormatException e) {
                    log.warn("Could not parse price: {}", priceStr, e);
                }
            }
        }

        // Try to find price in other elements
        Elements elements = doc.getElementsContainingText("AED");
        for (Element element : elements) {
            String text = element.text().trim();
            if (text.contains("From") || text.contains("from")) {
                Matcher matcher = PRICE_PATTERN.matcher(text);
                if (matcher.find()) {
                    String priceStr = matcher.group(1).replaceAll("[^\\d.]", "");
                    try {
                        return Double.parseDouble(priceStr);
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse price: {}", priceStr, e);
                    }
                }
            }
        }

        return 0;
    }

    /**
     * Extracts property types from the document.
     */
    private List<AdModel.PropertyTypeModel> extractPropertyTypes(Document doc) {
        List<AdModel.PropertyTypeModel> propertyTypes = new ArrayList<>();

        // Look for bedroom information
        if (doc.getElementsContainingText("1 Bedroom").size() > 0) {
            propertyTypes.add(AdModel.PropertyTypeModel.APARTMENT);
        }

        if (doc.getElementsContainingText("2 Bedrooms").size() > 0 ||
            doc.getElementsContainingText("3 Bedrooms").size() > 0) {
            if (!propertyTypes.contains(AdModel.PropertyTypeModel.APARTMENT)) {
                propertyTypes.add(AdModel.PropertyTypeModel.APARTMENT);
            }
        }

        if (doc.getElementsContainingText("Villa").size() > 0 ||
            doc.getElementsContainingText("Townhouse").size() > 0) {
            propertyTypes.add(AdModel.PropertyTypeModel.VILLA);
        }

        // If no specific types found, default to APARTMENT
        if (propertyTypes.isEmpty()) {
            propertyTypes.add(AdModel.PropertyTypeModel.APARTMENT);
        }

        return propertyTypes;
    }

    /**
     * Extracts the completion date from the document.
     */
    private String extractCompletionDate(Document doc) {
        // Look for handover date
        Elements elements = doc.getElementsContainingText("Handover date");
        for (Element element : elements) {
            String text = element.text().trim();
            if (text.contains("Q")) {
                Matcher matcher = HANDOVER_DATE_PATTERN.matcher(text);
                if (matcher.find()) {
                    String quarter = matcher.group(1);
                    String year = matcher.group(2);

                    // Convert quarter to month
                    int month;
                    switch (quarter) {
                        case "1": month = 3; break;  // Q1 -> March
                        case "2": month = 6; break;  // Q2 -> June
                        case "3": month = 9; break;  // Q3 -> September
                        case "4": month = 12; break; // Q4 -> December
                        default: month = 12;
                    }

                    return year + "-" + String.format("%02d", month) + "-01";
                }
            }
        }

        // Look for completion date in meta title
        Element metaTitleElement = doc.selectFirst("meta[property=og:title]");
        if (metaTitleElement != null) {
            String metaTitle = metaTitleElement.attr("content").trim();
            if (metaTitle.contains("Q")) {
                Matcher matcher = Pattern.compile("Q([1-4])\\s+(\\d{4})").matcher(metaTitle);
                if (matcher.find()) {
                    String quarter = matcher.group(1);
                    String year = matcher.group(2);

                    // Convert quarter to month
                    int month;
                    switch (quarter) {
                        case "1": month = 3; break;  // Q1 -> March
                        case "2": month = 6; break;  // Q2 -> June
                        case "3": month = 9; break;  // Q3 -> September
                        case "4": month = 12; break; // Q4 -> December
                        default: month = 12;
                    }

                    return year + "-" + String.format("%02d", month) + "-01";
                }
            }
        }

        return null;
    }

    /**
     * Extracts unit statistics from the document.
     */
    private List<UnitStats> extractUnitStats(Document doc) {
        List<UnitStats> unitStatsList = new ArrayList<>();

        // Look for bedroom information sections
        Elements bedroomSections = doc.select("*:containsOwn(Bedroom), *:containsOwn(Bedrooms)");
        for (Element section : bedroomSections) {
            try {
                String text = section.text().trim();

                // Extract bedroom count
                int bedrooms = 0;
                if (text.contains("1 Bedroom")) {
                    bedrooms = 1;
                } else if (text.contains("2 Bedrooms")) {
                    bedrooms = 2;
                } else if (text.contains("3 Bedrooms")) {
                    bedrooms = 3;
                } else if (text.contains("4 Bedrooms")) {
                    bedrooms = 4;
                } else if (text.contains("5 Bedrooms")) {
                    bedrooms = 5;
                }

                if (bedrooms == 0) {
                    continue;
                }

                // Extract area
                double area = 0;
                Matcher areaMatcher = AREA_PATTERN.matcher(text);
                if (areaMatcher.find()) {
                    String areaStr = areaMatcher.group(1).replaceAll("[^\\d.]", "");
                    try {
                        area = Double.parseDouble(areaStr);
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse area: {}", areaStr, e);
                    }
                }

                // Extract price
                double price = 0;
                Matcher priceMatcher = PRICE_PATTERN.matcher(text);
                if (priceMatcher.find()) {
                    String priceStr = priceMatcher.group(1).replaceAll("[^\\d.]", "");
                    try {
                        price = Double.parseDouble(priceStr);
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse price: {}", priceStr, e);
                    }
                }

                // Create unit stats
                UnitStats.UnitStatsBuilderBase unitStatsBuilder = UnitStats.builder();
                unitStatsBuilder.withBedrooms((double) bedrooms);
                unitStatsBuilder.withPropertyType(AdModel.PropertyTypeModel.APARTMENT);

                if (area > 0) {
                    unitStatsBuilder.withAreaRangeSqm(area);
                }

                if (price > 0) {
                    PriceModel priceModel = PriceModel.builder()
                            .withValue(price)
                            .withCurrency("AED")
                            .build();
                    unitStatsBuilder.withPriceMin(priceModel);
                }

                unitStatsList.add(unitStatsBuilder.build());

            } catch (Exception e) {
                log.warn("Error extracting unit stats: {}", e.getMessage(), e);
            }
        }

        return unitStatsList;
    }

    /**
     * Extracts amenities from the document.
     */
    private List<AmenityModel> extractAmenities(Document doc) {
        List<AmenityModel> amenities = new ArrayList<>();

        // Look for amenities section
        Elements amenitiesSection = doc.select(".amenities, section:containsOwn(Amenities)");
        if (!amenitiesSection.isEmpty()) {
            Elements amenityElements = amenitiesSection.select("img + *");
            for (Element amenityElement : amenityElements) {
                String amenityName = amenityElement.text().trim();
                if (!amenityName.isEmpty()) {
                    AmenityModel amenity = AmenityModel.builder()
                            .withLabel(amenityName)
                            .build();
                    amenities.add(amenity);
                }
            }
        }

        // If no amenities found in the section, try to find them elsewhere
        if (amenities.isEmpty()) {
            Elements possibleAmenities = doc.select("img[src*=facility]");
            for (Element img : possibleAmenities) {
                String alt = img.attr("alt");
                if (alt == null || alt.isEmpty()) {
                    Element nextElement = img.nextElementSibling();
                    if (nextElement != null) {
                        alt = nextElement.text().trim();
                    }
                }

                if (alt != null && !alt.isEmpty()) {
                    AmenityModel amenity = AmenityModel.builder()
                            .withLabel(alt)
                            .build();
                    amenities.add(amenity);
                }
            }
        }

        return amenities;
    }

    /**
     * Extracts brochures from the document.
     */
    private List<BrochureModel> extractBrochures(Document doc) {
        List<BrochureModel> brochures = new ArrayList<>();

        // Look for brochure links
        Elements brochureLinks = doc.select("a:containsOwn(brochure), a:containsOwn(Brochure)");
        for (Element link : brochureLinks) {
            String href = link.attr("href");
            if (href != null && !href.isEmpty()) {
                try {
                    URI brochureUri = new URI(href);
                    BrochureModel brochure = BrochureModel.builder()
                            .withUrl(brochureUri)
                            .build();
                    brochures.add(brochure);
                } catch (URISyntaxException e) {
                    log.warn("Invalid brochure URI: {}", href, e);
                }
            }
        }

        return brochures;
    }

    /**
     * Extracts and saves developer information to the database.
     *
     * @param developerName The name of the developer
     * @param developerUrn The URN of the developer
     * @param doc The parsed HTML document
     */
    private void extractAndSaveDeveloper(String developerName, String developerUrn, Document doc) {
        if (developerName == null || developerName.isEmpty() || developerName.equals("Unknown Developer")) {
            log.warn("Developer name is null, empty, or unknown. Skipping developer extraction.");
            return;
        }

        try {
            // Check if developer already exists
            if (developerRepository.existsById(developerUrn)) {
                log.debug("Developer {} already exists, skipping", developerName);
                return;
            }

            // Create developer model
            DeveloperModel.DeveloperModelBuilderBase developerBuilder = DeveloperModel.builder()
                    .withDeveloperUrn(developerUrn)
                    .withSourceUrn(SOURCE_URN)
                    .withTitle(developerName);

            // Extract developer logo if available
            Elements logoElements = doc.select("img[src*=developer]");
            if (!logoElements.isEmpty()) {
                String logoSrc = logoElements.first().attr("src");
                if (logoSrc != null && !logoSrc.isEmpty()) {
                    developerBuilder.withLogoUrl(logoSrc);
                }
            }

            // Extract developer website if available
            String developerSlug = slugify(developerName);
            String developerUrl = BASE_URL + "/developer/" + developerSlug + "/";
            developerBuilder.withWebsite(developerUrl);

            // Build developer model
            DeveloperModel developerModel = developerBuilder.build();

            // Create and save developer record
            DeveloperRecord record = new DeveloperRecord();
            record.setUrn(developerUrn);
            record.setData(developerModel);
            record.setEmittedAt(ZonedDateTime.now());

            developerRepository.save(record);
            log.info("Successfully saved developer: {}", developerName);

        } catch (Exception e) {
            log.error("Error extracting and saving developer {}: {}", developerName, e.getMessage(), e);
        }
    }

    /**
     * Converts a string to a slug format (lowercase, hyphens instead of spaces, alphanumeric only).
     *
     * @param input The string to convert to a slug
     * @return The slug version of the input string
     */
    private String slugify(String input) {
        if (input == null || input.isEmpty()) {
            return "unknown";
        }

        // Convert to lowercase
        String slug = input.toLowerCase();

        // Replace spaces and special characters with hyphens
        slug = slug.replaceAll("[^a-z0-9]+", "-");

        // Remove leading and trailing hyphens
        slug = slug.replaceAll("^-|-$", "");

        return slug;
    }
}
