package com.realmond.temporal_service.crawler.err;

public sealed class CrawlerException extends RuntimeException permits NonRetryableCrawlerException, RetryableCrawlerException {
    public CrawlerException(String message) {
        super(message);
    }

    public CrawlerException(String message, Throwable cause) {
        super(message, cause);
    }

    public CrawlerException(Throwable cause) {
        super(cause);
    }

    public CrawlerException(
          String message,
          Throwable cause,
          boolean enableSuppression,
          boolean writableStackTrace
    ) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
