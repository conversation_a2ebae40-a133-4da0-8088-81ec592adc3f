version: "3.5"

services:
  postgresql:
    container_name: temporal-postgresql
    image: postgres:14
    environment:
      POSTGRES_DB: temporal
      POSTGRES_USER: temporal
      POSTGRES_PASSWORD: temporal
    ports:
      - "5433:5432"
    networks:
      - temporal-network
    volumes:
      - pgdata:/var/lib/postgresql/data

  temporal:
    container_name: temporal
    image: temporalio/auto-setup:1.25.1
    depends_on:
      - postgresql
    environment:
      # IMPORTANT: Tells Temporal auto-setup we're using the Postgres plugin
      - DB=postgres12
      - DB_HOST=host.docker.internal
      # Must match the *container's* Postgres port
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=postgresql
      - TEMPORAL_CONFIG_FILE_PATH=/etc/temporal/temporal.yaml
      # - SKIP_DEFAULT_CONFIG=true  # Only if you prefer to skip built-in config entirely
    ports:
      - "7233:7233"   # Expose Temporal on host:7233
    volumes:
      - ./temporal.yaml:/etc/temporal/temporal.yaml
    networks:
      - temporal-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  temporal-ui:
    container_name: temporal-ui
    image: temporalio/ui:latest
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233  # Container → Container using service name + port
    # Map container port 8080 to host port 8082 (or choose any free port)
    ports:
      - "8082:8080"
    networks:
      - temporal-network

  redis:
    container_name: redis
    image: redis:7.0
    ports:
      - "6000:6379"
    networks:
      - temporal-network
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes

networks:
  temporal-network:

volumes:
  pgdata:
  redis-data:
