// eslint-disable-next-line filenames/no-index
import * as k8s from '@pulumi/kubernetes';
import * as pulumi from '@pulumi/pulumi';

import {getBaseTags, resolveImageVersion} from './utils';
import {WorkerConfig} from "./config";
import {Worker} from "./worker";

const baseTags = getBaseTags();
const pulumiConfig = new pulumi.Config();
// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const kubeconfig = pulumiConfig.getSecret('kubeconfig')!;
const provider = new k8s.Provider('k8s-provider', {kubeconfig});

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const workerConfig = pulumiConfig.getSecretObject<WorkerConfig>('config')!.apply((config) => ({
  ...config,
  imageVersion: resolveImageVersion(config.imageVersion ?? 'latest'),
}));

export const worker = pulumi.all([baseTags, workerConfig]).apply(([tags, config]) => {
  const worker = new Worker(
    'workflow-worker',
    {
      tags: {
        ...tags,
        'realmond.com/app': 'workflow-worker'
      },
      config,
      appName: 'workflow-worker',
    },
    {provider, ignoreChanges: ['tags', 'metadata.labels']}
  );
  return worker.getOutput();
});
