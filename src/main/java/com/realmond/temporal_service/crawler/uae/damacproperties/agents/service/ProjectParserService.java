package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Page;
import com.realmond.etl.model.ProjectModel;

import java.util.List;

/**
 * Interface for parsing project information from Damac Properties
 * Using Playwright for web scraping
 */
public interface ProjectParserService {

    /**
     * Parse projects from the current page
     *
     * @param page The Playwright page object
     * @param waitTimeSeconds The time to wait for elements to load
     * @return List of ProjectModel objects
     */
    List<ProjectModel> parseProjectsFromCurrentPage(Page page, int waitTimeSeconds);

    /**
     * Parse a specific project from the page
     *
     * @param page The Playwright page object
     * @param projectName The name of the project to parse
     * @return ProjectModel object or null if not found
     */
    ProjectModel parseProject(Page page, String projectName);

    /**
     * Save projects to the database
     *
     * @param projects List of ProjectModel objects to save
     * @return true if successful, false otherwise
     */
    boolean saveProjects(List<ProjectModel> projects);
}
