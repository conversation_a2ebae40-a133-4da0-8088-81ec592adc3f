package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.KeyboardModifier;
import com.realmond.etl.model.AdModel.ConstructionStatusModel;
import com.realmond.etl.model.AmenityModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.ProjectModel;
import com.realmond.etl.model.ProjectStats;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.config.S3Config.S3Properties;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ProjectRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.ProjectRepository;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.BrowserManager;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.ImageStorageService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.ProjectParserService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.UrnGeneratorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Implementation of ProjectParserService for Damac Properties using Playwright
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DamacProjectParserService implements ProjectParserService {

    /**
     * Helper class to store project data during extraction
     */
    private static class ProjectData {
        String title;
        String description;
        String projectName;
        String productName;
        String acd;
        String externalId;
        String projectSlug;
        String projectUrn;
        LocalDate completionDate;
        ConstructionStatusModel status;
        String virtualTour;
        List<ImageModel> images = new ArrayList<>();
        List<AmenityModel> amenities = new ArrayList<>();
        List<String> unitTypes = new ArrayList<>();
        List<Map<String, String>> paymentPlans = new ArrayList<>();
        List<URI> urls = new ArrayList<>();
        Map<String, Object> additionalData = new HashMap<>();
        ProjectStats projectStats = new ProjectStats();
    }

    private final BrowserManager browserManager;
    private final UrnGeneratorService urnGeneratorService;
    private final ProjectRepository projectRepository;
    private final ImageStorageService imageStorageService;
    private final S3Properties s3Properties;

    private static final String DEVELOPER_URN = "urn:realmond:developer:damac-properties";
    private static final String SOURCE_URN = "urn:source:agents.damacproperties.com";

    /**
     * Handles any modal dialogs that might be present on the page and blocking clicks
     * @param page The Playwright page object
     */
    private void handleModalDialogs(Page page) {
        try {
            // Try to detect if any modal is visible using JavaScript with expanded selectors
            Object modalResult = page.evaluate("() => {\n" +
                    "  const modals = document.querySelectorAll('.modal.in, .modal.show, .popup, #myModal, .modal-backdrop, .modal[style*=\"display: block\"], div[role=\"dialog\"], .overlay, .fade.in');\n" +
                    "  return modals.length > 0;\n" +
                    "}");

            boolean hasVisibleModal = modalResult instanceof Boolean && (Boolean) modalResult;

            if (hasVisibleModal) {
                log.info("Detected visible modal, attempting to close or remove it");

                // First try clicking any close buttons with expanded selectors
                List<ElementHandle> closeButtons = page.querySelectorAll(
                    "button.close, .modal-header .close, .popup-close, .btn-close, " +
                    "[data-dismiss='modal'], .close-button, button:has-text('Close'), " +
                    "button:has-text('Cancel'), button:has-text('X'), [aria-label='Close']"
                );

                if (!closeButtons.isEmpty()) {
                    for (ElementHandle closeButton : closeButtons) {
                        try {
                            // Try both regular click and JavaScript click for reliability
                            try {
                                closeButton.click(new ElementHandle.ClickOptions().setForce(true).setTimeout(2000));
                            } catch (Exception e) {
                                // If direct click fails, try JS click
                                page.evaluate("(element) => element.click()", closeButton);
                            }
                            page.waitForTimeout(1000); // Increased wait time
                        } catch (Exception e) {
                            log.debug("Could not click close button: {}", e);
                        }
                    }
                }

                // Try pressing Escape key to close modals
                try {
                    page.keyboard().press("Escape");
                    page.waitForTimeout(1000);
                } catch (Exception e) {
                    log.debug("Could not press Escape key: {}", e.getMessage());
                }

                // Check if modal is still visible
                Object stillVisibleResult = page.evaluate("() => {\n" +
                        "  const modals = document.querySelectorAll('.modal.in, .modal.show, .popup, #myModal, .modal-backdrop, .modal[style*=\"display: block\"], div[role=\"dialog\"], .overlay, .fade.in');\n" +
                        "  return modals.length > 0;\n" +
                        "}");

                boolean modalStillVisible = stillVisibleResult instanceof Boolean && (Boolean) stillVisibleResult;

                // If modals still exist, use JavaScript to forcibly remove them with more aggressive approach
                if (modalStillVisible) {
                    log.info("Modal still visible after clicking close buttons, forcibly removing via JavaScript");
                    page.evaluate("() => {" +
                            "  const modalSelectors = ['.modal', '.popup', '#myModal', '.modal-backdrop', '.overlay', '.fade.in', 'div[role=\"dialog\"]'];" +
                            "  modalSelectors.forEach(selector => {" +
                            "    document.querySelectorAll(selector).forEach(modal => {" +
                            "      modal.style.display = 'none';" +
                            "      modal.classList.remove('in', 'show', 'fade', 'active');" +
                            "      modal.setAttribute('aria-hidden', 'true');" +
                            "      try { modal.remove(); } catch(e) { console.error('Could not remove modal:', e); }" +
                            "    });" +
                            "  });" +
                            "  document.querySelectorAll('.modal-backdrop, .modal-overlay, .backdrop, .overlay').forEach(backdrop => {" +
                            "    try { backdrop.remove(); } catch(e) { console.error('Could not remove backdrop:', e); }" +
                            "  });" +
                            "  document.body.classList.remove('modal-open');" +
                            "  document.body.style.overflow = '';" +
                            "  document.body.style.paddingRight = '';" +
                            "  document.documentElement.style.overflow = '';" +
                            "}");

                    page.waitForTimeout(1000); // Increased wait time
                }
            }

            // Handle specific texts we know appear in modals
            ElementHandle licenseRenewalModal = page.querySelector("div:has-text('Kindly renew the Trade Licence.')");
            if (licenseRenewalModal != null) {
                log.info("Found license renewal modal, attempting to close it");
                page.evaluate("() => {\n" +
                        "  const elements = document.querySelectorAll('div:has-text(\"Kindly renew the Trade Licence.\")');\n" +
                        "  elements.forEach(el => {\n" +
                        "    // Go up the DOM to find the modal container\n" +
                        "    let parent = el;\n" +
                        "    while (parent && !parent.classList.contains('modal')) {\n" +
                        "      parent = parent.parentElement;\n" +
                        "    }\n" +
                        "    if (parent) {\n" +
                        "      parent.style.display = 'none';\n" +
                        "      parent.classList.remove('in', 'show');\n" +
                        "      try { parent.remove(); } catch(e) { console.error('Could not remove parent:', e); }\n" +
                        "    }\n" +
                        "  });\n" +
                        "}");
                page.waitForTimeout(1000); // Increased wait time
            }

            // Handle any cookie consent or alert dialogs
            try {
                ElementHandle consentDialog = page.querySelector("[id*='cookie'], [class*='cookie'], [id*='consent'], [class*='consent'], [class*='alert'], .popup");
                if (consentDialog != null) {
                    log.info("Found potential consent/alert dialog, attempting to close");
                    page.evaluate("(element) => {\n" +
                            "  const closeButtons = element.querySelectorAll('button, .close, [aria-label=\"close\"], .btn');\n" +
                            "  if (closeButtons.length > 0) {\n" +
                            "    closeButtons[0].click();\n" +
                            "  } else {\n" +
                            "    element.style.display = 'none';\n" +
                            "    try { element.remove(); } catch(e) { console.error('Could not remove element:', e); }\n" +
                            "  }\n" +
                            "}", consentDialog);
                    page.waitForTimeout(1000);
                }
            } catch (Exception e) {
                log.debug("Error handling consent dialog: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.warn("Error handling modal dialogs: {}", e.getMessage());
        }
    }

    /**
     * Safely clicks an element, handling any modals that appear during the process
     * @param page The Playwright page object
     * @param element The element to click
     * @param options Click options
     * @return true if click was successful, false otherwise
     */
    private boolean safeClick(Page page, ElementHandle element, ElementHandle.ClickOptions options) {
        int maxAttempts = 5;  // Increased attempts

        // Ensure the element is still attached to the DOM
        try {
            if (element.evaluate("el => el.isConnected") == Boolean.FALSE) {
                log.warn("Element is not attached to the DOM");
                return false;
            }
        } catch (Exception e) {
            log.warn("Error checking if element is attached: {}", e.getMessage());
            return false;
        }

        // First try scrolling to the element to make sure it's in view
        try {
            page.evaluate("(element) => {\n" +
                    "  element.scrollIntoView({behavior: 'smooth', block: 'center'});\n" +
                    "}", element);
            page.waitForTimeout(1000);
        } catch (Exception e) {
            log.debug("Could not scroll to element: {}", e.getMessage());
        }

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            try {
                // Handle any modals that might be blocking
                handleModalDialogs(page);

                if (attempt > 0) {
                    log.info("Trying alternative click method for attempt {}", attempt + 1);

                    // Increase wait time with each retry
                    page.waitForTimeout(1000 * (attempt + 1));

                    if (attempt == 1) {
                        // Try regular click with force and increased timeout
                        ElementHandle.ClickOptions forceOptions = new ElementHandle.ClickOptions()
                            .setForce(true)
                            .setTimeout(5000);

                        element.click(forceOptions);
                    } else if (attempt == 2) {
                        // Try JavaScript click
                        page.evaluate("(element) => {\n" +
                                "  element.click();\n" +
                                "}", element);
                    } else if (attempt == 3) {
                        // Try dispatchEvent method
                        page.evaluate("(element) => {\n" +
                                "  const evt = document.createEvent('MouseEvents');\n" +
                                "  evt.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n" +
                                "  element.dispatchEvent(evt);\n" +
                                "}", element);
                    } else {
                        // Last attempt - try to simulate both mousedown and mouseup events
                        page.evaluate("(element) => {\n" +
                                "  // Create and dispatch mousedown event\n" +
                                "  const mousedown = new MouseEvent('mousedown', {\n" +
                                "    bubbles: true,\n" +
                                "    cancelable: true,\n" +
                                "    view: window\n" +
                                "  });\n" +
                                "  element.dispatchEvent(mousedown);\n" +
                                "  \n" +
                                "  // Create and dispatch mouseup event\n" +
                                "  const mouseup = new MouseEvent('mouseup', {\n" +
                                "    bubbles: true,\n" +
                                "    cancelable: true,\n" +
                                "    view: window\n" +
                                "  });\n" +
                                "  element.dispatchEvent(mouseup);\n" +
                                "  \n" +
                                "  // Final click attempt\n" +
                                "  element.click();\n" +
                                "}", element);
                    }
                } else {
                    // First attempt - regular click
                    if (options != null) {
                        // If options are provided, use them but ensure timeout is set
                        ElementHandle.ClickOptions enrichedOptions = options.setTimeout(5000);
                        element.click(enrichedOptions);
                    } else {
                        // Default click with timeout
                        element.click(new ElementHandle.ClickOptions().setTimeout(5000));
                    }
                }

                // Wait for any potential navigation or state change
                page.waitForTimeout(2000);
                return true;

            } catch (Exception e) {
                log.warn("Click attempt {} failed: {}", attempt + 1, e.getMessage());

                // Check if the element is still attached and visible
                try {
                    boolean isAttached = element.evaluate("el => el.isConnected") == Boolean.TRUE;
                    boolean isVisible = element.evaluate("el => {\n" +
                            "  const style = window.getComputedStyle(el);\n" +
                            "  return style.display !== 'none' && style.visibility !== 'hidden' && el.offsetParent !== null;\n" +
                            "}") == Boolean.TRUE;

                    if (!isAttached) {
                        log.warn("Element is no longer attached to the DOM");
                        return false;
                    }

                    if (!isVisible) {
                        log.warn("Element is not visible");
                        // Try to make it visible
                        page.evaluate("(element) => {\n" +
                                "  element.style.display = 'block';\n" +
                                "  element.style.visibility = 'visible';\n" +
                                "}", element);
                    }
                } catch (Exception checkError) {
                    log.debug("Error checking element state: {}", checkError.getMessage());
                }

                // Try to handle any modals that might have appeared
                handleModalDialogs(page);
            }
        }

        log.error("All click attempts failed");
        return false;
    }

    @Override
    public List<ProjectModel> parseProjectsFromCurrentPage(Page page, int waitTimeSeconds) {
        log.info("Starting to parse projects from current page with wait time {} seconds", waitTimeSeconds);
        List<ProjectModel> projects = new ArrayList<>();
        Set<String> parsedProjects = new HashSet<>();

        try {
            // Handle any initial modals
            handleModalDialogs(page);

            // Wait for table to be visible with increased timeout
            page.waitForSelector("table#projectSearchResultId > tbody > tr",
                new Page.WaitForSelectorOptions().setTimeout(waitTimeSeconds * 2000));

            // Get all the rows (each project takes 2 rows)
            List<ElementHandle> rows = page.querySelectorAll("table#projectSearchResultId > tbody > tr");
            log.info("Found {} rows in the project table", rows.size());

            for (int i = 0; i < rows.size(); i += 2) {
                if (i + 1 >= rows.size()) {
                    // Skip if we don't have a details row
                    continue;
                }

                int retryCount = 0;
                boolean success = false;

                while (!success && retryCount < 3) {
                    try {
                        // Re-query the rows in case the DOM has changed
                        if (retryCount > 0) {
                            log.info("Retrying project row {} (attempt {})", i, retryCount + 1);

                            // Handle modals and re-fetch rows
                            handleModalDialogs(page);
                            rows = page.querySelectorAll("table#projectSearchResultId > tbody > tr");

                            if (i + 1 >= rows.size()) {
                                log.warn("Row no longer exists after refresh");
                                break;
                            }
                        }

                        ElementHandle row = rows.get(i);

                        // Get project name
                        ElementHandle projectNameElement = row.querySelector("td:nth-child(4)");
                        if (projectNameElement == null) {
                            log.warn("Could not find project name element for row {}, skipping", i);
                            break;
                        }

                        String projectName = projectNameElement.textContent().trim();
                        log.debug("Processing project: {}", projectName);

                        // Skip if already processed
                        if (parsedProjects.contains(projectName)) {
                            log.debug("Project {} already processed, skipping", projectName);
                            success = true;
                            break;
                        }

                        // Get product name and ACD before expanding
                        ElementHandle productNameElement = row.querySelector("td:nth-child(3) > a");
                        String productName = productNameElement != null ? productNameElement.textContent().trim() : null;

                        // Click on the row to expand details - use safe click
                        safeClick(page, row, null);
                        page.waitForTimeout(2000); // Increased wait for details to expand

                        // Get the details div
                        ElementHandle detailsDiv = null;
                        try {
                            // Try with ElementHandle first
                            detailsDiv = rows.get(i+1).querySelector("td:first-child > div");
                        } catch (Exception e) {
                            // Fallback to direct page query if ElementHandle fails
                            log.debug("Fallback to direct page query for details div");
                            detailsDiv = page.querySelector(String.format("table#projectSearchResultId > tbody > tr:nth-child(%d) > td:first-child > div", i+2));
                        }

                        if (detailsDiv == null) {
                            log.warn("Could not find details div for project {}, retrying", projectName);
                            retryCount++;
                            continue;
                        }

                        String detailsDivId = detailsDiv.getAttribute("id");
                        if (detailsDivId == null || detailsDivId.isEmpty()) {
                            log.warn("Details div has no ID for project {}, retrying", projectName);
                            retryCount++;
                            continue;
                        }
                        log.debug("Details div ID: {}", detailsDivId);

                        // Get additional details for ACD
                        String additionalDetailsSelector = String.format(
                                "div#%s > div.unitSearchAccordianContent > div.row > div:nth-child(2) > div.unitSearchAccordianRight > div.childRowHeader > div.row",
                                detailsDivId);

                        // Try different approaches to get the ACD
                        String acd = null;
                        try {
                            ElementHandle acdElement = page.querySelector(
                                    String.format("%s > div:nth-child(2) > div:nth-child(3) > div.childRowBox", additionalDetailsSelector));

                            if (acdElement != null) {
                                acd = acdElement.textContent().trim();
                                if (acd.startsWith("ACD")) {
                                    acd = acd.substring(3).trim(); // Remove "ACD" prefix
                                }
                            }
                        } catch (Exception e) {
                            log.debug("Error getting ACD: {}", e.getMessage());
                        }

                        // Get the view project details button - with more flexible selector
                        ElementHandle viewProjectDetailsButton = null;

                        // Try multiple selectors for the button
                        String[] buttonSelectors = {
                            String.format("div#%s > div > div.childRowFooter.childRowFooterAccordian.notranslate > button", detailsDivId),
                            String.format("div#%s button:has-text('View Project Details')", detailsDivId),
                            String.format("div#%s button.viewProjectBtn", detailsDivId),
                            String.format("div#%s .childRowFooter button", detailsDivId)
                        };

                        for (String selector : buttonSelectors) {
                            try {
                                viewProjectDetailsButton = page.querySelector(selector);
                                if (viewProjectDetailsButton != null) {
                                    log.debug("Found view project button with selector: {}", selector);
                                    break;
                                }
                            } catch (Exception e) {
                                log.debug("Error with selector {}: {}", selector, e.getMessage());
                            }
                        }

                        if (viewProjectDetailsButton == null) {
                            log.warn("Could not find view project details button for project {}, retrying", projectName);
                            retryCount++;
                            continue;
                        }

                        // Save current page and URL for returning
                        String currentUrl = page.url();
                        Page projectPage = null;
                        int openTabsBeforeClick = page.context().pages().size();

                        try {
                            // Click the view project details button to open in new tab - use safe click with ALT key
                            ElementHandle.ClickOptions clickOptions = new ElementHandle.ClickOptions()
                                .setModifiers(List.of(KeyboardModifier.ALT))
                                .setForce(true)
                                .setTimeout(10000);

                            // Make sure no modals are present before clicking
                            handleModalDialogs(page);
                            boolean clickSuccess = safeClick(page, viewProjectDetailsButton, clickOptions);

                            if (!clickSuccess) {
                                // Try alternative approach - using JavaScript for middle click
                                log.info("ALT+Click failed, trying JavaScript middle click simulation");

                                page.evaluate("(element) => {\n" +
                                    "  // Simulate middle click - this often opens a link in a new tab\n" +
                                    "  const evt = document.createEvent('MouseEvents');\n" +
                                    "  evt.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, true, false, false, false, 1, null);\n" +
                                    "  element.dispatchEvent(evt);\n" +
                                    "}", viewProjectDetailsButton);

                                page.waitForTimeout(2000); // Wait for potential new tab

                                clickSuccess = page.context().pages().size() > openTabsBeforeClick;
                            }

                            if (!clickSuccess) {
                                // Final approach - JavaScript to open in new tab
                                log.info("Mouse clicks failed, trying JavaScript to open new tab");
                                page.evaluate("(element) => {\n" +
                                    "  const url = element.getAttribute('data-url') || element.getAttribute('href');\n" +
                                    "  if (url) {\n" +
                                    "    window.open(url, '_blank');\n" +
                                    "  } else {\n" +
                                    "    // Try to find URL in onclick attribute\n" +
                                    "    const onclickAttr = element.getAttribute('onclick');\n" +
                                    "    if (onclickAttr && onclickAttr.includes('window.open')) {\n" +
                                    "      // Extract URL and execute\n" +
                                    "      const match = onclickAttr.match(/window\\.open\\(['\"]([^'\"]+)['\"]/);\n" +
                                    "      if (match && match[1]) {\n" +
                                    "        window.open(match[1], '_blank');\n" +
                                    "      }\n" +
                                    "    }\n" +
                                    "  }\n" +
                                    "}", viewProjectDetailsButton);
                            }

                            // Wait for new tab to open (increased timeout)
                            page.waitForTimeout(5000);

                            // Get the new tab - wait for a new page to be created
                            List<Page> pages = page.context().pages();
                            int openTabsAfterClick = pages.size();

                            if (openTabsAfterClick > openTabsBeforeClick) {
                                // Get the last page (new tab)
                                projectPage = pages.get(pages.size() - 1);

                                // Wait for project details page to load
                                projectPage.waitForLoadState();
                                projectPage.waitForSelector("section.projectDetailsMainSection",
                                        new Page.WaitForSelectorOptions().setTimeout(waitTimeSeconds * 2000));

                                // Parse project details
                                ProjectModel project = parseProjectDetails(projectPage, acd, productName, projectName);
                                if (project != null) {
                                    projects.add(project);
                                    parsedProjects.add(projectName);
                                    log.info("Successfully parsed project: {}", projectName);
                                    success = true;
                                }
                            } else {
                                log.warn("No new tab opened for project {}, retrying", projectName);
                                retryCount++;
                                continue;
                            }
                        } catch (Exception e) {
                            log.error("Error parsing project {}: {}", projectName, e.getMessage(), e);
                            retryCount++;
                            continue;
                        } finally {
                            // Close the project details tab if it was opened
                            if (projectPage != null) {
                                try {
                                    projectPage.close();
                                } catch (Exception e) {
                                    log.debug("Error closing project page: {}", e.getMessage());
                                }
                            }

                            // Return to the original page if needed
                            if (!page.url().equals(currentUrl)) {
                                try {
                                    page.navigate(currentUrl);
                                    page.waitForLoadState();

                                    // Handle any modals that might appear after navigation
                                    handleModalDialogs(page);
                                } catch (Exception e) {
                                    log.debug("Error returning to original page: {}", e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error processing project row {}: {}", i, e.getMessage(), e);
                        retryCount++;
                    }
                }

                if (!success && retryCount >= 3) {
                    log.error("Failed to process project row {} after {} attempts", i, retryCount);
                }
            }

            log.info("Finished parsing projects, found {} projects", projects.size());
            return projects;

        } catch (Exception e) {
            log.error("Error parsing projects: {}", e.getMessage(), e);
            return projects;
        }
    }

    /**
     * Parse project details from the project details page
     */
    private ProjectModel parseProjectDetails(Page page, String acd, String productName, String projectName) {
        log.debug("Parsing project details for {}", projectName);

        try {
            // Initialize project data object
            ProjectData data = new ProjectData();
            data.acd = acd;
            data.productName = productName;
            data.projectName = projectName;

            // Extract basic project information
            extractProjectTitle(page, data);
            extractProjectDescription(page, data);
            extractProjectImages(page, data);

            // Extract additional project information
            extractUnitTypes(page, data);
            extractLocation(page, data);
            extractProjectStatus(page, data);

            // Extract dates and coordinates
            extractCompletionDate(data);
            extractCoordinates(page, data);
            extractVirtualTour(page, data);
            extractAmenities(page, data);

            // Prepare project metadata
            prepareProjectStats(data);
            generateProjectIdentifiers(data);
            prepareAdditionalData(page, data);

            // Set external ID from page URL
            data.externalId = page.url();

            // Create URLs list
            createProjectUrls(data);

            // Build and return the project model
            ProjectModel project = buildProjectModel(data);

            log.debug("Successfully built ProjectModel with URN: {}", project.getProjectUrn());

            return project;

        } catch (Exception e) {
            log.error("Error parsing project details: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract project title from the page
     */
    private void extractProjectTitle(Page page, ProjectData data) {
        ElementHandle titleElement = page.querySelector("#projectDetailsMainSectionId > div.topProjectNameBox > h1");
        data.title = titleElement != null ? titleElement.textContent().trim() : data.projectName;
        log.debug("Extracted project title: {}", data.title);
    }

    /**
     * Extract project description from the page
     */
    private void extractProjectDescription(Page page, ProjectData data) {
        ElementHandle aboutElement = page.querySelector("#aboutSection > div.aboutContent");
        data.description = aboutElement != null ? aboutElement.textContent().trim() : null;
        log.debug("Extracted project description: {}", data.description != null ?
                data.description.substring(0, Math.min(50, data.description.length())) + "..." : "null");
    }

    /**
     * Extract project images from the page
     */
    private void extractProjectImages(Page page, ProjectData data) {
        List<ElementHandle> imageElements = page.querySelectorAll("#myCarousel .item img.img-responsive");
        Map<String, URI> originalUrls = new HashMap<>();
        log.debug("Found {} image elements using selector '#myCarousel .item img.img-responsive'", imageElements.size());

        for (ElementHandle img : imageElements) {
            String src = img.getAttribute("src");
            if (src != null && !src.isEmpty()) {
                try {
                    ImageModel image = new ImageModel();
                    URI uri = new URI(src);
                    image.setUrl(uri);
                    data.images.add(image);
                    originalUrls.put(src, uri);
                    log.debug("Added image: {}", src);
                } catch (Exception e) {
                    log.warn("Error parsing image URL: {}", src, e);
                }
            }
        }
        log.debug("Extracted {} project images", data.images.size());
    }

    /**
     * Parse coordinates from the page
     */
    private CoordinatesModel parseCoordinates(Page page) {
        log.debug("Parsing coordinates...");
        try {
            // Look for map initialization script
            List<ElementHandle> scripts = page.querySelectorAll("body > script");
            for (ElementHandle script : scripts) {
                String scriptContent = script.textContent();
                if (scriptContent.contains("setView")) {
                    // Use regex to extract coordinates
                    Pattern pattern = Pattern.compile("setView\\(\\[([0-9.]*),([0-9.]*)\\]");
                    Matcher matcher = pattern.matcher(scriptContent);
                    if (matcher.find() && matcher.group(1) != null && !matcher.group(1).isEmpty()) {
                        double lat = Double.parseDouble(matcher.group(1));
                        double lng = Double.parseDouble(matcher.group(2));

                        log.debug("Found coordinates: lat={}, lng={}", lat, lng);

                        CoordinatesModel coordinates = new CoordinatesModel();
                        coordinates.setLat(lat);
                        coordinates.setLng(lng);
                        return coordinates;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error parsing coordinates: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Extract unit types from the page
     */
    private void extractUnitTypes(Page page, ProjectData data) {
        List<ElementHandle> unitTypeElements = page.querySelectorAll(
                "#pageId\\:formId\\:wrapperdetaillist > div.detailContent > div > div:nth-child(1) > div.detailContentBox > h5");
        for (ElementHandle unitType : unitTypeElements) {
            data.unitTypes.add(unitType.textContent().trim());
        }
        log.debug("Extracted {} unit types", data.unitTypes.size());
    }

    /**
     * Extract location from the page
     */
    private void extractLocation(Page page, ProjectData data) {
        ElementHandle locationElement = page.querySelector(
                "#pageId\\:formId\\:wrapperdetaillist > div.detailContent > div > div:nth-child(2) > div.detailContentBox > h5");
        String location = locationElement != null ? locationElement.textContent().trim() : null;
        if (data.productName != null && location != null) {
            location = data.productName + ", " + location;
        }

        // Store location in additional data
        if (location != null) {
            Map<String, Object> locationData = new HashMap<>();
            locationData.put("address", location);
            locationData.put("city", "Dubai");
            locationData.put("state", "Dubai");
            locationData.put("country", "UAE");
            data.additionalData.put("location", locationData);
        }
        log.debug("Extracted location: {}", location);
    }

    /**
     * Extract project status from the page
     */
    private void extractProjectStatus(Page page, ProjectData data) {
        ElementHandle statusElement = page.querySelector(
                "#pageId\\:formId\\:wrapperdetaillist > div.detailContent > div > div:nth-child(3) > div.detailContentBox > h5");
        String statusText = statusElement != null ? statusElement.textContent().trim() : null;
        data.status = ConstructionStatusModel.ACTIVE; // Default to active
        if (statusText != null && statusText.equalsIgnoreCase("Ready")) {
            data.status = ConstructionStatusModel.FINISHED;
        }
        log.debug("Extracted project status: {}", data.status);
    }

    /**
     * Parse virtual tour URL from the page
     */
    private String parseVirtualTour(Page page) {
        log.debug("Parsing virtual tour URL...");
        try {
            ElementHandle iframeElement = page.querySelector("iframe#info");
            if (iframeElement != null) {
                String src = iframeElement.getAttribute("src");
                if (src != null && !src.isEmpty() && !src.contains("https://agents.damacproperties.com")) {
                    log.debug("Found virtual tour URL: {}", src);
                    return src;
                }
            }
        } catch (Exception e) {
            log.warn("Error parsing virtual tour URL: {}", e.getMessage());
        }
        return null;
    }

    /**
     * Extract completion date from the project data
     */
    private void extractCompletionDate(ProjectData data) {
        if (data.acd != null && !data.acd.isEmpty()) {
            try {
                // Parse date in format DD/MM/YYYY
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                data.completionDate = LocalDate.parse(data.acd, formatter);
                log.debug("Extracted completion date: {}", data.completionDate);
            } catch (DateTimeParseException e) {
                log.warn("Could not parse completion date: {}", data.acd, e);
            }
        }
    }

    /**
     * Extract coordinates from the page
     */
    private void extractCoordinates(Page page, ProjectData data) {
        CoordinatesModel coordinates = parseCoordinates(page);
        if (coordinates != null) {
            // Add coordinates to location data if it exists
            Map<String, Object> locationData = (Map<String, Object>) data.additionalData.get("location");
            if (locationData != null) {
                locationData.put("coordinates", coordinates);
            }
            log.debug("Added coordinates to location data: {}, {}", coordinates.getLat(), coordinates.getLng());
        }
    }

    /**
     * Extract virtual tour from the page
     */
    private void extractVirtualTour(Page page, ProjectData data) {
        String virtualTourUrl = parseVirtualTour(page);
        if (virtualTourUrl != null) {
            try {
                data.virtualTour = virtualTourUrl;
                log.debug("Extracted virtual tour URL: {}", data.virtualTour);
            } catch (Exception e) {
                log.warn("Error parsing virtual tour URL: {}", virtualTourUrl, e);
            }
        }
    }

    /**
     * Extract amenities from the page
     */
    private void extractAmenities(Page page, ProjectData data) {
        data.amenities.addAll(parseProjectAmenities(page));
        data.amenities.addAll(parseUnitAmenities(page));
        log.debug("Extracted {} total amenities", data.amenities.size());
    }

    /**
     * Parse project amenities from the page
     */
    private List<AmenityModel> parseProjectAmenities(Page page) {
        log.debug("Parsing project amenities...");
        List<AmenityModel> amenities = new ArrayList<>();

        try {
            // REMOVED: Click on project details tab - Python version doesn't do this
            // The tab should already be active when this method is called.

            List<ElementHandle> amenityElements = page.querySelectorAll(
                    "#projectDetails > div.projectDetailContainer > div.projectDetailContainerBox");

            for (int i = 0; i < amenityElements.size(); i++) {
                try {
                    String imgSelector = String.format(
                            "#projectDetails > div > div:nth-child(%d) > img", i + 1);
                    String labelSelector = String.format(
                            "#projectDetails > div > div:nth-child(%d) > span", i + 1);

                    ElementHandle imgElement = page.querySelector(imgSelector);
                    ElementHandle labelElement = page.querySelector(labelSelector);

                    if (labelElement != null) {
                        String label = labelElement.textContent().trim();

                        AmenityModel amenity = new AmenityModel();
                        amenity.setLabel(label);

                        // Add image if available
                        if (imgElement != null) {
                            String src = imgElement.getAttribute("src");
                            if (src != null && !src.isEmpty()) {
                                try {
                                    amenity.setImg(new URI(src));
                                } catch (Exception e) {
                                    log.warn("Error parsing amenity image URL: {}", src, e);
                                }
                            }
                        }

                        amenities.add(amenity);
                        log.debug("Added project amenity: {}", label);
                    }
                } catch (Exception e) {
                    log.warn("Error parsing project amenity at index {}: {}", i, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("Error parsing project amenities: {}", e.getMessage());
        }

        return amenities;
    }

    /**
     * Parse unit amenities from the page
     */
    private List<AmenityModel> parseUnitAmenities(Page page) {
        log.debug("Parsing unit amenities...");
        List<AmenityModel> amenities = new ArrayList<>();

        try {
            // Click on unit details tab
            ElementHandle unitTab = page.querySelector("ul#addFeaUL > li:nth-child(2) > a");
            if (unitTab != null) {
                unitTab.click();
                page.waitForTimeout(500); // Short wait for tab to activate
            }

            List<ElementHandle> unitAmenityContainers = page.querySelectorAll(
                    "#unitDetails > div > div.row.projectUnitDetailsBoxContainerRow > div.projectUnitDetailsBoxContainer");

            for (int i = 0; i < unitAmenityContainers.size(); i++) {
                try {
                    List<ElementHandle> amenityItems = page.querySelectorAll(
                            String.format("#unitDetails > div > div.row.projectUnitDetailsBoxContainerRow > div:nth-child(%d) > div > ul > li", i + 1));

                    for (ElementHandle item : amenityItems) {
                        String text = item.textContent().trim();
                        if (!text.isEmpty()) {
                            AmenityModel amenity = new AmenityModel();
                            amenity.setLabel(text);
                            amenities.add(amenity);
                            log.debug("Added unit amenity: {}", text);
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error parsing unit amenities at index {}: {}", i, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("Error parsing unit amenities: {}", e.getMessage());
        }

        return amenities;
    }

    /**
     * Parse bedroom stats from the page
     */
    private Map<String, Object> parseBedroomStats(Page page) {
        log.debug("Parsing bedroom stats...");
        Map<String, Object> bedroomStats = new HashMap<>();

        try {
            List<ElementHandle> statRows = page.querySelectorAll(
                    "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr");

            List<Map<String, String>> stats = new ArrayList<>();

            for (int i = 0; i < statRows.size(); i++) {
                try {
                    String bedroomsSelector = String.format(
                            "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr:nth-child(%d) > td:first-child", i + 1);
                    String areaRangeSelector = String.format(
                            "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr:nth-child(%d) > td:nth-child(2)", i + 1);
                    String priceRangeSelector = String.format(
                            "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr:nth-child(%d) > td:nth-child(3)", i + 1);
                    String pricePerSqftSelector = String.format(
                            "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr:nth-child(%d) > td:nth-child(4)", i + 1);
                    String bedroomTypeSelector = String.format(
                            "#pageId\\:formId\\:wrapperdetaillist > div.detailTableBox > table > tbody > tr:nth-child(%d) > td:nth-child(5)", i + 1);

                    ElementHandle bedroomsElement = page.querySelector(bedroomsSelector);
                    ElementHandle areaRangeElement = page.querySelector(areaRangeSelector);
                    ElementHandle priceRangeElement = page.querySelector(priceRangeSelector);
                    ElementHandle pricePerSqftElement = page.querySelector(pricePerSqftSelector);
                    ElementHandle bedroomTypeElement = page.querySelector(bedroomTypeSelector);

                    if (bedroomsElement != null) {
                        Map<String, String> stat = new HashMap<>();
                        stat.put("bedrooms", bedroomsElement.textContent().trim());

                        if (areaRangeElement != null) {
                            stat.put("area_range_sqft", areaRangeElement.textContent().trim());
                        }

                        if (priceRangeElement != null) {
                            stat.put("price_range_sqft", priceRangeElement.textContent().trim());
                        }

                        if (pricePerSqftElement != null) {
                            stat.put("price_per_sqft", pricePerSqftElement.textContent().trim());
                        }

                        if (bedroomTypeElement != null) {
                            stat.put("bedroom_type", bedroomTypeElement.textContent().trim());
                        }

                        stats.add(stat);
                        log.debug("Added bedroom stat: {}", stat);
                    }
                } catch (Exception e) {
                    log.warn("Error parsing bedroom stat at index {}: {}", i, e.getMessage());
                }
            }

            if (!stats.isEmpty()) {
                bedroomStats.put("stats", stats);
            }
        } catch (Exception e) {
            log.warn("Error parsing bedroom stats: {}", e.getMessage());
        }

        return bedroomStats;
    }

    /**
     * Parse payment plan details from the page
     */
    private List<Map<String, String>> parsePaymentPlans(Page page) {
        log.debug("Parsing payment plan details...");
        List<Map<String, String>> paymentPlanInstallments = new ArrayList<>();

        try {
            // Select the container for the visible payment plan installments
            String containerSelector = "div#maxPayments"; // Container for visible plan
            ElementHandle container = page.querySelector(containerSelector);

            if (container == null) {
                log.warn("Payment plan container 'div#maxPayments' not found.");
                return paymentPlanInstallments;
            }

            // Select all individual installment boxes within the visible container
            List<ElementHandle> installmentBoxes = container.querySelectorAll("div.paymentPlanContainerBox");
            log.debug("Found {} payment plan installment boxes.", installmentBoxes.size());

            for (ElementHandle box : installmentBoxes) {
                try {
                    Map<String, String> installment = new HashMap<>();

                    // Extract Header/Title
                    ElementHandle headerElement = box.querySelector(".projectDetailContainerHeader");
                    if (headerElement != null) {
                        installment.put("title", headerElement.textContent().trim());
                    }

                    // Extract Percentage
                    ElementHandle percentageElement = box.querySelector(".projectDetailContainerBody > h3");
                    if (percentageElement != null) {
                        installment.put("percentage", percentageElement.textContent().trim());
                    }

                    // Extract Description
                    ElementHandle descriptionElement = box.querySelector(".projectDetailContainerBody > label");
                    if (descriptionElement != null) {
                        installment.put("description", descriptionElement.textContent().trim());
                    }

                    // Extract Currency
                    ElementHandle currencyElement = box.querySelector(".projectDetailContainerFooter > h5");
                    if (currencyElement != null) {
                        installment.put("currency", currencyElement.textContent().trim());
                    }

                    // Extract Amount
                    ElementHandle amountElement = box.querySelector(".projectDetailContainerFooter > h6");
                    if (amountElement != null) {
                        installment.put("amount", amountElement.textContent().trim().replace(",", "")); // Remove commas from amount
                    }

                    if (!installment.isEmpty()) {
                        paymentPlanInstallments.add(installment);
                        log.debug("Added payment plan installment: {}", installment);
                    }

                } catch (Exception e) {
                    log.warn("Error parsing individual payment plan installment box: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            log.warn("Error parsing payment plans section: {}", e.getMessage());
        }

        return paymentPlanInstallments;
    }

    @Override
    public ProjectModel parseProject(Page page, String projectName) {
        log.info("Parsing specific project: {}", projectName);
        try {
            // Handle any initial modals
            handleModalDialogs(page);

            // Wait for table to be visible
            page.waitForSelector("table#projectSearchResultId > tbody > tr");

            // Find the row with the matching project name
            List<ElementHandle> rows = page.querySelectorAll("table#projectSearchResultId > tbody > tr");

            for (int i = 0; i < rows.size(); i += 2) {
                ElementHandle row = rows.get(i);
                ElementHandle projectNameElement = row.querySelector("td:nth-child(4)");

                if (projectNameElement != null) {
                    String rowProjectName = projectNameElement.textContent().trim();

                    if (projectName.equals(rowProjectName)) {
                        log.debug("Found matching project row: {}", projectName);

                        // Use safe click instead of direct click
                        safeClick(page, row, null);
                        page.waitForTimeout(1000); // Wait for details to expand

                        // Get the details div
                        ElementHandle detailsDiv = rows.get(i+1).querySelector("td:first-child > div");
                        if (detailsDiv == null) {
                            log.warn("Could not find details div for project {}", projectName);
                            return null;
                        }

                        String detailsDivId = detailsDiv.getAttribute("id");

                        // Get the view project details button
                        ElementHandle viewProjectDetailsButton = page.querySelector(
                                String.format("div#%s > div > div.childRowFooter.childRowFooterAccordian.notranslate > button", detailsDivId));

                        if (viewProjectDetailsButton == null) {
                            log.warn("Could not find view project details button for project {}", projectName);
                            return null;
                        }

                        // Get product name and ACD before opening new tab
                        ElementHandle productNameElement = row.querySelector("td:nth-child(3) > a");
                        String productName = productNameElement != null ? productNameElement.textContent().trim() : null;

                        // Get additional details selector
                        String additionalDetailsSelector = String.format(
                                "div#%s > div.unitSearchAccordianContent > div.row > div:nth-child(2) > div.unitSearchAccordianRight > div.childRowHeader > div.row",
                                detailsDivId);

                        // Get ACD (Anticipated Completion Date)
                        ElementHandle acdElement = page.querySelector(
                                String.format("%s > div:nth-child(2) > div:nth-child(3) > div.childRowBox", additionalDetailsSelector));

                        String acd = null;
                        if (acdElement != null) {
                            acd = acdElement.textContent().trim();
                            if (acd.startsWith("ACD")) {
                                acd = acd.substring(3).trim(); // Remove "ACD" prefix
                            }
                        }

                        // Save current page and URL for returning
                        String currentUrl = page.url();
                        Page projectPage = null;

                        try {
                            // Click the view project details button to open in new tab - use safe click
                            ElementHandle.ClickOptions clickOptions = new ElementHandle.ClickOptions()
                                .setModifiers(List.of(KeyboardModifier.ALT));

                            // Make sure no modals are present before clicking
                            handleModalDialogs(page);
                            boolean clickSuccess = safeClick(page, viewProjectDetailsButton, clickOptions);

                            if (!clickSuccess) {
                                log.warn("Could not click view project details button for project {}", projectName);
                                return null;
                            }

                            // Get the new tab - wait for a new page to be created
                            List<Page> pages = page.context().pages();
                            if (pages.size() > 1) {
                                // Get the last page (new tab)
                                projectPage = pages.get(pages.size() - 1);

                                // Wait for project details page to load
                                projectPage.waitForLoadState();
                                projectPage.waitForSelector("section.projectDetailsMainSection");

                                // Parse project details
                                return parseProjectDetails(projectPage, acd, productName, projectName);
                            } else {
                                log.warn("No new tab opened for project {}", projectName);
                                return null;
                            }
                        } catch (Exception e) {
                            log.error("Error parsing project {}: {}", projectName, e.getMessage(), e);
                            return null;
                        } finally {
                            // Close the project details tab if it was opened
                            if (projectPage != null) {
                                projectPage.close();
                            }

                            // Return to the original page if needed
                            if (!page.url().equals(currentUrl)) {
                                page.navigate(currentUrl);
                                page.waitForLoadState();
                                // Handle any modals that might appear after navigation
                                handleModalDialogs(page);
                            }
                        }
                    }
                }
            }

            log.warn("Could not find project with name: {}", projectName);
            return null;
        } catch (Exception e) {
            log.error("Error parsing project {}: {}", projectName, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean saveProjects(List<ProjectModel> projects) {
        log.info("Saving {} projects to database", projects.size());
        int successCount = 0;

        for (ProjectModel project : projects) {
            try {
                // Upload images to S3 if present
                if (project.getImages() != null && !project.getImages().isEmpty()) {
                    List<ImageModel> uploadedImages = project.getImages().stream()
                        .map(imageStorageService::uploadImage)
                        .collect(Collectors.toList());

                    // Filter out any images that failed to upload
                    List<ImageModel> successfulUploads = uploadedImages.stream()
                        .filter(img -> img.getUrl() != null && img.getUrl().toString().contains(s3Properties.getBucket()))
                        .collect(Collectors.toList());

                    log.info("Successfully uploaded {} out of {} images to S3",
                            successfulUploads.size(), project.getImages().size());

                    if (!successfulUploads.isEmpty()) {
                        project.setImages(successfulUploads);

                        // Set the first uploaded image as cover image
                        if (!successfulUploads.isEmpty()) {
                            project.setCoverImage(successfulUploads.get(0));
                        }
                    }
                }

                // Create and save project record
                ProjectRecord record = new ProjectRecord();
                record.setUrn(project.getProjectUrn());
                record.setData(project);
                record.setEmittedAt(ZonedDateTime.now());

                projectRepository.save(record);
                successCount++;
                log.debug("Successfully saved project: {}", project.getTitle());
            } catch (Exception e) {
                log.error("Error saving project {}: {}", project.getProjectUrn(), e.getMessage(), e);
            }
        }

        log.info("Successfully saved {}/{} projects", successCount, projects.size());
        return successCount > 0;
    }

    /**
     * Prepare project stats
     */
    private void prepareProjectStats(ProjectData data) {
        if (data.completionDate != null) {
            data.projectStats.setCompletionDate(data.completionDate.toString());
        }
        log.debug("Prepared project stats");
    }

    /**
     * Generate project identifiers (URN and slug)
     */
    private void generateProjectIdentifiers(ProjectData data) {
        // Generate project URN
        data.projectUrn = generateProjectUrn(data.projectName);

        // Create project slug
        data.projectSlug = slugify(data.projectName);

        log.debug("Generated project identifiers: urn={}, slug={}", data.projectUrn, data.projectSlug);
    }

    /**
     * Prepare additional data for the project
     */
    private void prepareAdditionalData(Page page, ProjectData data) {
        // Add ACD to additional data
        if (data.acd != null) {
            data.additionalData.put("acd", data.acd);
        }

        // Add unit types to additional data
        if (!data.unitTypes.isEmpty()) {
            data.additionalData.put("unit_types", data.unitTypes);
        }

        // Add bedroom stats to additional data
        Map<String, Object> bedroomStats = parseBedroomStats(page);
        if (!bedroomStats.isEmpty()) {
            data.additionalData.put("bedroom_stats", bedroomStats);
        }

        // Add payment plan stats to additional data
        List<Map<String, String>> paymentPlans = parsePaymentPlans(page);
        if (!paymentPlans.isEmpty()) {
            data.additionalData.put("payment_plan_installments", paymentPlans);
            data.paymentPlans = paymentPlans;
        }

        log.debug("Prepared additional data with {} entries", data.additionalData.size());
    }

    /**
     * Create project URLs list
     */
    private void createProjectUrls(ProjectData data) {
        try {
            data.urls.add(new URI(data.externalId));
            log.debug("Added URL to project: {}", data.externalId);
        } catch (Exception e) {
            log.warn("Error parsing URL: {}", data.externalId, e);
        }
    }

    /**
     * Build the project model from the collected data
     */
    private ProjectModel buildProjectModel(ProjectData data) throws URISyntaxException {
        // Build the project model
        @SuppressWarnings("unchecked")
        ProjectModel project = ProjectModel.builder()
                .withTitle(data.title)
                .withDescription(data.description)
                .withProjectSlug(data.projectSlug)
                .withDeveloperUrn(DEVELOPER_URN)
                .withProjectUrn(data.projectUrn)
                .withSourceUrn(SOURCE_URN)
                .withExternalId(data.externalId)
                .withProjectStatus(data.status)
                .withCurrency("AED")
                .withUrls(data.urls)
                .withAdditionalData(data.additionalData)
                .build();

        // Set images if available
        if (!data.images.isEmpty()) {
            project.setImages(data.images);

            // Set first image as cover image
            project.setCoverImage(data.images.get(0));
        }

        // Set virtual tour if available
        if (data.virtualTour != null) {
            project.setVirtualTour(new URI(data.virtualTour));
        }

        // Set amenities if available
        if (!data.amenities.isEmpty()) {
            project.setAmenities(data.amenities);
        }

        // Set project stats if available
        if (data.projectStats != null) {
            project.setProjectStats(data.projectStats);
        }

        return project;
    }

    /**
     * Generate project URN following Python logic
     */
    private String generateProjectUrn(String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            log.warn("Project name is null or empty, using default URN");
            return "urn:realmond:project:damac-properties:unknown";
        }
        String slug = slugify(projectName);
        String urn = "urn:realmond:project:damac-properties:" + slug;
        log.debug("Generated project URN: {} from name: {}", urn, projectName);
        return urn;
    }

    /**
     * Slugify text following Python logic
     */
    private String slugify(String text) {
        if (text == null) return "";

        // Apply replacements similar to what the service does
        String result = text.toLowerCase()
            .replace("@", "at")
            .replace("&", "and")
            .replace("%", "percent")
            .replace("|", "or");

        // Remove special characters and replace spaces with hyphens
        result = result.replaceAll("[^a-z0-9\\s-]", "")
            .replaceAll("\\s+", "-")
            .replaceAll("-+", "-");

        return result.trim();
    }

    /**
     * CoordinatesModel representing latitude and longitude
     */
    public static class CoordinatesModel {
        private double lat;
        private double lng;

        public double getLat() {
            return lat;
        }

        public void setLat(double lat) {
            this.lat = lat;
        }

        public double getLng() {
            return lng;
        }

        public void setLng(double lng) {
            this.lng = lng;
        }
    }
}
