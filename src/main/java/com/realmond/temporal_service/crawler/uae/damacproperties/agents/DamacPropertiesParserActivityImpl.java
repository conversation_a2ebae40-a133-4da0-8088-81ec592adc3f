package com.realmond.temporal_service.crawler.uae.damacproperties.agents;

import com.realmond.etl.model.AdModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.AdRepository;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.CrawlingService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.PropertyParserService;
import com.realmond.temporal_service.parser.damacproperties.DamacPropertiesParserActivity;
import io.temporal.activity.Activity;
import io.temporal.spring.boot.ActivityImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Implementation of DamacPropertiesParserActivity.
 * Uses existing DamacProperties crawler components to fetch and parse property data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ActivityImpl(taskQueues = {"property-parsing-task-queue", "damac-parsing-task-queue"})
public class DamacPropertiesParserActivityImpl implements DamacPropertiesParserActivity {

    @Qualifier("propertiesCrawler")
    private final CrawlingService damacCrawlingService;
    private final PropertyParserService propertyParserService;
    private final AdRepository adRepository;

    @Override
    public List<String> fetchAllPages() {
        log.info("fetchAllPages called for DamacProperties - using parseAllPages instead");
        // For DamacProperties, we use the single activity approach instead of pagination
        // This method is implemented for interface compatibility
        return Collections.emptyList();
    }

    @Override
    public List<AdModel> parsePage(String pageIdentifier) {
        log.info("parsePage called for DamacProperties - using parseAllPages instead");
        // For DamacProperties, we use the single activity approach instead of pagination
        // This method is implemented for interface compatibility
        return Collections.emptyList();
    }

    @Override
    public List<AdModel> parseAllPages() {
        log.info("Starting to parse all DamacProperties properties");
        List<AdModel> allProperties = new ArrayList<>();
        AtomicInteger count = new AtomicInteger(0);
        AtomicInteger retryCount = new AtomicInteger(0);
        final int MAX_RETRIES = 15; // Up to 15 retries at the activity level

        boolean success = false;
        Exception lastException = null;

        while (!success && retryCount.get() < MAX_RETRIES) {
            try {
                // Report heartbeat to Temporal with retry information
                Activity.getExecutionContext().heartbeat("Starting DamacProperties crawl (attempt " + (retryCount.get() + 1) + ")");

                if (retryCount.get() > 0) {
                    log.info("Retrying DamacProperties crawl (attempt {})", retryCount.get() + 1);
                    // Add exponential backoff between retries
                    long sleepTime = Math.min(30000, 1000 * (long) Math.pow(2, retryCount.get()));
                    Thread.sleep(sleepTime);
                }

                // Start the crawling process
                damacCrawlingService.crawlProperties();

                // If we get here without exception, the crawl was successful
                success = true;
                log.info("DamacProperties crawl completed successfully on attempt {}. Properties were saved directly to the repository.",
                        retryCount.get() + 1);

            } catch (Exception e) {
                lastException = e;
                retryCount.incrementAndGet();

                // Report heartbeat to prevent activity timeout during retries
                Activity.getExecutionContext().heartbeat("DamacProperties crawl failed (attempt " + retryCount.get() + ")");

                log.error("Error during DamacProperties parsing (attempt {}): {}",
                        retryCount.get(), e.getMessage());

                if (retryCount.get() >= MAX_RETRIES) {
                    log.error("Maximum retry attempts ({}) reached for DamacProperties crawl", MAX_RETRIES);
                    // Let the exception propagate to trigger workflow-level retry
                    throw new RuntimeException("Failed to parse DamacProperties after " + MAX_RETRIES + " attempts", e);
                }
            }
        }

        // If we've exhausted all retries and still failed, throw an exception
        if (!success) {
            throw new RuntimeException("Failed to parse DamacProperties after " + MAX_RETRIES + " attempts", lastException);
        }

        return allProperties;
    }
}
