## v1.1.0 (2025-03-13)

### Feat

- Revert "bump: version 1.0.0 → 1.1.0"
- **ci**: configure remote in after_script
- **ci**: configure remote in after_script
- add cz config
- test semver

## v1.0.0 (2025-03-13)

### Feat

- add cz config
- test semver
- add semver bumping plugin
- add reactive http client
- add default reactive translate strategy

### Fix

- use 1 page when fetching entities to translate
- add activity hook for location contentType that triggers restoration of parent links
- add sequential translation strategy to keep consistent translation of hierarchical entities(that have recursive relation it itself)
- use 1st page when querying entities to translate
- do activity heartbeats in the current thread
- check if the found workflow execution is running
