package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

/**
 * Interface for web crawling services.
 * Provides a standard contract for all crawling implementations.
 */
public interface CrawlingService {

    /**
     * Starts the crawling process.
     * This method manages the crawling lifecycle, including handling scheduling and concurrency.
     */
    void startCrawling();

    /**
     * Performs the actual crawling operation.
     * This is the main method that contains the crawling logic.
     */
    void crawlProperties();
}
