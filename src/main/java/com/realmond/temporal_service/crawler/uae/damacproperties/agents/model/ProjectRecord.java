package com.realmond.temporal_service.crawler.uae.damacproperties.agents.model;

import com.realmond.etl.model.ProjectModel;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.ZonedDateTime;

@Entity
//@Table(name = "raw_projects", schema = "strapi", indexes = @Index(name = "idx_damac_agents_raw_projects_emitted_at_idx", columnList = "emitted_at"))
@Table(name = "raw_projects")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRecord {
    @Id
    private String urn;

    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private ProjectModel data;

    @Column(name = "emitted_at")
    private ZonedDateTime emittedAt;
}
