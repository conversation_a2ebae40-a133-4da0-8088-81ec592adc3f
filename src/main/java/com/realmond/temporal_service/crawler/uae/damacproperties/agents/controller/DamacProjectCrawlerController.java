package com.realmond.temporal_service.crawler.uae.damacproperties.agents.controller;

import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.CrawlingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for managing Damac Projects crawler operations.
 * Provides endpoints to trigger crawling manually.
 */
@Slf4j
@RestController
@RequestMapping("/api/crawler/damac-projects")
public class DamacProjectCrawlerController {

    private final CrawlingService crawlingService;

    public DamacProjectCrawlerController(@Qualifier("projectsCrawler") CrawlingService crawlingService) {
        this.crawlingService = crawlingService;
    }

    /**
     * Endpoint to trigger the crawler manually via the startCrawling method.
     * This method follows the same pattern as the scheduled execution.
     *
     * @return ResponseEntity with status and message
     */
    @PostMapping("/trigger")
    public ResponseEntity<String> triggerCrawler() {
        log.info("Manual project crawler trigger requested");
        try {
            crawlingService.startCrawling();
            return ResponseEntity.ok("Project crawler triggered successfully");
        } catch (Exception e) {
            log.error("Error triggering project crawler: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Failed to trigger project crawler: " + e.getMessage());
        }
    }

    /**
     * Endpoint to check the status of the crawler.
     *
     * @return ResponseEntity with status message
     */
    @GetMapping("/status")
    public ResponseEntity<String> getStatus() {
        // This is a simple status endpoint that could be enhanced with actual status information
        return ResponseEntity.ok("Project crawler service is running");
    }
}
