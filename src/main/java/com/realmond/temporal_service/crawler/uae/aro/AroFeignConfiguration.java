package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import feign.RequestInterceptor;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.realmond.temporal_service.crawler.rate_limit.BucketRateLimiter;
import io.github.bucket4j.distributed.proxy.ProxyManager;

import java.util.Random;

/**
 * Feign configuration for ARO.ae API client.
 * Includes browser fingerprinting, custom headers, and retry logic.
 */
@Slf4j
@Configuration
public class AroFeignConfiguration {

    private final FingerprintGenerator fingerprintGenerator;
    private final Random random = new Random();
    private final AroSettings settings;
    private final BucketRateLimiter rateLimiter;

    public AroFeignConfiguration(FingerprintGenerator fingerprintGenerator, AroSettings settings, ObjectProvider<ProxyManager<String>> proxyManagerProvider) {
        this.fingerprintGenerator = fingerprintGenerator;
        this.settings = settings;
        // Get the ProxyManager if available, otherwise null (will fall back to in-memory rate limiting)
        ProxyManager<String> proxyManager = proxyManagerProvider.getIfAvailable();
        this.rateLimiter = new BucketRateLimiter(settings.getRateLimiter(), proxyManager);
    }

    /**
     * Request interceptor that adds browser-like headers with fingerprinting.
     */
    @Bean
    public RequestInterceptor aroRequestInterceptor() {
        return template -> {
            // Apply client-level rate limiting (blocks until a token is available)
            rateLimiter.consume(AroSettings.SOURCE_URN);

            fingerprintGenerator.applyBrowserHeaders(template);
            addRandomDelay();
        };
    }

    /**
     * Custom retryer with exponential backoff.
     */
    @Bean
    public Retryer aroRetryer() {
        return new Retryer.Default(
                settings.getRetry().getRetryDelayMs(),
                settings.getRetry().getMaxPeriodMs(),
                settings.getRetry().getMaxRetries());
    }

    /**
     * Adds a random delay to make requests look more natural.
     */
    private void addRandomDelay() {
        try {
            int delay = random.nextInt(settings.getRetry().getRandomDelayCapMs());
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Request delay interrupted", e);
        }
    }
}
