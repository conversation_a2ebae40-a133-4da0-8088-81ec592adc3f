package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.realmond.etl.model.ImageModel;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for handling image storage operations
 */
public interface ImageStorageService {

    /**
     * Upload an image to S3 if it hasn't been uploaded before
     *
     * @param imageModel The image model containing the URL to upload
     * @return The updated image model with S3 URL
     */
    ImageModel uploadImage(ImageModel imageModel);

    /**
     * Asynchronously upload a list of images to S3
     *
     * @param imageModels The list of image models to upload
     * @return A CompletableFuture that will be completed when all uploads are done
     */
    CompletableFuture<List<ImageModel>> uploadImagesAsync(List<ImageModel> imageModels);

    /**
     * Check if an image has already been uploaded
     *
     * @param originalUrl The original image URL
     * @return true if the image has been uploaded, false otherwise
     */
    boolean isImageUploaded(String originalUrl);

    /**
     * Get the S3 URL for an already uploaded image
     *
     * @param originalUrl The original image URL
     * @return The S3 URL if found, or null if the image hasn't been uploaded
     */
    String getS3Url(String originalUrl);
}
