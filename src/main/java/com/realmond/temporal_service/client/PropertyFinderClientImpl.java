package com.realmond.temporal_service.client;

import com.microsoft.playwright.*;
import com.realmond.temporal_service.crawler.fingerprint.BrowserFingerprint;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Random;

@Slf4j
@Component
public class PropertyFinderClientImpl implements PropertyFinderClient {

    private static final String BASE_URL = "https://www.propertyfinder.ae";
    private static final String PROJECTS_LIST_URL = BASE_URL + "/en/new-projects?page=";
    private static final int DEFAULT_TIMEOUT_MS = 30000;

    @Value("${playwright.headless:true}")
    private boolean headless;

    private final FingerprintGenerator fingerprintGenerator;

    public PropertyFinderClientImpl(FingerprintGenerator fingerprintGenerator) {
        this.fingerprintGenerator = fingerprintGenerator;
    }

    @Override
    public String fetchProjectsList(int pageNumber) {
        String url = PROJECTS_LIST_URL + pageNumber;
        log.info("Fetching projects list from URL: {}", url);
        return fetchPageContent(url);
    }

    @Override
    public String fetchProjectDetail(String shareUrl) {
        String url = BASE_URL + shareUrl;
        log.info("Fetching project details from URL: {}", url);
        return fetchPageContent(url);
    }

    private String fetchPageContent(String url) {
        try (Playwright playwright = Playwright.create()) {
            // Generate a random fingerprint
            BrowserFingerprint fingerprint = fingerprintGenerator.generateRandomFingerprint();
            log.info("Using fingerprint: {}", fingerprint);

            // Configure browser with anti-detection measures
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setArgs(Arrays.asList(
                            "--disable-blink-features=AutomationControlled",
                            "--disable-features=IsolateOrigins,site-per-process"
                    ));

            Browser browser = playwright.chromium().launch(launchOptions);

            try {
                // Create browser context with the fingerprint
                Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                        .setUserAgent(fingerprint.getUserAgent())
                        .setViewportSize(fingerprint.getViewportWidth(), fingerprint.getViewportHeight())
                        .setLocale(fingerprint.getLanguage().split(",")[0])
                        .setDeviceScaleFactor(fingerprint.getPixelRatio())
                        .setTimezoneId("UTC")
                        .setHasTouch(false);

                // Create a new browser context with our options
                BrowserContext context = browser.newContext(contextOptions);

                // Add JavaScript to override navigator properties
                context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
                context.addInitScript("Object.defineProperty(navigator, 'platform', { get: () => '" + fingerprint.getPlatform() + "' });");

                // Create a new page in this context
                Page page = context.newPage();
                page.setDefaultTimeout(DEFAULT_TIMEOUT_MS);

                // Add random delays to simulate human behavior
                int randomDelay = 1000 + new Random().nextInt(2000);
                Thread.sleep(randomDelay);

                // Navigate to the URL and wait for the page to load
                page.navigate(url);
                page.waitForLoadState();

                // Add another random delay before looking for elements
                Thread.sleep(randomDelay);

                // Wait for the main content to be loaded with increased timeout
                page.waitForSelector("script#__NEXT_DATA__",
                        new Page.WaitForSelectorOptions().setTimeout(DEFAULT_TIMEOUT_MS * 2));

                // Get the page content
                String content = page.content();

                // Clean up
                page.close();
                context.close();

                return content;
            } finally {
                browser.close();
            }
        } catch (Exception e) {
            log.error("Error fetching content from URL: {}", url, e);
            throw new RuntimeException("Failed to fetch content from URL: " + url, e);
        }
    }
}
