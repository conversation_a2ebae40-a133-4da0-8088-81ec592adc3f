package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.uae.aro.model.Amenity;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitStats;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service for enriching project data with additional information from ARO.ae API.
 * Demonstrates usage of the Feign client for various endpoints.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AroEnrichmentService {

    private final AroFeignRestClient aroFeignRestClient;

    /**
     * Enriches project detail with amenities and unit statistics.
     * @param projectDetail The base project detail
     * @return Enriched project information
     */
    public EnrichedProjectInfo enrichProjectDetail(ProjectDetail projectDetail) {
        log.info("Enriching project detail for: {}", projectDetail.getSlug());

        // Fetch amenities
        List<Amenity> amenities = fetchProjectAmenities(projectDetail.getId());

        // Fetch unit statistics
        List<UnitStats> unitStats = fetchUnitStatistics(projectDetail.getId());

        // Fetch building details (measurement in sqft)
        List<Building> buildings = fetchBuildingDetails(projectDetail.getId());

        // Build enriched info (brochure logic removed)
        return EnrichedProjectInfo.builder()
                .projectDetail(projectDetail)
                .amenities(amenities)
                .unitStats(unitStats)
                .buildings(buildings)
                .build();
    }

    /**
     * Fetches amenities for a project.
     * @param projectId Project ID
     * @return List of amenities
     */
    public List<Amenity> fetchProjectAmenities(int projectId) {
        try {
            log.debug("Fetching amenities for project ID: {}", projectId);
            return aroFeignRestClient.getProjectAmenities(projectId);
        } catch (Exception e) {
            log.warn("Failed to fetch amenities for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetches unit statistics for a project.
     * @param projectId Project ID
     * @return List of unit statistics
     */
    public List<UnitStats> fetchUnitStatistics(int projectId) {
        try {
            log.debug("Fetching unit statistics for project ID: {}", projectId);
            return aroFeignRestClient.getUnitStats(projectId, 1, 50); // Get all unit types
        } catch (Exception e) {
            log.warn("Failed to fetch unit statistics for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetches building details for a project.
     *
     * @param projectId Project ID
     * @return List of buildings (can be empty when API returns none)
     */
    public List<Building> fetchBuildingDetails(int projectId) {
        try {
            log.debug("Fetching building details for project ID: {}", projectId);
            // measurement type set to sqft by default as used in Postman collection
            return aroFeignRestClient.getProjectBuildings(projectId, "sqft");
        } catch (Exception e) {
            log.warn("Failed to fetch building details for project {}: {}", projectId, e.getMessage());
            return List.of();
        }
    }

    /**
     * Fetch building details for a project.
     */
    @lombok.Data
    @lombok.Builder
    public static class EnrichedProjectInfo {
        private ProjectDetail projectDetail;
        private List<Amenity> amenities;
        private List<UnitStats> unitStats;
        private List<Building> buildings;
    }
} 