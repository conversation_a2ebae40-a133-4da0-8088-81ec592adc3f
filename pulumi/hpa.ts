import * as pulumi from "@pulumi/pulumi";
import * as k8s from "@pulumi/kubernetes";
import {Tags} from "./utils";

type CreateHPAArgs = {
    appName: string;
    namespace: pulumi.Output<string>;
    tags: Tags;
}

export function createHorizontalPodAutoscaler(name: string, args: CreateHPAArgs, opts: pulumi.ComponentResourceOptions): k8s.autoscaling.v2.HorizontalPodAutoscaler {
    let fullName = `${args.appName}-${name}`;
    return new k8s.autoscaling.v2.HorizontalPodAutoscaler(name, {
        apiVersion: "autoscaling/v2",
        kind: "HorizontalPodAutoscaler",
        metadata: {
            name: fullName,
            namespace: args.namespace,
            labels: {
                ...args.tags,
                component: name,
                release: args.appName
            }
        },
        spec: {
            scaleTargetRef: {
                apiVersion: "apps/v1",
                kind: "Deployment",
                name: fullName
            },
            minReplicas: 1,
            maxReplicas: 10,
            metrics: [
                {
                    type: "Resource",
                    resource: {
                        name: "memory",
                        target: {
                            type: "Utilization",
                            averageUtilization: 65
                        }
                    }
                },
                {
                    type: "Resource",
                    resource: {
                        name: "cpu",
                        target: {
                            type: "Utilization",
                            averageUtilization: 50
                        }
                    }
                }
            ],
            behavior: {
                scaleUp: {
                    stabilizationWindowSeconds: 0,
                    policies: [
                        {
                            type: "Pods",
                            value: 2,
                            periodSeconds: 15
                        }
                    ]
                },
                scaleDown: {
                    stabilizationWindowSeconds: 300,
                    policies: [
                        {
                            type: "Pods",
                            value: 1,
                            periodSeconds: 60
                        }
                    ]
                }
            }
        }
    }, opts);
}
