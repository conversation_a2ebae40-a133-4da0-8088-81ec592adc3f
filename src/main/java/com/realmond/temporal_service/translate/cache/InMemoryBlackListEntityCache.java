package com.realmond.temporal_service.translate.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * An in-memory thread-safe cache component that stores String keys and Set<Long> values.
 * The cache will be cleared upon application restart or every 24 hours.
 */
@Component
@Slf4j
@Profile("!redis-black-list-cache")
public class InMemoryBlackListEntityCache implements BlackListEntityCache {


    private final ConcurrentMap<String, Set<Long>> cacheMap;

    /**
     * Constructs a new, empty cache.
     */
    public InMemoryBlackListEntityCache() {
        this.cacheMap = new ConcurrentHashMap<>();
        log.info("Initialized in-memory black list entity cache");
    }

    @Override
    public Set<Long> get(String key) {
        return cacheMap.get(key);
    }

    @Override
    public void addToSet(String key, Long value) {
        cacheMap.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet()).add(value);
        log.info("Added value {} to key {}", value, key);
    }

    @Override
    public boolean removeFromSet(String key, Long value) {
        Set<Long> set = cacheMap.get(key);
        if (set != null) {
            boolean removed = set.remove(value);
            if (removed) {
                log.info("Removed value {} from key {}", value, key);
                // If the set becomes empty after removal, remove the key from the map
                if (set.isEmpty()) {
                    cacheMap.remove(key);
                    log.info("Removed key {} as its Set became empty", key);
                }
                return true;
            }
        }
        return false;
    }

    @Override
    public void put(String key, Set<Long> value) {
        if (value == null) {
            throw new IllegalArgumentException("Value cannot be null");
        }
        cacheMap.put(key, ConcurrentHashMap.newKeySet());
        cacheMap.get(key).addAll(value);
        log.info("Added/Updated key: {}", key);
    }

    @Override
    public Set<Long> remove(String key) {
        Set<Long> removed = cacheMap.remove(key);
        if (removed != null) {
            log.info("Removed key: {}", key);
        }
        return removed;
    }

    @Override
    public void clear() {
        cacheMap.clear();
        log.info("Cache cleared manually.");
    }

    @Override
    public int size() {
        return cacheMap.size();
    }

    @Override
    public boolean containsKey(String key) {
        return cacheMap.containsKey(key);
    }

    /**
     * Scheduled task to clear the cache every 24 hours.
     * The fixedRate is set to 24 hours in milliseconds.
     */
    @Scheduled(fixedRate = 86400000) // 24 hours in milliseconds
    public void scheduledCacheClear() {
        cacheMap.clear();
        log.info("Cache cleared automatically by scheduled task.");
    }

}
