package com.realmond.temporal_service.crawler.uae.damacproperties.agents.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.ZonedDateTime;

/**
 * Entity for tracking uploaded images to avoid duplicates
 */
@Entity
@Table(name = "image_records")
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ImageRecord {

    /**
     * The original image URL used as primary key
     */
    @Id
    private String originalUrl;

    /**
     * The S3 URL where the image has been uploaded
     */
    private String s3Url;

    /**
     * The timestamp when the image was uploaded
     */
    @Column(name = "uploaded_at")
    private ZonedDateTime uploadedAt;

    /**
     * The status of the upload
     */
    @Enumerated(EnumType.STRING)
    private UploadStatus status;

    /**
     * Optional error message if upload failed
     */
    @Column(length = 1000)
    private String errorMessage;

    public enum UploadStatus {
        PENDING,
        COMPLETED,
        FAILED
    }
}
