package com.realmond.temporal_service.translate.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Builder
@Data
public class SingleTranslateRequest {

    @JsonProperty("contentType")
    private String contentType;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("sourceLocale")
    private String sourceLocale;

    @JsonProperty("targetLocale")
    private String targetLocale;

    @JsonProperty("autoPublish")
    private Boolean autoPublish;

    public SingleTranslateRequest(String contentType, Long id, String sourceLocale, String targetLocale, Boolean autoPublish) {
        this.contentType = contentType;
        this.id = id;
        this.sourceLocale = sourceLocale;
        this.targetLocale = targetLocale;
        this.autoPublish = autoPublish;
    }
}
