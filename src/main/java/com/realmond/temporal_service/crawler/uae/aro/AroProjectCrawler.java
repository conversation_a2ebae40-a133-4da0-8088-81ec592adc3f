package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.IntStream;

/**
 * Implementation of ARO Crawler.
 * Uses existing ARO parser components to fetch and parse project data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AroProjectCrawler implements Crawler<ProjectModel> {

    private final AroApiService aroApiService;

    @Override
    public String getSourceUrn() {
        return AroSettings.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return true;
    }

    @Override
    public List<String> fetchAllPageIds() {
        int pagesCount = aroApiService.fetchProjectPagesCount();
        return IntStream.range(1, pagesCount + 1).boxed().map(Object::toString).toList();
    }

    @Override
    public List<CrawlRecord<ProjectModel>> parsePage(String pageNum) {
        log.info("Parsing ARO.ae page with ID: {}", pageNum);
        int page;
        try {
            page = Integer.parseInt(pageNum);
        } catch (NumberFormatException e) {
            throw new NonRetryableCrawlerException("failed to parse page number: " + pageNum, e);
        }

        return aroApiService.fetchProjectsPage(page);
    }

    @Override
    public List<CrawlRecord<ProjectModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
}
