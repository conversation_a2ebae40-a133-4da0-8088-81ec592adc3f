log:
  stdout: true
  level: info

persistence:
  defaultStore: default
  visibilityStore: postgres-visibility

  datastores:
    # 1) Main data store
    default:
      sql:
        pluginName: "postgres12"
        databaseName: "temporal"         # main DB
        connectAddr: "postgresql:5432"
        connectProtocol: "tcp"
        user: "temporal"
        password: "temporal"
        maxConns: 20
        maxIdleConns: 20
        maxConnLifetime: "1h"

    # 2) Visibility data store
    postgres-visibility:
      sql:
        pluginName: "postgres12"
        databaseName: "temporal_visibility"  # separate DB for visibility
        connectAddr: "postgresql:5432"
        connectProtocol: "tcp"
        user: "temporal"
        password: "temporal"
        maxConns: 10
        maxIdleConns: 10
        maxConnLifetime: "1h"
