package com.realmond.temporal_service.translate.client;

import com.realmond.temporal_service.config.StrapiClientConfig;
import com.realmond.temporal_service.translate.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

// Regular Feign client for synchronous methods
@FeignClient(name = "strapi", url = "${strapi.api.url}", configuration = StrapiClientConfig.class)
public interface StrapiTranslationClient {

    @PostMapping("/translation/single-translate")
    SingleTranslateResponse singleTranslate(@RequestBody SingleTranslateRequest request);

    @GetMapping("/translation/untranslated/{type}")
    UnTranslatedEntities getUntranslatedEntityIds(
            @PathVariable("type") String type,
            @RequestParam(value = "locale", defaultValue = "en") String locale,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "1000") int pageSize
    );

    @GetMapping("/translation/types")
    UntranslatedTypes getTranslatableTypes();

    @PostMapping("/location/restore-parent-links")
    LocationRestoreResponse restoreLocationParentLinks();
}

