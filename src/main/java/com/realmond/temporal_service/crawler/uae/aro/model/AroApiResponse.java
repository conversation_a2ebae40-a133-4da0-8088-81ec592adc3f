package com.realmond.temporal_service.crawler.uae.aro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Base API response wrapper for ARO.ae API responses with pagination.
 * @param <T> The type of data contained in the response
 */
@Data
public class AroApiResponse<T> {
    private List<T> data;
    private Paging paging;
    private Integer total;
    private Integer left;
    private Integer count;

    @Data
    public static class Paging {
        private Integer total;
        private Integer size;
        private Integer number;
    }
} 