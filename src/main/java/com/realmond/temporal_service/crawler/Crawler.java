package com.realmond.temporal_service.crawler;

import java.util.List;

/**
 * Defines methods for fetching and parsing data from different providers.
 */
public interface Crawler<T> {

    /**
     * Get the source URN the crawler belongs to
     * @return Source URN
     */
    String getSourceUrn();

    /**
     * Check if the crawler supports pagination
     * @return true if pagination is supported, false otherwise
     */
    <PERSON>ole<PERSON> supportsPagination();

    /**
     * Fetches all page identifiers (URLs, IDs, etc.) for pagination.
     *
     * @return List of page identifiers
     */
    List<String> fetchAllPageIds();

    /**
     * Parses a single page identified by the pageIdentifier.
     *
     * @param pageIdentifier The identifier for the page (URL, ID, etc.)
     *
     * @return List of parsed items from the page
     */
    List<CrawlRecord<T>> parsePage(String pageIdentifier);

    /**
     * Parses all pages at once (for providers that don't need pagination).
     *
     * @return List of all parsed items
     */
    List<CrawlRecord<T>> fetchAll();
}
