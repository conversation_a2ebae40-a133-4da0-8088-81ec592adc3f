<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Base appender configurations -->
    <property name="LOGS" value="./logs" />

    <!-- Console appender configuration -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{ISO8601} %highlight(%-5level) [%blue(%t)] %yellow(%C{1}): %msg%n%throwable
            </Pattern>
        </layout>
    </appender>

    <!-- File appender configuration for all logs -->
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/application.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d %p %C{1} [%t] %m%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/archived/application-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- Keep logs for 30 days -->
            <maxHistory>30</maxHistory>
            <!-- Total size cap of all log files -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Parser-specific file appender -->
    <appender name="ParserLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/parser.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/archived/parser-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Logger for property parser with dedicated file -->
    <logger name="com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserService" level="debug" additivity="false">
        <appender-ref ref="ParserLogFile" />
        <appender-ref ref="Console" />
    </logger>

    <!-- Root logger configuration -->
    <root level="info">
        <appender-ref ref="RollingFile" />
        <appender-ref ref="Console" />
    </root>

    <!-- Environment specific configurations -->
    <springProfile name="dev">
        <logger name="com.realmond.temporal_service.crawler.uae.damacproperties" level="debug" />
    </springProfile>

    <springProfile name="prod">
        <!-- In production, keep parser at INFO level to reduce log volume -->
        <logger name="com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserService" level="info" />
    </springProfile>

    <springProfile name="test">
        <!-- For testing, enable all parser logs including trace -->
        <logger name="com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl.DamacPropertyParserService" level="trace" />
        <logger name="com.realmond.temporal_service.parser" level="debug" />
    </springProfile>
</configuration>
