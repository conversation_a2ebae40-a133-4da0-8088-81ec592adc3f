{"name": "alnair: process data", "nodes": [{"parameters": {}, "id": "7e5bba81-ec7a-4552-89e4-cfd16d8d66dc", "name": "configure sync config", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [1440, -920]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "select distinct\n      ap.project_details->'developer'->>'title' as title,\n      cast(ap.project_details->'developer'->>'id' as int) as id,\n      (\n        CASE\n          WHEN (ap.project_details->'developer'->'logo') IS NULL THEN NULL\n          ELSE ap.project_details->'developer'->'logo'->>'url'\n        END\n      ) as logo_url,\n      ap.project_details->'developer'->>'site' as website,\n      ap.project_details->'developer'->>'description' as description,\n      ap.source as source\nfrom competitors.alnair_projects as ap\nwhere\n      ap.project_details->'developer'->>'title' is not null\norder by ap.project_details->'developer'->>'title' asc", "options": {}}, "id": "ae292991-2a3c-43f6-90e8-8b01589b650c", "name": "alnair developers", "type": "n8n-nodes-base.postgres", "typeVersion": 2.3, "position": [1780, -420], "output": [{"data": [{"title": "Test Developer", "id": "123", "logo_url": "https://example.com/logo.png", "website": "https://example.com", "description": "Test developer description", "source": "alnair.ae"}]}]}, {"parameters": {"jsCode": "const utils = require('@realmond/n8n-utils');\nconst synced_at = new Date().toISOString();\n\n\nreturn $input.all().map(item => {\n  const j = item.json;\n  const source = j.source;\n  const title = j.title;\n  const id = j.id;\n  const urn = `${source}:developer:${id}`;\n  const metadata = {\n    id,\n    title,\n    logo_url: j.logo_url,\n    website: j.website,\n    description: j.description\n  };\n  const sync_execution_id = $execution.id;\n\n  return {\n    metadata,\n    urn,\n    source,\n    sync_execution_id,\n    synced_at\n  };\n});"}, "id": "9ad25f05-53be-4764-8046-e41b8a038ca2", "name": "prep developer upsert", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2040, -420]}, {"parameters": {"jsCode": "const utils = require('@realmond/n8n-utils');\n\nconst default_currency = 'AED';\n\nconst updated_at = new Date().toISOString();\nconst sync_execution_id = $execution.id;\n\nfunction get_project_images(j) {\n  let photos = {};\n  if (j.project_photos?.data) {\n    const project_photos = j.project_photos.data.flatMap(pp => {\n      const alt = pp.name?.toLowerCase();\n      return pp.photos?.map(p => ({\n        url: p.url,\n        alt,\n      })) ?? [];\n    });\n    project_photos.forEach(pp => {\n      photos[pp.url] = pp;\n    });\n  }\n  if (j.project_details.albums) {\n    const albumPhotos = j.project_details.albums\n        .filter(a => a.type !== 'construction-progress')\n        .filter(a => a.photo)\n        .map(album => {\n          const alt = album.name?.toLowerCase();\n          return {url: album.photo.url, alt};\n        });\n    albumPhotos.forEach(ap => {\n      photos[ap.url] = ap;\n    });\n  }\n  if (j.project_details.presentation) {\n    const presentation_photos = j.project_details.presentation\n      .filter(p => p.videoUrl === null || p.videoUrl === undefined)\n      .map(p => ({url: p.url, alt: null}));\n    presentation_photos.forEach(pp => {\n      photos[pp.url] = pp;\n    });\n  }\n  \n  let cover_photo = undefined;\n  if (j.project_details.photo) {\n    cover_photo = {\n      url: j.project_details.photo.url,\n      alt: j.project_details.title?.toLowerCase()\n    }\n  }\n\n  return {\n    images: Object.values(photos),\n    cover_image: cover_photo\n  }\n}\n\nfunction get_videos(j) {\n  const presentation = j.project_details.presentation;\n  if (presentation && Array.isArray(presentation)) {\n    return presentation\n      .filter(p => p.videoUrl !== null && p.videoUrl !== undefined)\n      .map(({videoUrl}) => ({url: videoUrl}));\n  }\n  return [];\n}\n\nvar youtubePattern = /^(?:https?:\\/\\/)?(?:m\\.|www\\.)?(?:youtu\\.be\\/|youtube\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=))((\\w|-){11})(?:\\S+)?$/;\nfunction validateYouTubeUrl(urlToParse){\n  if (urlToParse) {\n    if (url.match(youtubePattern)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction get_brochures(j) {\n  return j.project_details.documents?.map(d => ({url: d.url, title: d.title})) ?? [];\n}\n\nfunction get_location(j) {\n  const country = j.project_details.country;\n  const city = j.project_details.city;\n  const district = j.project_details.district;\n  const address = j.project_details.address;\n  return {\n    country,\n    city,\n    district,\n    address\n  }\n}\nfunction get_project_urls(j) {\n  const urls = ['https://alnair.ae/app/view/' + j.project_id];\n  if (j.project_details.site) {\n    urls.push(j.project_details.site);\n  }\n  return urls;\n}\n\nconst alnair_property_type_dict = {\n  \"Apartment\": \"apartment\",\n  \"Duplex\": \"duplex\",\n  \"Villa in RC\": \"villa\",\n  \"Retail\": \"retail\",\n  \"Office\": \"office\",\n  \"Penthouse\": \"penthouse\",\n  \"Triplex\": \"triplex\",\n  \"Townhouse\": \"townhouse\"\n}\n\nfunction get_price_stats(j) {\n  if (!j.layouts || !j.layouts.data) {\n    return {\n      price_min: null, \n      price_max: null\n    };\n  }\n  let price_min = 1_000_000_000;\n  let price_max = -1;\n  j.layouts.data.flatMap(l => l.units)\n    .forEach(u => {\n      if (price_min > u.price_min) {\n        console.log(u.price_min)\n        price_min = u.price_min;\n      }\n      if (price_max < u.price_max) {\n        console.log(u.price_max)\n        price_max = u.price_max;\n      }\n    });\n  if (price_min === 1_000_000_000) {\n    price_min = null;\n  }\n  if (price_max === -1) {\n    price_max = null;\n  }\n  return {\n    price_min: {\n      value: price_min,\n      currency: default_currency\n    },\n    price_max: {\n      value: price_max,\n      currency: default_currency\n    }\n  }\n}\n\nfunction get_property_tyeps(j) {\n  let property_types = [];\n  if (j.layouts && j.layouts.data) {\n    const set = new Set();\n    j.layouts?.data?.map(l => l.units)\n      .flatMap(u => u.property_types)\n      .filter(pt => pt)\n      .map(pt => alnair_property_type_dict[pt])\n      .forEach(pt => set.add(pt));\n    j.layouts?.data?.flatMap(fp => {\n    if (fp.catalog_values?.village_type_house) {\n      property_type = alnair_property_type_dict[fp.catalog_values.village_type_house[0]];\n    }  \n  })\n    .filter(pt => pt)\n    .forEach(pt => set.add(pt));\n    property_types = Array.from(set);\n  }\n\n  return property_types;\n}\n\nfunction get_project_stats(j) {\n  const pd = j.project_details;\n  const property_types = get_property_tyeps(j);\n  const stats = pd.stats.total;\n  const total_no_of_residential_units = stats.unitsCount;\n  const total_area = stats.unitsSquareMt;\n  const launch_date = pd.start_at;\n  const completion_date = pd.planned_at;\n  const total_number_of_units = total_no_of_residential_units;\n  const price_stats = get_price_stats(j);\n  return {\n    property_types,\n    total_no_of_residential_units,\n    total_area,\n    launch_date,\n    completion_date,\n    retail_space: null,\n    total_number_of_units,\n    ...price_stats\n  }\n}\n\nconst alnair_project_status_dict = {\n  \"Scheduled\": \"not_started\",\n  \"Completed\": \"finished\",\n  \"Stopped\": \"stopped\",\n  \"In Progress\": \"active\"\n}\n\nfunction get_project_status(j) {\n  if (j.project_details.catalog_values && j.project_details.catalog_values.construction_phase_status) {\n    const construction_phase_status = j.project_details.catalog_values.construction_phase_status[0];\n    return alnair_project_status_dict[construction_phase_status];\n  }\n  \n  return null;\n}\n\nfunction get_amenities(j) {\n  let amenities = [];\n  const cv = j.project_details.catalog_values;\n  if (!cv) {\n    return amenities;\n  }\n  if (cv.residential_complex_advantages) {\n    amenities = amenities.concat(cv.residential_complex_advantages);\n  }\n  if (cv.village_advantages) {\n    amenities = amenities.concat(cv.village_advantages);\n  }\n  if (cv.village_apartment_advantages) {\n    amenities = amenities.concat(cv.village_apartment_advantages);\n  }\n  return amenities.map(label => ({img: null, label}));\n}\n\nfunction get_description(j) {\n  const description = j.project_details.description;\n  const photo_descriptions = new Set();\n  if (j.project_photos && j.project_photos.data) {\n    j.project_photos.data.map(pp => pp.description)\n      .forEach(photo_description => {\n        photo_descriptions.add(photo_description);\n      });\n  }\n  if (j.project_details.albums) {\n    j.project_details.albums\n      .filter(a => a.type !== 'construction-progress')\n      .map(a => a.description)\n      .forEach(album_description => {\n        photo_descriptions.add(album_description);\n      });\n  }\n  const descriptions = [description, ...photo_descriptions];\n\n  return descriptions.join('\\n\\n');\n}\n\nfunction get_coordinates(j) {\n  const lat = j.project_details.latitude;\n  const lng = j.project_details.longitude;\n  return {lat, lng};\n}\nfunction get_polygon(j) {\n  const polygon = j.project_details.polygon;\n  if (!polygon) {\n    return null;\n  }\n  return JSON.parse(polygon).map(([lng, lat]) => ({lng, lat}));\n}\n\nreturn $input.all().map(item => {\n  const j = item.json;\n  const project_title = j.project_details.title;\n  \n  const {images, cover_image} = get_project_images(j);\n  const source = j.source;\n  const developer_id = j.project_details.developer.id;\n  const project_slug = utils.slugify(project_title);\n  const project_id = j.project_id;\n  const project_urn = `${source}:project:${project_id}`;\n  const developer_urn = `${source}:developer:${developer_id}`;\n\n  const additional_data = {};\n  \n  if (j.project_details.dld_project_number) {\n    additional_data['dld'] = {\n      project_number: j.project_details.dld_project_number\n    }\n  }\n  const metadata =  {\n    title: project_title,\n    project_slug,\n    external_id: j.project_id,\n    brochures: get_brochures(j),\n    images,\n    cover_image,\n    videos: get_videos(j),\n    description: get_description(j),\n    location: get_location(j),\n    urls: get_project_urls(j),\n    source_urn: j.source,\n    project_stats: get_project_stats(j),\n    project_status: get_project_status(j),\n    amenities: get_amenities(j),\n    coordinates: get_coordinates(j),\n    polygon: get_polygon(j),\n    project_urn,\n    developer_urn,\n    additional_data\n  };\n  const synced_at = j.synced_at;\n\n  return {\n    metadata,\n    urn: project_urn,\n    source: j.source,\n    sync_execution_id,\n    updated_at,\n    synced_at\n  }\n});"}, "id": "1273b0ea-d769-4a9a-8139-e4efa8789335", "name": "prepare project", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2780, 300], "output": [{"data": [{"metadata": {"title": "Test Project", "project_slug": "test-project", "external_id": "456", "brochures": [{"url": "https://example.com/brochure.pdf", "title": "Project Brochure"}], "images": [{"url": "https://example.com/image1.jpg", "alt": "Project Image 1"}], "cover_image": {"url": "https://example.com/cover.jpg", "alt": "Project Cover"}, "videos": [{"url": "https://example.com/video.mp4"}], "description": "Test project description", "location": {"country": "United Arab Emirates", "city": "Dubai", "district": "Downtown", "address": "123 Test Street"}, "urls": ["https://alnair.ae/app/view/456", "https://example.com/project"], "source_urn": "alnair.ae", "project_stats": {"property_types": ["apartment", "villa"], "total_no_of_residential_units": 100, "total_area": 10000, "launch_date": "2023-01-01", "completion_date": "2025-01-01", "retail_space": null, "total_number_of_units": 100, "price_min": {"value": 1000000, "currency": "AED"}, "price_max": {"value": 5000000, "currency": "AED"}}, "project_status": "active", "amenities": [{"img": null, "label": "Swimming Pool"}, {"img": null, "label": "Gym"}], "coordinates": {"lat": 25.123456, "lng": 55.123456}, "polygon": [{"lat": 25.123456, "lng": 55.123456}, {"lat": 25.123457, "lng": 55.123457}], "project_urn": "alnair.ae:project:456", "developer_urn": "alnair.ae:developer:123", "additional_data": {"dld": {"project_number": "DLD123"}}}, "urn": "alnair.ae:project:456", "source": "alnair.ae", "sync_execution_id": 123, "updated_at": "2023-01-01T00:00:00Z", "synced_at": "2023-01-01T00:00:00Z"}]}]}]}