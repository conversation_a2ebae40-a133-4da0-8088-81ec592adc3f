# ARO.ae Parser

This is a parser for extracting property project data from ARO.ae.

## Running the Application

You can run the application using the provided script:

```bash
./run-aro-parser.sh
```

This will start the application on port 8090.

## Accessing the Dashboard

Once the application is running, you can access the dashboard at:

http://localhost:8090/aro-parser

## API Endpoints

The following API endpoints are available:

- `GET /api/parser/aro/status` - Get the status of the parser
- `GET /api/parser/aro/projects` - Get all project slugs from ARO.ae
- `GET /api/parser/aro/projects/{projectSlug}` - Parse a specific project
- `POST /api/parser/aro/parse-all` - Parse all projects from ARO.ae

## Example Usage

### Check Status

```bash
curl http://localhost:8090/api/parser/aro/status
```

### Get All Projects

```bash
curl http://localhost:8090/api/parser/aro/projects
```

### Parse a Specific Project

```bash
curl http://localhost:8090/api/parser/aro/projects/skyhills-residences-3
```

### Parse All Projects

```bash
curl -X POST http://localhost:8090/api/parser/aro/parse-all
```

## Integration with Workflow

The ARO.ae parser is designed to be integrated with the existing PropertyParsingWorkflow. Once you've verified that the parser works correctly, you can update the workflow to include ARO.ae as a new data source.
