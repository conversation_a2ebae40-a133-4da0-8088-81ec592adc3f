package com.realmond.temporal_service.translate.cache;

import java.util.Set;

/**
 * Interface for a cache that stores String keys and Set<Long> values.
 */
public interface BlackListEntityCache {
    /**
     * Retrieves the Set<Long> associated with the given key.
     *
     * @param key The key whose associated Set<Long> is to be returned.
     * @return The Set<Long> associated with the key, or null if not present.
     */
    Set<Long> get(String key);

    /**
     * Adds a single Long value to the Set associated with the given key.
     * If the key does not exist, a new Set is created and associated with the key.
     *
     * @param key   The key to which the value should be added.
     * @param value The Long value to add to the Set.
     */
    void addToSet(String key, Long value);

    /**
     * Removes a specific Long value from the Set associated with the given key.
     *
     * @param key   The key whose Set is to be modified.
     * @param value The Long value to remove from the Set.
     * @return true if the value was removed, false otherwise.
     */
    boolean removeFromSet(String key, Long value);

    /**
     * Adds or updates a key with a complete Set<Long>.
     * Replaces the existing Set if the key already exists.
     *
     * @param key   The key to add/update.
     * @param value The Set<Long> to associate with the key.
     */
    void put(String key, Set<Long> value);

    /**
     * Removes a key-value pair from the cache.
     *
     * @param key The key whose mapping is to be removed from the cache.
     * @return The previous Set<Long> associated with the key, or null if there was no mapping.
     */
    Set<Long> remove(String key);

    /**
     * Clears all entries from the cache.
     */
    void clear();

    /**
     * Returns the number of key-value mappings in the cache.
     *
     * @return The number of key-value mappings.
     */
    int size();

    /**
     * Checks if the cache contains a mapping for the specified key.
     *
     * @param key The key whose presence is to be tested.
     * @return true if the cache contains a mapping for the key, false otherwise.
     */
    boolean containsKey(String key);
}
