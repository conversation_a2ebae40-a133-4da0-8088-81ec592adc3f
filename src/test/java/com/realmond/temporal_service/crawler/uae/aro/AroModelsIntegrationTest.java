package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.temporal_service.crawler.uae.aro.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for ARO model classes using real JSON data from the Postman collection.
 * 
 * This test ensures that our model classes can properly deserialize the actual JSON responses
 * from the ARO.ae API, validating field mappings and data types.
 */
class AroModelsIntegrationTest {

    private ObjectMapper objectMapper;

    // Real JSON data from ARO.ae Postman collection
    private static final String PROJECTS_LIST_JSON = """
        {
            "data": [
                {
                    "id": 3088,
                    "title": "Alton by <PERSON>sham<PERSON>",
                    "slug": "alton-by-nshama",
                    "price_from": {
                        "currency_code": 784,
                        "amount": 1182888
                    },
                    "developer": {
                        "title": "Nshama",
                        "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                        "id": 403
                    },
                    "images": [
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
                    ],
                    "is_favorite": false,
                    "handover_date": "2028-05-31T00:00:00.000Z",
                    "center": null,
                    "in_folder": false,
                    "pricing_category": null,
                    "rating": 0
                }
            ],
            "paging": {
                "total": 18,
                "size": 30,
                "number": 1
            },
            "total": 540,
            "left": 510,
            "count": 30
        }""";

    private static final String PROJECT_DETAIL_JSON = """
        {
            "id": 3088,
            "slug": "alton-by-nshama",
            "title": "Alton by Nshama",
            "description": "Welcome to Alton by Nshama, nestled in the vibrant community of Town Square...",
            "handover_date": "2028-05-31T00:00:00.000Z",
            "developer": {
                "title": "Nshama",
                "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                "id": 403
            },
            "images": [
                "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
            ],
            "gallery": [
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                    "tags": ["exterior", "main"]
                },
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp",
                    "tags": ["exterior"]
                }
            ],
            "youtube_videos": [],
            "unit_description": "The Alton by Nshama project offers 0 units...",
            "floorplan_description": "The Alton by Nshama project offers 0 unique floor plans.",
            "is_favorite": false,
            "address": "Dubai, Dubailand, Town Square",
            "launch_date": null,
            "construction_start_date": null,
            "has_brochure": true,
            "is_exclusive": false,
            "in_folder": false,
            "pricing_category": null,
            "price_per_sqft": null,
            "rating": 0,
            "rating_count": 0,
            "rating_distribution": {
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0,
                "5": 0
            }
        }""";

    private static final String AMENITIES_JSON = """
        [
            {
                "type": null,
                "image": "https://d1rxmks6dvv2cz.cloudfront.net/facility/1e644e6d-1b67-4313-8577-4dc906ebb2f0.svg",
                "title": "Outdoor Lawn Area",
                "id": 4
            },
            {
                "type": null,
                "image": "https://d1rxmks6dvv2cz.cloudfront.net/facility/cda3c46b-a0e0-4cbc-b2ca-19322796e746.svg",
                "title": "Community Pool",
                "id": 6
            },
            {
                "type": null,
                "image": "https://d1rxmks6dvv2cz.cloudfront.net/facility/451c24c4-c7e3-42ce-878b-f44473020c05.svg",
                "title": "Gym",
                "id": 7
            }
        ]""";

    private static final String UNIT_STATS_JSON = """
        [
            {
                "bedrooms": 1,
                "price_from": {
                    "currency_code": 784,
                    "amount": 1182888
                },
                "size_from": {
                    "measurement": "sqft",
                    "value": 689.21
                },
                "count": 7
            },
            {
                "bedrooms": 2,
                "price_from": {
                    "currency_code": 784,
                    "amount": 1601888
                },
                "size_from": {
                    "measurement": "sqft",
                    "value": 977.36
                },
                "count": 6
            },
            {
                "bedrooms": 3,
                "price_from": {
                    "currency_code": 784,
                    "amount": 2692888
                },
                "size_from": {
                    "measurement": "sqft",
                    "value": 1527.51
                },
                "count": 3
            }
        ]""";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // For Java 8 time support
    }

    @Test
    void deserializeAroApiResponse_ShouldParseProjectsList() throws JsonProcessingException {
        // When: Deserialize projects list response
        AroApiResponse<ProjectSummary> response = objectMapper.readValue(PROJECTS_LIST_JSON,
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));

        // Then: Should properly deserialize all fields
        assertThat(response).isNotNull();
        assertThat(response.getData()).hasSize(1);
        assertThat(response.getTotal()).isEqualTo(540);
        assertThat(response.getLeft()).isEqualTo(510);
        assertThat(response.getCount()).isEqualTo(30);

        // Validate pagination
        assertThat(response.getPaging()).isNotNull();
        assertThat(response.getPaging().getTotal()).isEqualTo(18);
        assertThat(response.getPaging().getSize()).isEqualTo(30);
        assertThat(response.getPaging().getNumber()).isEqualTo(1);
    }

    @Test
    void deserializeProjectSummary_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize projects list and extract first project
        AroApiResponse<ProjectSummary> response = objectMapper.readValue(PROJECTS_LIST_JSON,
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        ProjectSummary project = response.getData().get(0);

        // Then: Should properly deserialize all ProjectSummary fields
        assertThat(project.getId()).isEqualTo(3088);
        assertThat(project.getTitle()).isEqualTo("Alton by Nshama");
        assertThat(project.getSlug()).isEqualTo("alton-by-nshama");
        assertThat(project.getIsFavorite()).isFalse();
        assertThat(project.getInFolder()).isFalse();
        assertThat(project.getPricingCategory()).isNull();
        assertThat(project.getRating()).isEqualTo(0);

        // Validate handover date parsing
        assertThat(project.getHandoverDate()).isNotNull();
        assertThat(project.getHandoverDate()).isEqualTo(ZonedDateTime.parse("2028-05-31T00:00:00.000Z"));

        // Validate price_from
        assertThat(project.getPriceFrom()).isNotNull();
        assertThat(project.getPriceFrom().getCurrencyCode()).isEqualTo(784);
        assertThat(project.getPriceFrom().getAmount()).isEqualTo(1182888);

        // Validate images
        assertThat(project.getImages()).hasSize(2);
        assertThat(project.getImages().get(0)).startsWith("https://d1rxmks6dvv2cz.cloudfront.net/project/");
    }

    @Test
    void deserializeDeveloper_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize projects list and extract developer info
        AroApiResponse<ProjectSummary> response = objectMapper.readValue(PROJECTS_LIST_JSON,
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        Developer developer = response.getData().get(0).getDeveloper();

        // Then: Should properly deserialize all Developer fields from Postman data
        assertThat(developer).isNotNull();
        assertThat(developer.getId()).isEqualTo(403);
        assertThat(developer.getTitle()).isEqualTo("Nshama");
        assertThat(developer.getLogo()).isEqualTo("https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp");
    }

    @Test
    void deserializeProjectDetail_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize project detail response
        ProjectDetail detail = objectMapper.readValue(PROJECT_DETAIL_JSON, ProjectDetail.class);

        // Then: Should properly deserialize all ProjectDetail fields
        assertThat(detail.getId()).isEqualTo(3088);
        assertThat(detail.getSlug()).isEqualTo("alton-by-nshama");
        assertThat(detail.getTitle()).isEqualTo("Alton by Nshama");
        assertThat(detail.getDescription()).startsWith("Welcome to Alton by Nshama");
        assertThat(detail.getAddress()).isEqualTo("Dubai, Dubailand, Town Square");

        // Validate boolean fields
        assertThat(detail.getIsFavorite()).isFalse();
        assertThat(detail.getHasBrochure()).isTrue();
        assertThat(detail.getIsExclusive()).isFalse();
        assertThat(detail.getInFolder()).isFalse();

        // Validate null fields
        assertThat(detail.getLaunchDate()).isNull();
        assertThat(detail.getConstructionStartDate()).isNull();
        assertThat(detail.getPricingCategory()).isNull();
        assertThat(detail.getPricePerSqft()).isNull();

        // Validate numeric fields
        assertThat(detail.getRating()).isEqualTo(0);
        assertThat(detail.getRatingCount()).isEqualTo(0);

        // Validate date parsing
        assertThat(detail.getHandoverDate()).isEqualTo(ZonedDateTime.parse("2028-05-31T00:00:00.000Z"));

        // Validate images
        assertThat(detail.getImages()).hasSize(2);
        assertThat(detail.getImages()).allSatisfy(image -> 
            assertThat(image).startsWith("https://d1rxmks6dvv2cz.cloudfront.net/project/"));

        // Validate developer
        assertThat(detail.getDeveloper()).isNotNull();
        assertThat(detail.getDeveloper().getTitle()).isEqualTo("Nshama");
        assertThat(detail.getDeveloper().getId()).isEqualTo(403);

        // Validate youtube videos (empty array)
        assertThat(detail.getYoutubeVideos()).isEmpty();

        // Validate rating distribution
        assertThat(detail.getRatingDistribution()).isNotNull();
        assertThat(detail.getRatingDistribution()).hasSize(5);
        assertThat(detail.getRatingDistribution().get("1")).isEqualTo(0);
        assertThat(detail.getRatingDistribution().get("5")).isEqualTo(0);
    }

    @Test
    void deserializeGalleryItems_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize project detail and extract gallery items
        ProjectDetail detail = objectMapper.readValue(PROJECT_DETAIL_JSON, ProjectDetail.class);

        // Then: Should properly parse gallery items from Postman data
        assertThat(detail.getGallery()).hasSize(2);

        ProjectDetail.GalleryItem firstItem = detail.getGallery().get(0);
        assertThat(firstItem.getPath()).startsWith("https://d1rxmks6dvv2cz.cloudfront.net/project/");
        assertThat(firstItem.getTags()).containsExactly("exterior", "main");

        ProjectDetail.GalleryItem secondItem = detail.getGallery().get(1);
        assertThat(secondItem.getPath()).startsWith("https://d1rxmks6dvv2cz.cloudfront.net/project/");
        assertThat(secondItem.getTags()).containsExactly("exterior");
    }

    @Test
    void deserializeAmenities_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize amenities list
        List<Amenity> amenities = objectMapper.readValue(AMENITIES_JSON,
            objectMapper.getTypeFactory().constructCollectionType(List.class, Amenity.class));

        // Then: Should properly parse amenities from Postman data
        assertThat(amenities).hasSize(3);

        // Validate first amenity
        Amenity firstAmenity = amenities.get(0);
        assertThat(firstAmenity.getId()).isEqualTo(4);
        assertThat(firstAmenity.getTitle()).isEqualTo("Outdoor Lawn Area");
        assertThat(firstAmenity.getImage()).startsWith("https://d1rxmks6dvv2cz.cloudfront.net/facility/");
        assertThat(firstAmenity.getType()).isNull();

        // Validate second amenity
        Amenity secondAmenity = amenities.get(1);
        assertThat(secondAmenity.getId()).isEqualTo(6);
        assertThat(secondAmenity.getTitle()).isEqualTo("Community Pool");

        // Validate third amenity
        Amenity thirdAmenity = amenities.get(2);
        assertThat(thirdAmenity.getId()).isEqualTo(7);
        assertThat(thirdAmenity.getTitle()).isEqualTo("Gym");
    }

    @Test
    void deserializeUnitStats_ShouldParseAllFields() throws JsonProcessingException {
        // When: Deserialize unit stats list
        List<UnitStats> unitStats = objectMapper.readValue(UNIT_STATS_JSON,
            objectMapper.getTypeFactory().constructCollectionType(List.class, UnitStats.class));

        // Then: Should properly parse unit stats from Postman data
        assertThat(unitStats).hasSize(3);

        // Validate 1-bedroom unit
        UnitStats oneBedroom = unitStats.get(0);
        assertThat(oneBedroom.getBedrooms()).isEqualTo(1);
        assertThat(oneBedroom.getCount()).isEqualTo(7);
        assertThat(oneBedroom.getPriceFrom().getCurrencyCode()).isEqualTo(784);
        assertThat(oneBedroom.getPriceFrom().getAmount()).isEqualTo(1182888);
        assertThat(oneBedroom.getSizeFrom().getMeasurement()).isEqualTo("sqft");
        assertThat(oneBedroom.getSizeFrom().getValue()).isEqualTo(689.21);

        // Validate 2-bedroom unit
        UnitStats twoBedroom = unitStats.get(1);
        assertThat(twoBedroom.getBedrooms()).isEqualTo(2);
        assertThat(twoBedroom.getCount()).isEqualTo(6);
        assertThat(twoBedroom.getPriceFrom().getAmount()).isEqualTo(1601888);
        assertThat(twoBedroom.getSizeFrom().getValue()).isEqualTo(977.36);

        // Validate 3-bedroom unit
        UnitStats threeBedroom = unitStats.get(2);
        assertThat(threeBedroom.getBedrooms()).isEqualTo(3);
        assertThat(threeBedroom.getCount()).isEqualTo(3);
        assertThat(threeBedroom.getPriceFrom().getAmount()).isEqualTo(2692888);
        assertThat(threeBedroom.getSizeFrom().getValue()).isEqualTo(1527.51);
    }

    @Test
    void deserializePrice_ShouldHandleDifferentPriceFormats() throws JsonProcessingException {
        // Given: Different price formats that might appear in ARO API
        String priceJson = """
            {
                "currency_code": 784,
                "amount": 1182888
            }""";

        // When: Deserialize price
        Price price = objectMapper.readValue(priceJson, Price.class);

        // Then: Should properly parse price fields
        assertThat(price.getCurrencyCode()).isEqualTo(784); // AED currency code
        assertThat(price.getAmount()).isEqualTo(1182888);
    }

    @Test
    void deserializeSize_ShouldHandleDifferentSizeFormats() throws JsonProcessingException {
        // Given: Size format from ARO API
        String sizeJson = """
            {
                "measurement": "sqft",
                "value": 689.21
            }""";

        // When: Deserialize size
        UnitStats.SizeInfo size = objectMapper.readValue(sizeJson, UnitStats.SizeInfo.class);

        // Then: Should properly parse size fields
        assertThat(size.getMeasurement()).isEqualTo("sqft");
        assertThat(size.getValue()).isEqualTo(689.21);
    }

    @Test
    void deserializeDeveloper_ShouldHandleVariousFormats() throws JsonProcessingException {
        // Given: Different developer formats that might appear
        String developerWithAllFields = """
            {
                "id": 403,
                "title": "Nshama",
                "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp"
            }""";

        String developerMinimal = """
            {
                "id": 28,
                "title": "HRE Development"
            }""";

        // When: Deserialize both formats
        Developer fullDeveloper = objectMapper.readValue(developerWithAllFields, Developer.class);
        Developer minimalDeveloper = objectMapper.readValue(developerMinimal, Developer.class);

        // Then: Should handle both formats correctly
        assertThat(fullDeveloper.getId()).isEqualTo(403);
        assertThat(fullDeveloper.getTitle()).isEqualTo("Nshama");
        assertThat(fullDeveloper.getLogo()).isNotNull();

        assertThat(minimalDeveloper.getId()).isEqualTo(28);
        assertThat(minimalDeveloper.getTitle()).isEqualTo("HRE Development");
        assertThat(minimalDeveloper.getLogo()).isNull();
    }

    @Test
    void validateJsonPropertyMappings_ShouldMapSnakeCaseToJavaFields() throws JsonProcessingException {
        // This test validates that @JsonProperty annotations work correctly for ARO API fields
        
        // When: Deserialize project with snake_case fields
        ProjectDetail detail = objectMapper.readValue(PROJECT_DETAIL_JSON, ProjectDetail.class);

        // Then: Should map snake_case JSON fields to camelCase Java fields
        assertThat(detail.getHandoverDate()).isNotNull(); // handover_date -> handoverDate
        assertThat(detail.getYoutubeVideos()).isNotNull(); // youtube_videos -> youtubeVideos
        assertThat(detail.getUnitDescription()).isNotNull(); // unit_description -> unitDescription
        assertThat(detail.getFloorplanDescription()).isNotNull(); // floorplan_description -> floorplanDescription
        assertThat(detail.getIsFavorite()).isNotNull(); // is_favorite -> isFavorite
        assertThat(detail.getLaunchDate()).isNull(); // launch_date -> launchDate (null in test data)
        assertThat(detail.getConstructionStartDate()).isNull(); // construction_start_date -> constructionStartDate
        assertThat(detail.getHasBrochure()).isNotNull(); // has_brochure -> hasBrochure
        assertThat(detail.getIsExclusive()).isNotNull(); // is_exclusive -> isExclusive
        assertThat(detail.getInFolder()).isNotNull(); // in_folder -> inFolder
        assertThat(detail.getPricingCategory()).isNull(); // pricing_category -> pricingCategory
        assertThat(detail.getPricePerSqft()).isNull(); // price_per_sqft -> pricePerSqft
        assertThat(detail.getRatingCount()).isNotNull(); // rating_count -> ratingCount
        assertThat(detail.getRatingDistribution()).isNotNull(); // rating_distribution -> ratingDistribution
    }
} 