package com.realmond.temporal_service.translate.activity.strategy;

import com.realmond.temporal_service.config.TranslationConfigProperties;
import com.realmond.temporal_service.translate.dto.SingleTranslateRequest;
import com.realmond.temporal_service.translate.svc.TranslateService;
import io.temporal.activity.Activity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ReactiveTranslateStrategy implements TranslateStrategy {

    private final TranslateService translateService;
    private final int concurrencyLevel;
    private final int batchSize;

    @Autowired
    public ReactiveTranslateStrategy(
            TranslateService translateService,
            TranslationConfigProperties configProperties) {
        this.translateService = translateService;
        this.concurrencyLevel = configProperties.getReactiveConcurrencyLevel();
        this.batchSize = configProperties.getReactiveBatchSize();
        log.info("Reactive strategy initialized with concurrency level: {}, batch size: {}",
                concurrencyLevel, batchSize);
    }

    @Override
    public TranslationResult processEntities(List<Long> entityIds,
                                           String contentType,
                                           String sourceLocale,
                                           String targetLocale) {

        int totalEntities = entityIds.size();
        AtomicInteger processedCount = new AtomicInteger(0);

        // Process in batches to allow heartbeats between batches
        int effectiveBatchSize = Math.min(entityIds.size(), batchSize);
        Map<Long, String> allErrors = new HashMap<>();
        int totalSuccess = 0;

        for (int i = 0; i < entityIds.size(); i += effectiveBatchSize) {
            // Send heartbeat from main thread before processing each batch
            Activity.getExecutionContext().heartbeat(
                    String.format("Processing translations reactively. Progress: %d/%d",
                            processedCount.get(), totalEntities)
            );

            int endIndex = Math.min(i + effectiveBatchSize, entityIds.size());
            List<Long> batch = entityIds.subList(i, endIndex);

            // Process the batch reactively and collect results
            Tuple2<Integer, Map<Long, String>> batchResult = Flux.fromIterable(batch)
                .flatMap(id -> {
                    SingleTranslateRequest request = new SingleTranslateRequest(
                            contentType, id, sourceLocale, targetLocale, true);

                    // Use the reactive translate method directly
                    return translateService.translateReactive(request)
                        .map(response -> {
                            log.debug("Successfully translated {} entity {} to {}", contentType, id, targetLocale);
                            return Tuples.of(true, id, (String)null);
                        })
                        .onErrorResume(e -> {
                            String errorMsg = e.getMessage();
                            log.error("Error translating {} entity {}: {}", contentType, id, errorMsg, e);
                            return Mono.just(Tuples.of(false, id, errorMsg));
                        })
                        .doFinally(signal -> processedCount.incrementAndGet());
                }, concurrencyLevel)
                .reduce(
                    Tuples.of(0, new HashMap<Long, String>()),
                    (acc, result) -> {
                        // Accumulate success count and errors
                        if (result.getT1()) {
                            return Tuples.of(acc.getT1() + 1, acc.getT2());
                        } else {
                            acc.getT2().put(result.getT2(), result.getT3());
                            return acc;
                        }
                    }
                )
                .map(tuple -> {
                    // Convert HashMap to Map for type compatibility
                    return Tuples.of(tuple.getT1(), (Map<Long, String>)tuple.getT2());
                })
                .block(); // Wait for the batch to complete before moving to the next

            // Aggregate batch results
            if (batchResult != null) {
                totalSuccess += batchResult.getT1();
                allErrors.putAll(batchResult.getT2());
            }
        }

        if (!allErrors.isEmpty()) {
            log.error("Encountered {} errors while translating {} entities",
                    allErrors.size(), totalEntities);
        }

        return new TranslationResult(totalSuccess, allErrors);
    }
}
