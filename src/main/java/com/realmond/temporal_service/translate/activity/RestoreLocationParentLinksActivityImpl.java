package com.realmond.temporal_service.translate.activity;

import com.realmond.temporal_service.translate.client.StrapiTranslationClient;
import com.realmond.temporal_service.translate.dto.LocationRestoreResponse;
import io.temporal.spring.boot.ActivityImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ActivityImpl(taskQueues = "translation-task-queue")
public class RestoreLocationParentLinksActivityImpl implements RestoreLocationParentLinksActivity {

    private final StrapiTranslationClient strapiTranslationClient;

    @Autowired
    public RestoreLocationParentLinksActivityImpl(StrapiTranslationClient strapiTranslationClient) {
        this.strapiTranslationClient = strapiTranslationClient;
    }

    @Override
    public int restoreLocationParentLinks() {
        log.info("Restoring location parent links");
        try {
            LocationRestoreResponse response = strapiTranslationClient.restoreLocationParentLinks();
            log.info("Successfully restored parent links for {} locations", response.getAffectedRows());
            return response.getAffectedRows();
        } catch (Exception e) {
            log.error("Failed to restore location parent links: {}", e.getMessage(), e);
            return 0;
        }
    }
} 