package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.realmond.etl.model.ImageModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.config.S3Config.S3Properties;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ImageRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ImageRecord.UploadStatus;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.ImageRepository;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.ImageStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URI;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Implementation of ImageStorageService for SupaBase
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupaBaseImageStorageService implements ImageStorageService {

    private final S3Client s3Client;
    private final S3Properties s3Properties;
    private final ImageRepository imageRepository;

    @Override
    public ImageModel uploadImage(ImageModel imageModel) {
        if (imageModel == null || imageModel.getUrl() == null) {
            log.warn("Cannot upload null image or image with null URL");
            return imageModel;
        }

        String originalUrl = imageModel.getUrl().toString();

        // Check if already uploaded
        Optional<ImageRecord> existingRecord = imageRepository.findById(originalUrl);
        if (existingRecord.isPresent() && UploadStatus.COMPLETED.equals(existingRecord.get().getStatus())) {
            log.debug("Image already uploaded: {}", originalUrl);
            try {
                // Update the image model with the existing S3 URL
                imageModel.setUrl(URI.create(existingRecord.get().getS3Url()));
                return imageModel;
            } catch (Exception e) {
                log.error("Error updating image URL: {}", e.getMessage(), e);
                return imageModel;
            }
        }


        ImageRecord record = ImageRecord.builder()
                .originalUrl(originalUrl)
                .uploadedAt(ZonedDateTime.now())
                .status(UploadStatus.FAILED)
                .build();

        imageRepository.save(record);
        log.debug("Saved image record: {}");

        return imageModel;
    }



    @Override
    @Async
    public CompletableFuture<List<ImageModel>> uploadImagesAsync(List<ImageModel> imageModels) {
        if (imageModels == null || imageModels.isEmpty()) {
            log.warn("No images to upload");
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        log.info("Uploading {} images asynchronously", imageModels.size());

        List<ImageModel> uploadedImages = imageModels.stream()
                .map(this::uploadImage)
                .collect(Collectors.toList());

        return CompletableFuture.completedFuture(uploadedImages);
    }

    @Override
    public boolean isImageUploaded(String originalUrl) {
        if (originalUrl == null || originalUrl.trim().isEmpty()) {
            return false;
        }

        Optional<ImageRecord> record = imageRepository.findById(originalUrl);
        return record.isPresent() && UploadStatus.COMPLETED.equals(record.get().getStatus());
    }

    @Override
    public String getS3Url(String originalUrl) {
        if (originalUrl == null || originalUrl.trim().isEmpty()) {
            return null;
        }

        Optional<ImageRecord> record = imageRepository.findById(originalUrl);
        if (record.isPresent() && UploadStatus.COMPLETED.equals(record.get().getStatus())) {
            return record.get().getS3Url();
        }

        return null;
    }

    /**
     * Generate a unique S3 key for the image
     * Format: {basePath}/{yyyy-MM-dd}/{uuid}{extension}
     */
    private String generateS3Key(String originalUrl) {
        String extension = getExtensionFromUrl(originalUrl);
        String uniqueId = UUID.randomUUID().toString().replace("-", "");
        String datePath = java.time.LocalDate.now().toString();

        return String.format("%s/%s/%s%s",
                s3Properties.getBasePath(),
                datePath,
                uniqueId,
                extension);
    }

    /**
     * Extract file extension from URL
     */
    private String getExtensionFromUrl(String url) {
        if (url == null) return ".jpg"; // Default

        int questionMarkPos = url.lastIndexOf('?');
        if (questionMarkPos > 0) {
            url = url.substring(0, questionMarkPos);
        }

        int lastDotPos = url.lastIndexOf('.');
        if (lastDotPos >= 0) {
            String ext = url.substring(lastDotPos);
            return ext.length() <= 5 ? ext : ".jpg"; // Most extensions are 4 chars or less
        }

        return ".jpg"; // Default
    }
}
