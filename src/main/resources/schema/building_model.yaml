---
"$schema": http://json-schema.org/draft-07/schema#
type: object
properties:
  title:
    type: string
  levels:
    type: integer
  address:
    type: string
  geometry:
    type: object
    properties:
      polygon:
        type: array
        items:
          $ref: common/coordinates_model.yaml
      centroid:
        $ref: common/coordinates_model.yaml
    required:
      - centroid
  images:
    type: array
    items:
      $ref: common/image_model.yaml
  amenities:
    type: array
    items:
      $ref: common/amenity_model.yaml
  building_urn:
    type: string
  source_urn:
    type: string
  external_id:
    type: string
  project_urn:
    type: string
required:
  - source_urn
  - building_urn
  - external_id
  - project_urn
