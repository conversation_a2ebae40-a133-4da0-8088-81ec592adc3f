package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.Page;
import com.realmond.etl.model.AdModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.AdRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.AdRepository;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Implementation of CrawlingService for Damac Properties using <PERSON>wright with Browserless.
 * Extends AbstractCrawlingService for common crawling functionality.
 */
@Service
@Qualifier("propertiesCrawler")
@Slf4j
@RequiredArgsConstructor
public class DamacCrawlingService extends AbstractCrawlingService {

    private static final String BASE_URL = "https://agents.damacproperties.com/AP_UnitSearch?langCode=en_US";

    // Dependencies injected through constructor (Dependency Inversion)
    private final BrowserManager browserManager;
    private final AuthenticationService authenticationService;
    private final NavigationService navigationService;
    private final PropertyParserService propertyParserService;
    private final AdRepository adRepository;

    @Value("${damacproperties.crawler.username}")
    private String username;

    @Value("${damacproperties.crawler.password}")
    private String password;

    @Value("${damacproperties.crawler.page-wait-time:5}")
    private int pageWaitTime;

    @Value("${damacproperties.crawler.max-pages:20}")
    private int maxPages;

    /**
     * Provides the browser manager to the abstract class
     */
    @Override
    protected BrowserManager getBrowserManager() {
        return browserManager;
    }

    /**
     * Performs the actual crawling operations
     */
    @Override
    protected void performCrawl(Page page) {
        // Authenticate and setup
        page = performInitialSetup(page);

        // Process property pages
        processPropertyPages(page);
    }

    /**
     * Sets up the initial authentication and search parameters.
     * Returns a new page if the browser reconnects during setup.
     */
    private Page performInitialSetup(Page page) {
        try {
            log.info("Starting initial setup");

            // Navigate to the base URL
            browserManager.navigateTo(page, BASE_URL);

            // Authenticate if needed
            if (!authenticationService.isAuthenticated(page)) {
                log.info("Authentication required");
                authenticationService.authenticate(page, username, password, pageWaitTime);

                // Double-check authentication
                if (!authenticationService.isAuthenticated(page)) {
                    throw new CrawlerException("Authentication failed");
                }

            } else {
                log.info("Already authenticated");
            }

            // Configure search filters
            log.info("Configuring search filters");
            navigationService.configureFilters(page, pageWaitTime);

            return page;

        } catch (BrowserlessManager.BrowserReconnectedException e) {
            log.info("Browser reconnected during setup, continuing with new page");
            Page newPage = e.getNewPage();

            // Start over with the new page
            return performInitialSetup(newPage);
        }
    }

    /**
     * Processes each property page, extracting and saving properties.
     * Handles browser reconnection during processing.
     */
    private void processPropertyPages(Page page) {
        int pageNum = 1;
        boolean hasNextPage = true;
        int totalProperties = 0;
        int pagesProcessed = 0;

        while (hasNextPage && pageNum <= maxPages) {
            try {
                log.info("Processing page {}", pageNum);

                // Parse properties on current page
                List<AdModel> properties = propertyParserService.parsePropertiesFromCurrentPage(page, pageWaitTime);
                log.info("Found {} properties on page {}", properties.size(), pageNum);

                // Save properties to repository
                saveProperties(properties.stream().map(property ->
                        AdRecord.builder()
                                .urn(property.getAdUrn())
                                .data(property)
                                .emittedAt(ZonedDateTime.now())
                                .build()
                ).toList());

                // Update metrics
                totalProperties += properties.size();
                pagesProcessed++;

                // Check if there's a next page
                hasNextPage = navigationService.goToNextPage(page, pageWaitTime);
                pageNum++;

            } catch (BrowserlessManager.BrowserReconnectedException e) {
                log.info("Browser reconnected during page processing, recovering");

                // Get the new page from the exception
                page = e.getNewPage();

                // Re-authenticate and navigate back to where we were
                try {
                    // Setup from the beginning
                    page = performInitialSetup(page);

                    // If we were beyond page 1, try to navigate to the current page
                    if (pageNum > 1) {
                        log.info("Attempting to navigate back to page {}", pageNum);
                        for (int i = 1; i < pageNum; i++) {
                            boolean success = navigationService.goToNextPage(page, pageWaitTime);
                            if (!success) {
                                log.warn("Could only navigate back to page {}. Continuing from there.", i);
                                pageNum = i + 1; // +1 because the next iteration will process the current page
                                break;
                            }
                        }
                    }

                    // Don't increment pageNum, we want to retry the current page
                    continue;

                } catch (Exception recoverError) {
                    log.error("Failed to recover after browser reconnection: {}", recoverError.getMessage(), recoverError);
                    throw recoverError;
                }
            }
        }

        if (pageNum > maxPages) {
            log.info("Reached maximum page limit of {}", maxPages);
        }

        log.info("Processed {} pages and found {} properties", pagesProcessed, totalProperties);

        // Attempt to logout
        try {
            log.info("Logging out from the agent portal");
            authenticationService.logout(page);
        } catch (Exception e) {
            log.warn("Error during logout: {}", e.getMessage());
        }
    }

    /**
     * Helper method to safely save properties to the repository.
     * Isolates database operations and provides detailed error handling.
     */
    private void saveProperties(List<AdRecord> properties) {
        if (properties == null || properties.isEmpty()) {
            log.debug("No properties to save");
            return;
        }

        log.debug("Saving {} properties to database", properties.size());
        int successCount = 0;

        for (AdRecord property : properties) {
            try {
                adRepository.save(property);
                successCount++;
            } catch (Exception e) {
                log.error("Error saving property {}: {}",
                        property.getUrn() != null ? property.getUrn() : "unknown",
                        e.getMessage());
            }
        }

        log.info("Successfully saved {}/{} properties", successCount, properties.size());
    }
}
