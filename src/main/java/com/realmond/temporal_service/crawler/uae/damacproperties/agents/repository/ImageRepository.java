package com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository;

import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.ImageRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for managing ImageRecord entities
 */
@Repository
public interface ImageRepository extends JpaRepository<ImageRecord, String> {

    /**
     * Find all images with a given upload status
     *
     * @param status The upload status to search for
     * @return List of image records with the specified status
     */
    List<ImageRecord> findByStatus(ImageRecord.UploadStatus status);
}
