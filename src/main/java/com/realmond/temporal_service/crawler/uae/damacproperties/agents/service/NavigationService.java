package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Page;

/**
 * Interface for page navigation operations
 * Following the Single Responsibility Principle by isolating navigation logic
 */
public interface NavigationService {

    /**
     * Configure filters on the property search page
     * @param page The page instance
     * @param pageWaitTimeSeconds Page wait time in seconds
     */
    void configureFilters(Page page, int pageWaitTimeSeconds);

    /**
     * Navigate to the next page
     * @param page The page instance
     * @param pageWaitTimeSeconds Page wait time in seconds
     * @return true if navigated to next page, false if no more pages
     */
    boolean goToNextPage(Page page, int pageWaitTimeSeconds);
}
