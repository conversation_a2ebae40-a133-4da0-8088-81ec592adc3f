package com.realmond.temporal_service.crawler.uae.aro;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.err.RetryableCrawlerException;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Integration test for {@link AroApiService} using realistic mock data from the ARO.ae Postman collection.
 * 
 * This test simulates real API responses to ensure proper data transformation and error handling.
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = {AroApiServiceMockTest.TestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
class AroApiServiceMockTest {

    // Test configuration that provides only the needed beans
    static class TestConfig {
        // Minimal configuration for this test
    }

    @Mock
    private AroFeignRestClient aroClient;

    @Mock
    private AroEnrichmentService enrichmentService;

    @Mock
    private AroSettings settings;

    private AroApiService aroApiService;
    private ObjectMapper objectMapper;

    // Mock data based on the Postman collection
    private static final String MOCK_PROJECTS_RESPONSE = """
        {
            "data": [
                {
                    "id": 3088,
                    "title": "Alton by Nshama",
                    "slug": "alton-by-nshama",
                    "price_from": {
                        "currency_code": 784,
                        "amount": 1182888
                    },
                    "developer": {
                        "title": "Nshama",
                        "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                        "id": 403
                    },
                    "images": [
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
                    ],
                    "is_favorite": false,
                    "handover_date": "2028-05-31T00:00:00.000Z",
                    "center": null,
                    "in_folder": false,
                    "pricing_category": null,
                    "rating": 0
                },
                {
                    "id": 3085,
                    "title": "Skyhills Residences 3",
                    "slug": "skyhills-residences-3",
                    "price_from": {
                        "currency_code": 784,
                        "amount": 1126510
                    },
                    "developer": {
                        "title": "HRE Development",
                        "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp",
                        "id": 28
                    },
                    "images": [
                        "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp"
                    ],
                    "is_favorite": false,
                    "handover_date": "2026-10-31T00:00:00.000Z",
                    "center": null,
                    "in_folder": false,
                    "pricing_category": "Mid-Range",
                    "rating": 0
                }
            ],
            "paging": {
                "total": 18,
                "size": 30,
                "number": 1
            },
            "total": 540,
            "left": 510,
            "count": 30
        }""";

    private static final String MOCK_PROJECT_DETAIL_RESPONSE = """
        {
            "id": 3088,
            "slug": "alton-by-nshama",
            "title": "Alton by Nshama",
            "description": "Welcome to Alton by Nshama, nestled in the vibrant community of Town Square, an area synonymous with dynamic growth and family-friendly vibes. This is where you can find your dream home, tailored to enhance your lifestyle.",
            "handover_date": "2028-05-31T00:00:00.000Z",
            "developer": {
                "title": "Nshama",
                "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/24acfc98-183a-4172-b85f-1586044ba625.webp",
                "id": 403
            },
            "images": [
                "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp"
            ],
            "gallery": [
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/06eb8ae2-ad6a-4a8a-9c89-14cec01a3ac7.webp",
                    "tags": ["exterior", "main"]
                },
                {
                    "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/c75ccfb7-da2a-4b3d-916c-382536c18d0c.webp",
                    "tags": ["exterior"]
                }
            ],
            "youtube_videos": [],
            "unit_description": "The Alton by Nshama project offers 0 units, ranging from studios to unspecified bedroom counts.",
            "floorplan_description": "The Alton by Nshama project offers 0 unique floor plans.",
            "is_favorite": false,
            "address": "Dubai, Dubailand, Town Square",
            "launch_date": null,
            "construction_start_date": null,
            "has_brochure": true,
            "is_exclusive": false,
            "in_folder": false,
            "pricing_category": null,
            "price_per_sqft": null,
            "rating": 0,
            "rating_count": 0,
            "rating_distribution": {
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0,
                "5": 0
            }
        }""";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // For Java 8 time support
        
        // Setup default settings behavior
        when(settings.getPageSize()).thenReturn(30);
        
        aroApiService = new AroApiService(aroClient, enrichmentService, settings);
    }

    @Test
    void fetchProjectPagesCount_WithValidResponse_ShouldReturnCorrectPageCount() throws JsonProcessingException {
        // Given: Mock response with pagination info from Postman collection
        AroApiResponse<ProjectSummary> mockResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        when(aroClient.getProjects(1, 30)).thenReturn(mockResponse);

        // When: Fetch project pages count
        int pagesCount = aroApiService.fetchProjectPagesCount();

        // Then: Should return the total pages from the pagination info
        assertThat(pagesCount).isEqualTo(18);
    }

    @Test
    void fetchProjectPagesCount_WithNoPagingInfo_ShouldReturnOne() {
        // Given: Mock response without pagination info
        AroApiResponse<ProjectSummary> mockResponseNoPaging = new AroApiResponse<>();
        mockResponseNoPaging.setData(List.of());
        mockResponseNoPaging.setPaging(null);
        
        when(aroClient.getProjects(1, 30)).thenReturn(mockResponseNoPaging);

        // When: Fetch project pages count
        int pagesCount = aroApiService.fetchProjectPagesCount();

        // Then: Should return 1 as default
        assertThat(pagesCount).isEqualTo(1);
    }

    @Test
    void fetchProjectPagesCount_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getProjects(1, 30)).thenThrow(new RuntimeException("API error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroApiService.fetchProjectPagesCount())
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching pages count")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchProjectsPage_WithValidResponse_ShouldReturnCrawlRecords() throws JsonProcessingException {
        // Given: Mock successful responses from ARO API
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail1 = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        // Create second project detail for Skyhills
        String skyhillsDetailJson = """
            {
                "id": 3085,
                "slug": "skyhills-residences-3",
                "title": "Skyhills Residences 3",
                "description": "Discover luxury living at Skyhills Residences 3, a premium development offering modern amenities and elegant design.",
                "handover_date": "2026-10-31T00:00:00.000Z",
                "developer": {
                    "title": "HRE Development",
                    "logo": "https://d1rxmks6dvv2cz.cloudfront.net/developer/7c79003f-07df-427c-8806-de5db5e8fcb1.webp",
                    "id": 28
                },
                "images": [
                    "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp"
                ],
                "gallery": [
                    {
                        "path": "https://d1rxmks6dvv2cz.cloudfront.net/project/2b64c46a-85e8-44b1-9e23-b00b08cf78eb.webp",
                        "tags": ["exterior", "main"]
                    }
                ],
                "youtube_videos": [],
                "unit_description": "The Skyhills Residences 3 project offers modern units with premium finishes.",
                "floorplan_description": "The Skyhills Residences 3 project offers various floor plans.",
                "is_favorite": false,
                "address": "Dubai, Al Furjan",
                "launch_date": null,
                "construction_start_date": null,
                "has_brochure": true,
                "is_exclusive": false,
                "in_folder": false,
                "pricing_category": "Mid-Range",
                "price_per_sqft": null,
                "rating": 0,
                "rating_count": 0,
                "rating_distribution": {
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0
                }
            }""";
        
        ProjectDetail mockProjectDetail2 = objectMapper.readValue(skyhillsDetailJson, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo1 = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail1)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();
            
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo2 = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail2)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks - both projects should succeed with their specific details
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(mockProjectDetail1);
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail2);
        when(enrichmentService.enrichProjectDetail(mockProjectDetail1)).thenReturn(mockEnrichedInfo1);
        when(enrichmentService.enrichProjectDetail(mockProjectDetail2)).thenReturn(mockEnrichedInfo2);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return crawl records for all projects on the page
        assertThat(results).hasSize(2);
        
        // Validate first project (Alton by Nshama)
        CrawlRecord<ProjectModel> firstRecord = results.get(0);
        assertThat(firstRecord.data().getTitle()).isEqualTo("Alton by Nshama");
        assertThat(firstRecord.data().getExternalId()).isEqualTo("3088");
        assertThat(firstRecord.data().getSourceUrn()).isEqualTo("aro.ae");
        assertThat(firstRecord.source()).isEqualTo("aro.ae");
        
        // Validate metadata contains raw API data
        assertThat(firstRecord.metadata()).containsKey("raw_data");
        assertThat(firstRecord.metadata()).containsKey("conversion_timestamp");
        assertThat(firstRecord.metadata()).containsKey("source_api");
        assertThat(firstRecord.metadata().get("source_api")).isEqualTo("aro.ae");
        
        // Validate second project (Skyhills Residences 3)
        CrawlRecord<ProjectModel> secondRecord = results.get(1);
        assertThat(secondRecord.data().getTitle()).isEqualTo("Skyhills Residences 3");
        assertThat(secondRecord.data().getExternalId()).isEqualTo("3085");
    }

    @Test
    void fetchProjectsPage_WithEmptyResponse_ShouldReturnEmptyList() {
        // Given: Mock empty response
        AroApiResponse<ProjectSummary> emptyResponse = new AroApiResponse<>();
        emptyResponse.setData(List.of());
        
        when(aroClient.getProjects(99, 30)).thenReturn(emptyResponse);

        // When: Fetch projects from page 99
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchProjectsPage_WithNullData_ShouldReturnEmptyList() {
        // Given: Mock response with null data
        AroApiResponse<ProjectSummary> nullDataResponse = new AroApiResponse<>();
        nullDataResponse.setData(null);
        
        when(aroClient.getProjects(99, 30)).thenReturn(nullDataResponse);

        // When: Fetch projects from page 99
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(99);

        // Then: Should return empty list
        assertThat(results).isEmpty();
    }

    @Test
    void fetchProjectsPage_WhenProjectDetailFails_ShouldContinueWithOtherProjects() throws JsonProcessingException {
        // Given: Mock successful project list but failing project detail for first project
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks: first project fails, second succeeds
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama"))
            .thenThrow(new RuntimeException("Project detail fetch failed"));
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return only the successful project (continues processing despite failure)
        assertThat(results).hasSize(1);
        assertThat(results.get(0).data().getTitle()).isEqualTo("Alton by Nshama"); // The mock always returns same detail
    }

    @Test
    void fetchProjectsPage_WhenProjectConversionFails_ShouldContinueWithOtherProjects() throws JsonProcessingException {
        // Given: Mock successful responses but enrichment service fails for first project
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup mocks: enrichment fails for first project, succeeds for second
        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug(any())).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class)))
            .thenThrow(new RuntimeException("Enrichment failed"))
            .thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 1
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should return both projects - one with enrichment failure handled gracefully, one with successful enrichment
        assertThat(results).hasSize(2);
    }

    @Test
    void fetchProjectsPage_WhenClientThrowsException_ShouldThrowRetryableException() {
        // Given: Mock client throws exception
        when(aroClient.getProjects(1, 30)).thenThrow(new RuntimeException("Network error"));

        // When & Then: Should wrap in RetryableCrawlerException
        assertThatThrownBy(() -> aroApiService.fetchProjectsPage(1))
            .isInstanceOf(RetryableCrawlerException.class)
            .hasMessage("Error fetching page: 1")
            .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void fetchProjectsPage_WithDifferentPageSizes_ShouldHandleCorrectly() throws JsonProcessingException {
        // Given: Mock response that simulates different page sizes
        AroApiResponse<ProjectSummary> mockResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        // Setup all required mocks
        when(aroClient.getProjects(5, 30)).thenReturn(mockResponse);
        when(aroClient.getProjectBySlug("alton-by-nshama")).thenReturn(mockProjectDetail);
        when(aroClient.getProjectBySlug("skyhills-residences-3")).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects from page 5
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(5);

        // Then: Should process correctly regardless of page number
        assertThat(results).hasSize(2); // Based on mock data
    }

    @Test
    void fetchProjectsPage_WithRealProjectData_ShouldCreateValidUrns() throws JsonProcessingException {
        // Given: Mock realistic project data from Postman collection
        AroApiResponse<ProjectSummary> mockProjectsResponse = objectMapper.readValue(MOCK_PROJECTS_RESPONSE, 
            objectMapper.getTypeFactory().constructParametricType(AroApiResponse.class, ProjectSummary.class));
        
        ProjectDetail mockProjectDetail = objectMapper.readValue(MOCK_PROJECT_DETAIL_RESPONSE, ProjectDetail.class);
        
        AroEnrichmentService.EnrichedProjectInfo mockEnrichedInfo = AroEnrichmentService.EnrichedProjectInfo.builder()
            .projectDetail(mockProjectDetail)
            .amenities(List.of())
            .unitStats(List.of())
            .buildings(List.of())
            .build();

        when(aroClient.getProjects(1, 30)).thenReturn(mockProjectsResponse);
        when(aroClient.getProjectBySlug(any())).thenReturn(mockProjectDetail);
        when(enrichmentService.enrichProjectDetail(any(ProjectDetail.class))).thenReturn(mockEnrichedInfo);

        // When: Fetch projects
        List<CrawlRecord<ProjectModel>> results = aroApiService.fetchProjectsPage(1);

        // Then: Should create proper URN structure based on ARO patterns
        assertThat(results).hasSize(2);
        
        for (CrawlRecord<ProjectModel> record : results) {
            ProjectModel project = record.data();
            
            // Validate URN structure follows ARO.ae patterns
            assertThat(project.getSourceUrn()).isEqualTo("aro.ae");
            assertThat(project.getDeveloperUrn()).startsWith("aro.ae:developer:");
            assertThat(project.getProjectUrn()).startsWith(project.getDeveloperUrn() + ":project:");
            assertThat(project.getCurrency()).isEqualTo("AED");
            
            // Validate CrawlRecord structure
            assertThat(record.source()).isEqualTo("aro.ae");
            assertThat(record.urn()).isEqualTo(project.getProjectUrn());
        }
    }
} 