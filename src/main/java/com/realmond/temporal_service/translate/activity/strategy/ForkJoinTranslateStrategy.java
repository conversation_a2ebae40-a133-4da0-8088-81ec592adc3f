package com.realmond.temporal_service.translate.activity.strategy;

import com.realmond.temporal_service.translate.dto.SingleTranslateRequest;
import com.realmond.temporal_service.translate.svc.TranslateService;
import io.temporal.activity.Activity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ForkJoinTranslateStrategy implements TranslateStrategy {

    private final TranslateService translateService;

    @Autowired
    public ForkJoinTranslateStrategy(TranslateService translateService) {
        this.translateService = translateService;
        log.info("Parallel strategy will use ForkJoinPool with parallelism level: {}",
                ForkJoinPool.getCommonPoolParallelism());
    }

    @Override
    public TranslationResult processEntities(List<Long> entityIds,
                                                                                                       String contentType,
                                                                                                       String sourceLocale,
                                                                                                       String targetLocale) {

        Map<Long, String> errors = new ConcurrentHashMap<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger processedCount = new AtomicInteger(0);
        int batchSize = Math.min(entityIds.size(), 10);

        for (int i = 0; i < entityIds.size(); i += batchSize) {
            // Send heartbeat from main thread before processing each batch
            Activity.getExecutionContext().heartbeat(
                    String.format("Processing translations in parallel. Progress: %d/%d",
                            processedCount.get(), entityIds.size())
            );

            int endIndex = Math.min(i + batchSize, entityIds.size());
            List<Long> batch = entityIds.subList(i, endIndex);

            batch.parallelStream().forEach(id -> {
                try {
                    SingleTranslateRequest request = new SingleTranslateRequest(
                            contentType, id, sourceLocale, targetLocale, true);
                    translateService.translate(request);
                    successCount.incrementAndGet();
                    log.debug("Successfully translated {} entity {} to {}", contentType, id, targetLocale);
                } catch (Exception e) {
                    String errorMsg = e.getMessage();
                    errors.put(id, errorMsg);
                    log.error("Error translating {} entity {}: {}", contentType, id, errorMsg);
                } finally {
                    processedCount.incrementAndGet();
                }
            });
        }

        if (!errors.isEmpty()) {
            log.error("Encountered {} errors while translating {} entities",
                    errors.size(), entityIds.size());
        }

        return new TranslationResult(successCount.get(), errors);
    }
}
