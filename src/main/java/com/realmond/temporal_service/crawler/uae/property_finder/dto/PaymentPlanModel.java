package com.realmond.temporal_service.crawler.uae.property_finder.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import java.util.List;

/**
 * Represents a payment plan structure.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@SuperBuilder
@NoArgsConstructor
public class PaymentPlanModel {

    @JsonProperty("title")
    private String title;

    @JsonProperty("phases")
    private List<PaymentPhaseModel> phases;

}
