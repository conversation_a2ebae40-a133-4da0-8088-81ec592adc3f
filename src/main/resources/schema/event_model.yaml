---
"$schema": http://json-schema.org/draft-07/schema#
title: event
description: create/update/delete event derived from external source fetch cycle
type: object
definitions:
  event_type:
    type: string
    enum:
      - create
      - update
      - delete
  metadata:
    type: object
    properties:
      event_type:
        $ref: "#/definitions/event_type"
      fetched_at:
        type: string
        format: date-time
      created_at:
        type: string
        format: date-time
  payload:
    oneOf:
      - $ref: project_model.yaml
      - $ref: ad_model.yaml
properties:
  metadata:
    $ref: "#/definitions/metadata"
  payload:
    $ref: "#/definitions/payload"

