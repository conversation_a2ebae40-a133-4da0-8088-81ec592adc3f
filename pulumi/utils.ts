// noinspection JSUnresolvedReference

import * as pulumi from "@pulumi/pulumi";
import {Input, Output} from "@pulumi/pulumi";
import * as aws from "@pulumi/aws";

export type Tags = { [p: string]: Input<string> }

const pulumiStateBucket = "pulumi-state-7";

/**
 * retrieves plain output from external project/stack.
 * @param project project name. similar to gitlab project name
 * @param stack stack name
 * @param key stack output key
 * @param stateBucket pulumi s3 state bucket name
 */
export async function getStackOutput<T>(project: string, stack: string, key: string, stateBucket: string = pulumiStateBucket): Promise<T> {
    const state = await aws.s3.getObject({key: `.pulumi/stacks/${project}/${stack}.json`, bucket: stateBucket});
    const j = JSON.parse(state.body);
    const resources = j.checkpoint.latest.resources
    const r = resources.find((r: {
        urn: string;
    }) => r.urn === `urn:pulumi:prod::network::pulumi:pulumi:Stack::${project}-${stack}`)
    return r.outputs[key] as T;
}

/**
 * Base tags must propagate the full pulumi execution context - the only constraint is to keep secrets safe and to be compliant with k8s and aws tag rules:
 * - must consist of alphanumeric characters, '-', '_' or '.', and must start and end with an alphanumeric character (e.g. 'MyValue',  or 'my_value',  or '12345', regex used for validation is '(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])?')
 * -
 * This enables:
 * - troubleshooting
 * - addressing documentation page for ops teams
 * - a SIEM solution to detect anomalies and prevent a security breach
 * - granular access model
 * See more for gitlab-ci context: https://docs.gitlab.com/ee/ci/variables/predefined_variables.html
 */
export function getBaseTags(): Output<Tags> {
    const pulumiTags = getPulumiTags();
    const awsTags: Promise<Tags> = getAwsTags()
    let gitlabTags = {};
    if (process.env.CI) {
        gitlabTags = {
            "git/commit": process.env.CI_COMMIT_SHA,
            "gitlab/namespace": process.env.CI_PROJECT_NAMESPACE,
            "gitlab/repo-name": process.env.CI_PROJECT_NAME,
            "gitlab-ci/environment": process.env.CI_ENVIRONMENT_NAME,
            "gitlab-ci/pipeline-id": process.env.CI_PIPELINE_ID,
            "gitlab-ci/pipeline-source": process.env.CI_PIPELINE_SOURCE,
            "gitlab-ci/runner-id": process.env.CI_RUNNER_ID,
            "gitlab-ci/runner-revision": process.env.CI_RUNNER_REVISION,
            "gitlab-ci/runner-version": process.env.CI_RUNNER_VERSION,
        }
    }
    const tags = awsTags.then(awsTags => {
        return {
            ...awsTags,
            ...pulumiTags,
            ...gitlabTags
        }
    });

    return pulumi.output<Tags>(tags);
}

function getPulumiTags() {
    return {
        "managed-by": "Pulumi",
        "pulumi/project": pulumi.getProject(),
        "pulumi/stack": pulumi.getStack(),
        "pulumi/organization": pulumi.getOrganization()
    };
}

async function getAwsTags() {
    let result = await aws.getCallerIdentity();
    return {
        "aws/accountId": result.accountId,
        "aws/userId": result.userId
    }
}

export function resolveImageVersion(fallbackVersion: string): string {
    return process.env.CI_COMMIT_TAG ?? process.env.CI_COMMIT_SHORT_SHA ?? fallbackVersion;
}

export const pick = (obj: any, keys: string[]) => keys.reduce((acc, key) => (key in obj ? {...acc, [key]: obj[key]} : acc), {});

export function encodeSecret(secret: string): string {
    return Buffer.from(secret).toString('base64');
}
