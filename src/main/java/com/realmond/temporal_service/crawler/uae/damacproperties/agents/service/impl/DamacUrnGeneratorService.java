package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.UrnGeneratorService;
import org.springframework.stereotype.Service;

/**
 * Implementation of UrnGeneratorService for Damac Properties
 */
@Service
public class DamacUrnGeneratorService implements UrnGeneratorService {

    private static final String DEVELOPER_SLUG = "damac-properties";
    private static final String SOURCE_SLUG = "agents.damacproperties.com";

    @Override
    public String generateAdUrn(String adId, String projectName) {
        String normalizedProjectName = projectName.toLowerCase().replaceAll("[^a-z0-9]", "-");
        return String.format("urn:ad:%s:%s:%s", DEVELOPER_SLUG, normalizedProjectName, adId);
    }

    @Override
    public String generateUniqueAdUrn(String adId, String projectName, String propertyType, String bedrooms, String unitType) {
        String normalizedProjectName = projectName.toLowerCase().replaceAll("[^a-z0-9]", "-");

        StringBuilder uniqueIdBuilder = new StringBuilder(adId);

        // Add property type if available (normalized)
        if (propertyType != null && !propertyType.isEmpty()) {
            String normalizedPropertyType = propertyType.toLowerCase().replaceAll("[^a-z0-9]", "-");
            uniqueIdBuilder.append("-").append(normalizedPropertyType);
        }

        // Add bedrooms if available
        if (bedrooms != null && !bedrooms.isEmpty()) {
            // Clean bedrooms value - remove non-alphanumeric chars except dots for decimal values
            String cleanedBedrooms = bedrooms.replaceAll("[^a-zA-Z0-9.]", "");
            uniqueIdBuilder.append("-").append(cleanedBedrooms);
        }

        // Add unit type if available (normalized)
        if (unitType != null && !unitType.isEmpty()) {
            String normalizedUnitType = unitType.toLowerCase().replaceAll("[^a-z0-9]", "-");
            uniqueIdBuilder.append("-").append(normalizedUnitType);
        }

        return String.format("urn:ad:%s:%s:%s", DEVELOPER_SLUG, normalizedProjectName, uniqueIdBuilder.toString());
    }

    @Override
    public String generateSourceUrn() {
        return String.format("urn:source:%s", SOURCE_SLUG);
    }
}
