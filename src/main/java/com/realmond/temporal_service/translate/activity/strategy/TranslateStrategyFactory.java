package com.realmond.temporal_service.translate.activity.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TranslateStrategyFactory {

    private final ForkJoinTranslateStrategy parallelStrategy;
    private final SequentialTranslateStrategy sequentialStrategy;
    private final ReactiveTranslateStrategy reactiveStrategy;

    @Autowired
    public TranslateStrategyFactory(
            ForkJoinTranslateStrategy parallelStrategy,
            SequentialTranslateStrategy sequentialStrategy,
            ReactiveTranslateStrategy reactiveStrategy) {
        this.parallelStrategy = parallelStrategy;
        this.sequentialStrategy = sequentialStrategy;
        this.reactiveStrategy = reactiveStrategy;
    }

    public TranslateStrategy getStrategy(String strategyName) {
        return switch (strategyName) {
            case "sequential" -> sequentialStrategy;
            case "forkjoin" -> parallelStrategy;
            case "reactive" -> reactiveStrategy;
            default -> throw new RuntimeException("unknown strategy: " + strategyName);
        };
    }
}
