package com.realmond.temporal_service.translate.activity.strategy;

import java.util.List;
import java.util.Map;

public interface TranslateStrategy {
    /**
     * Process a list of entity IDs for translation
     *
     * @param entityIds List of entity IDs to translate
     * @param contentType The content type being translated
     * @param sourceLocale Source language
     * @param targetLocale Target language
     * @return TranslationResult containing successful count and errors
     */
    TranslationResult processEntities(List<Long> entityIds,
                                     String contentType,
                                     String sourceLocale,
                                     String targetLocale);

    /**
     * Result class for translation operations
     */
    class TranslationResult {
        private final int successCount;
        private final Map<Long, String> errors;

        public TranslationResult(int successCount, Map<Long, String> errors) {
            this.successCount = successCount;
            this.errors = errors;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public Map<Long, String> getErrors() {
            return errors;
        }
    }
}
