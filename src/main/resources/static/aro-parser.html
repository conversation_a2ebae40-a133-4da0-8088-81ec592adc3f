<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARO.ae Parser Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button.secondary {
            background-color: #2196F3;
        }
        .button.secondary:hover {
            background-color: #0b7dda;
        }
        .button.danger {
            background-color: #f44336;
        }
        .button.danger:hover {
            background-color: #d32f2f;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .status.success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .status.error {
            background-color: #f2dede;
            color: #a94442;
        }
        .status.info {
            background-color: #d9edf7;
            color: #31708f;
        }
        .status.warning {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .project-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
        }
        .project-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        .project-item:hover {
            background-color: #f5f5f5;
        }
        .project-details {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            display: none;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            display: none;
            margin: 10px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ARO.ae Parser Dashboard</h1>
        
        <div class="card">
            <h2>Parser Status</h2>
            <button id="checkStatus" class="button secondary">Check Status</button>
            <div id="statusResult" class="status info" style="display: none;"></div>
        </div>
        
        <div class="card">
            <h2>Fetch All Projects</h2>
            <button id="fetchProjects" class="button">Fetch Projects</button>
            <div id="fetchLoader" class="loader"></div>
            <div id="projectsResult" class="status info" style="display: none;"></div>
            <div id="projectList" class="project-list" style="display: none;"></div>
        </div>
        
        <div class="card">
            <h2>Parse Project</h2>
            <input type="text" id="projectSlug" placeholder="Enter project slug" style="padding: 8px; width: 300px;">
            <button id="parseProject" class="button">Parse Project</button>
            <div id="parseLoader" class="loader"></div>
            <div id="parseResult" class="status info" style="display: none;"></div>
            <div id="projectDetails" class="project-details">
                <pre id="projectJson"></pre>
            </div>
        </div>
        
        <div class="card">
            <h2>Parse All Projects</h2>
            <button id="parseAllProjects" class="button danger">Parse All Projects</button>
            <div id="parseAllLoader" class="loader"></div>
            <div id="parseAllResult" class="status info" style="display: none;"></div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check Status
            document.getElementById('checkStatus').addEventListener('click', function() {
                fetch('/api/parser/aro/status')
                    .then(response => response.json())
                    .then(data => {
                        const statusResult = document.getElementById('statusResult');
                        statusResult.textContent = `Status: ${data.status} - ${data.message}`;
                        statusResult.className = 'status success';
                        statusResult.style.display = 'block';
                    })
                    .catch(error => {
                        const statusResult = document.getElementById('statusResult');
                        statusResult.textContent = `Error: ${error.message}`;
                        statusResult.className = 'status error';
                        statusResult.style.display = 'block';
                    });
            });
            
            // Fetch Projects
            document.getElementById('fetchProjects').addEventListener('click', function() {
                const fetchLoader = document.getElementById('fetchLoader');
                const projectsResult = document.getElementById('projectsResult');
                const projectList = document.getElementById('projectList');
                
                fetchLoader.style.display = 'block';
                projectsResult.style.display = 'none';
                projectList.style.display = 'none';
                
                fetch('/api/parser/aro/projects')
                    .then(response => response.json())
                    .then(data => {
                        fetchLoader.style.display = 'none';
                        projectsResult.textContent = `Found ${data.length} projects`;
                        projectsResult.className = 'status success';
                        projectsResult.style.display = 'block';
                        
                        projectList.innerHTML = '';
                        data.forEach(slug => {
                            const projectItem = document.createElement('div');
                            projectItem.className = 'project-item';
                            projectItem.textContent = slug;
                            projectItem.addEventListener('click', function() {
                                document.getElementById('projectSlug').value = slug;
                            });
                            projectList.appendChild(projectItem);
                        });
                        
                        projectList.style.display = 'block';
                    })
                    .catch(error => {
                        fetchLoader.style.display = 'none';
                        projectsResult.textContent = `Error: ${error.message}`;
                        projectsResult.className = 'status error';
                        projectsResult.style.display = 'block';
                    });
            });
            
            // Parse Project
            document.getElementById('parseProject').addEventListener('click', function() {
                const projectSlug = document.getElementById('projectSlug').value.trim();
                if (!projectSlug) {
                    alert('Please enter a project slug');
                    return;
                }
                
                const parseLoader = document.getElementById('parseLoader');
                const parseResult = document.getElementById('parseResult');
                const projectDetails = document.getElementById('projectDetails');
                const projectJson = document.getElementById('projectJson');
                
                parseLoader.style.display = 'block';
                parseResult.style.display = 'none';
                projectDetails.style.display = 'none';
                
                fetch(`/api/parser/aro/projects/${projectSlug}`)
                    .then(response => response.json())
                    .then(data => {
                        parseLoader.style.display = 'none';
                        
                        if (data && data.length > 0) {
                            parseResult.textContent = `Successfully parsed project: ${data[0].title}`;
                            parseResult.className = 'status success';
                            parseResult.style.display = 'block';
                            
                            projectJson.textContent = JSON.stringify(data[0], null, 2);
                            projectDetails.style.display = 'block';
                        } else {
                            parseResult.textContent = 'No project data found';
                            parseResult.className = 'status warning';
                            parseResult.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        parseLoader.style.display = 'none';
                        parseResult.textContent = `Error: ${error.message}`;
                        parseResult.className = 'status error';
                        parseResult.style.display = 'block';
                    });
            });
            
            // Parse All Projects
            document.getElementById('parseAllProjects').addEventListener('click', function() {
                if (!confirm('This will parse all projects from ARO.ae. This may take a long time. Are you sure you want to continue?')) {
                    return;
                }
                
                const parseAllLoader = document.getElementById('parseAllLoader');
                const parseAllResult = document.getElementById('parseAllResult');
                
                parseAllLoader.style.display = 'block';
                parseAllResult.style.display = 'none';
                
                fetch('/api/parser/aro/parse-all', {
                    method: 'POST'
                })
                    .then(response => response.json())
                    .then(data => {
                        parseAllLoader.style.display = 'none';
                        parseAllResult.textContent = `${data.message}`;
                        parseAllResult.className = 'status success';
                        parseAllResult.style.display = 'block';
                    })
                    .catch(error => {
                        parseAllLoader.style.display = 'none';
                        parseAllResult.textContent = `Error: ${error.message}`;
                        parseAllResult.className = 'status error';
                        parseAllResult.style.display = 'block';
                    });
            });
        });
    </script>
</body>
</html>
