import * as k8s from '@pulumi/kubernetes';
import * as pulumi from '@pulumi/pulumi';

import { Tags } from './utils';
import {Env} from "./config";


// typed env vars
export type RuntimeEnv = {
}

type SecretArgs = {
  env: Env & RuntimeEnv;
  namespace: pulumi.Output<string>;
  tags: Tags
}

export function createSecret(name: string, args: SecretArgs, opts: pulumi.ComponentResourceOptions): k8s.core.v1.Secret {
  const secretData = Object.entries(args.env)
    .map(([k, v]) => {
      const value = pulumi.output(v).apply(_v => encodeSecret(_v));

      return [k, value];
    })
    .reduce<Record<string, pulumi.Output<string>>>((agg, [k, v]) => {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const key = k as string;
      // eslint-disable-next-line no-param-reassign
      agg[key] = v as pulumi.Output<string>;
      return agg;
    }, {});
  return new k8s.core.v1.Secret(`${name}-secret`, {
    metadata: {
      name: `${name}-secret`,
      namespace: args.namespace,
      labels: {
        ...args.tags,
      },
    },
    data: secretData,
  }, opts);
}

export function encodeSecret(secret: string): string {
  return Buffer.from(secret).toString('base64');
}

