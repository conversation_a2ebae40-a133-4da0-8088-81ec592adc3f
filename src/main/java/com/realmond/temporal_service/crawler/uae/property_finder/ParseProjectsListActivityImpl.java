package com.realmond.temporal_service.crawler.uae.property_finder;

import com.realmond.temporal_service.client.PropertyFinderClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ParseProjectsListActivityImpl implements ParseProjectsListActivity {

    private final SharedURLParser sharedURLParser;
    private final PropertyFinderClient propertyFinderClient;

    @Override
    public List<String> parseProjectsList(String htmlContent) {
        log.info("Parsing projects list from HTML content");
        List<String> shareUrls = sharedURLParser.parseShareUrls(htmlContent);
        log.info("Found {} share URLs", shareUrls.size());
        return shareUrls;
    }

    public List<String> fetchAndParseProjectsList(int pageNumber) {
        String htmlContent = propertyFinderClient.fetchProjectsList(pageNumber);
        return parseProjectsList(htmlContent);
    }
}
