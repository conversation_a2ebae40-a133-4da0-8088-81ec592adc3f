package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class SharedURLParser {

    private final ObjectMapper objectMapper;

    /**
     * Parses the HTML content of a projects list page to extract share URLs.
     * Assumes the relevant data is within a JSON script tag with id "__NEXT_DATA__".
     *
     * @param htmlContent The HTML content as a string.
     * @return A list of shareUrl strings found, or an empty list if not found or on error.
     */
    public List<String> parseShareUrls(String htmlContent) {
        if (htmlContent == null || htmlContent.isEmpty()) {
            log.warn("HTML content is null or empty. Cannot parse share URLs.");
            return Collections.emptyList();
        }

        try {
            Document doc = Jsoup.parse(htmlContent);
            Element scriptElement = doc.selectFirst("script#__NEXT_DATA__");

            if (scriptElement == null) {
                log.warn("Could not find script tag with id '__NEXT_DATA__' in the HTML.");
                return Collections.emptyList();
            }

            String jsonData = scriptElement.data();
            if (jsonData.isEmpty()) {
                log.warn("Script tag '__NEXT_DATA__' is empty.");
                return Collections.emptyList();
            }

            log.debug("Attempting to parse JSON data from script tag...");
            JsonNode rootNode = objectMapper.readTree(jsonData);
            log.debug("Successfully parsed root JSON node.");

            // Path for the project list page (projects.html)
            JsonNode projectsNode = rootNode.path("props").path("pageProps").path("searchResult").path("data").path("projects").path("developer");
            log.debug("Attempting path 'props.pageProps.searchResult.data.projects.developer'. Found node: {}", !projectsNode.isMissingNode());

            if (projectsNode.isMissingNode() || !projectsNode.isArray()) {
                 log.warn("Could not find 'developer' projects array node at the expected path 'props.pageProps.searchResult.data.projects.developer' in the JSON data.");
                 return Collections.emptyList(); // Return empty if list path not found
            }

            // Original list processing logic
            List<String> shareUrls = new ArrayList<>();
            log.debug("Iterating through {} project nodes found.", projectsNode.size());
            int index = 0;
            for (JsonNode projectNode : projectsNode) {
                 log.debug("Processing project node index: {}", index);
                JsonNode shareUrlNode = projectNode.path("shareUrl");
                if (!shareUrlNode.isMissingNode() && shareUrlNode.isTextual()) {
                    String url = shareUrlNode.asText();
                    shareUrls.add(url);
                    log.debug("  Found shareUrl: {}", url);
                } else {
                     log.debug("  shareUrl not found or not text in node index {}. Trying alternatives...", index);
                     JsonNode projectUrlNode = projectNode.path("projectUrl"); // Alternative key
                     if(!projectUrlNode.isMissingNode() && projectUrlNode.isTextual()) {
                        String url = projectUrlNode.asText();
                        shareUrls.add(url);
                        log.debug("    Found projectUrl: {}", url);
                     } else {
                         JsonNode urlNode = projectNode.path("url"); // Another alternative key
                         if (!urlNode.isMissingNode() && urlNode.isTextual()) {
                            String url = urlNode.asText();
                            shareUrls.add(url);
                            log.debug("      Found url: {}", url);
                         } else {
                            log.debug("      No valid URL field (shareUrl, projectUrl, url) found in project node index: {}", index);
                         }
                     }
                }
                index++;
            }

            log.info("Successfully parsed {} share URLs.", shareUrls.size());
            return shareUrls;

        } catch (JsonProcessingException e) {
            log.error("Error processing JSON data from script tag: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("An unexpected error occurred during parsing share URLs: {}", e.getMessage(), e);
        }

        return Collections.emptyList();
    }
}
