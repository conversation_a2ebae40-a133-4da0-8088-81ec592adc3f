package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Page;

/**
 * Interface for handling authentication to the property portal
 * Following the Single Responsibility Principle by isolating authentication logic
 */
public interface AuthenticationService {

    /**
     * Check if the current session is authenticated
     * @param page The page instance
     * @return true if authenticated, false otherwise
     */
    boolean isAuthenticated(Page page);

    /**
     * Authenticate to the property portal
     * @param page The page instance
     * @param username The username
     * @param password The password
     * @param pageWaitTimeSeconds Wait time in seconds
     */
    void authenticate(Page page, String username, String password, int pageWaitTimeSeconds);

    /**
     * Logout from the current session
     * @param page The page instance
     */
    void logout(Page page);
}
