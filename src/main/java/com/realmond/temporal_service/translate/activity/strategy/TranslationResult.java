package com.realmond.temporal_service.translate.activity.strategy;

import lombok.Getter;

// Add this class at the end of the file
@Getter
class TranslationResult {
    private final boolean success;
    private final String error;

    private TranslationResult(boolean success, String error) {
        this.success = success;
        this.error = error;
    }

    public static TranslationResult success() {
        return new TranslationResult(true, null);
    }

    public static TranslationResult error(String message) {
        return new TranslationResult(false, message);
    }
}
