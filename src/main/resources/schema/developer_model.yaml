---
"$schema": http://json-schema.org/draft-07/schema#
type: object
properties:
  developer_urn:
    type: string
  source_urn:
    type: string
  title:
    type: string
  location:
    $ref: common/location_model.yaml
  website:
    type: string
  logo_url:
    type: string
  description:
    type: string
  founded:
    type: string
    format: date
  employee_count:
    type: integer
  valuation:
    type: string
  government_relation:
    type: string
    enum:
      - public
      - private
      - mixed
  additional_data:
    additionalProperties: true
  external_id:
    type: string
  developer_stats:
    $ref: "#/definitions/developer_stats"
required:
  - developer_urn
  - source_urn
  - title
  - external_id
