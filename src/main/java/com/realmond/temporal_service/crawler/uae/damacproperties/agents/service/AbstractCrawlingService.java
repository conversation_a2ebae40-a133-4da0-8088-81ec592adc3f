package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Page;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Abstract base class for web crawling services.
 * Provides common functionality and template methods for concrete implementations.
 */
@Slf4j
public abstract class AbstractCrawlingService implements CrawlingService {

    // Flag to track if a crawl is currently running
    private final AtomicBoolean crawlInProgress = new AtomicBoolean(false);

    /**
     * Main scheduled method to run the crawler
     */
    @Override
    @Scheduled(cron = "${damacproperties.crawler.cron:0 0 */12 * * *}")
    public void startCrawling() {
        if (crawlInProgress.compareAndSet(false, true)) {
            try {
                log.info("Starting scheduled crawl");
                crawlProperties();
                log.info("Scheduled crawl completed successfully");
            } catch (Exception e) {
                log.error("Error during scheduled crawl: {}", e.getMessage(), e);
            } finally {
                crawlInProgress.set(false);
            }
        } else {
            log.warn("Scheduled crawl skipped - another crawl is already in progress");
        }
    }

    /**
     * Main crawling method with retry logic
     */
    @Override
    @Retryable(
        value = {Exception.class},
        exclude = {CrawlerException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 5000, multiplier = 2)
    )
    public void crawlProperties() {
        log.info("Starting property crawl process");

        // Track progress metrics
        long startTime = System.currentTimeMillis();

        // Get the browser manager
        BrowserManager browserManager = getBrowserManager();
        Page page = null;

        try {
            // Create a new page
            page = browserManager.newPage();

            // Perform the actual crawling
            performCrawl(page);

            // Calculate metrics
            long endTime = System.currentTimeMillis();
            double duration = (endTime - startTime) / 1000.0;
            log.info("Crawling completed in {:.2f} seconds", duration);

        } catch (Exception e) {
            log.error("Error during property crawl: {}", e.getMessage(), e);
            throw e;

        } finally {
            // Ensure page is properly closed
            if (page != null) {
                try {
                    browserManager.closePage(page);
                } catch (Exception e) {
                    log.warn("Error closing page: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * Get the browser manager instance to use
     * @return BrowserManager instance
     */
    protected abstract BrowserManager getBrowserManager();

    /**
     * Perform the actual crawling operations
     * @param page The playwright page to use
     */
    protected abstract void performCrawl(Page page);

    /**
     * Check if a crawl is currently in progress
     * @return true if a crawl is in progress, false otherwise
     */
    protected boolean isCrawlInProgress() {
        return crawlInProgress.get();
    }
}
