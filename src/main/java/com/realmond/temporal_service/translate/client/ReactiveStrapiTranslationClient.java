package com.realmond.temporal_service.translate.client;

import com.realmond.temporal_service.translate.dto.*;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import reactor.core.publisher.Mono;

@Headers({"Accept: application/json"})
public interface ReactiveStrapiTranslationClient {

    @RequestLine("POST /translation/single-translate")
    Mono<SingleTranslateResponse> singleTranslateReactive(SingleTranslateRequest request);

    @RequestLine("GET /translation/untranslated/{type}?locale={locale}&page={page}&pageSize={pageSize}")
    Mono<UnTranslatedEntities> getUntranslatedEntityIdsReactive(
            @Param("type") String type,
            @Param("locale") String locale,
            @Param("page") int page,
            @Param("pageSize") int pageSize
    );

    @RequestLine("GET /translation/types")
    Mono<UntranslatedTypes> getTranslatableTypesReactive();

    @RequestLine("POST /location/restore-parent-links")
    Mono<LocationRestoreResponse> restoreLocationParentLinksReactive();
}
