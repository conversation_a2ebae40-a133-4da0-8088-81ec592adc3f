---
"$schema": http://json-schema.org/draft-07/schema#
title: location
type: object
description: Structured information about the project's location.
properties:
  address:
    type: string
    description: The street address of the project.
  city:
    type: string
    description: The city in which the project is located.
  city_district:
    type: string
    description: The district or neighborhood of the city.
  state:
    type: string
    description: The state or region of the location.
  country:
    type: string
    description: The country of the location.
  zip:
    type: string
    description: The postal code for the location.
    pattern: "^[0-9]{5}(?:-[0-9]{4})?$"
required:
  - address
  - city
  - state
  - country
