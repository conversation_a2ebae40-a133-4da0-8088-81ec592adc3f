package com.realmond.temporal_service.common;

import lombok.Data;

/**
 * Represents a browser fingerprint with various properties that can be used
 * to simulate different browsers and devices when making HTTP requests.
 */
@Data
public class BrowserFingerprint {
    private String userAgent;
    private int viewportWidth;
    private int viewportHeight;
    private String language;
    private String platform;
    private int colorDepth;
    private double pixelRatio;
    private int timezoneOffset;
    private String sessionId;
    
    /**
     * Creates a new browser fingerprint with default values.
     */
    public BrowserFingerprint() {
        // Default values
        this.userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        this.viewportWidth = 1920;
        this.viewportHeight = 1080;
        this.language = "en-US,en;q=0.9";
        this.platform = "Windows";
        this.colorDepth = 24;
        this.pixelRatio = 1.0;
        this.timezoneOffset = 0;
        this.sessionId = "default-session";
    }
    
    /**
     * Converts the fingerprint to a JSON-like string for debugging.
     * @return A string representation of the fingerprint
     */
    @Override
    public String toString() {
        return "BrowserFingerprint{" +
                "userAgent='" + userAgent + '\'' +
                ", viewport=" + viewportWidth + "x" + viewportHeight +
                ", language='" + language + '\'' +
                ", platform='" + platform + '\'' +
                ", pixelRatio=" + pixelRatio +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }
}
