package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.Page;
import com.realmond.etl.model.AdModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.AdRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.AdRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * Main service for crawling Damac Properties using Browserless and Playwright.
 * This service orchestrates the entire crawling process.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public abstract class DamacPropertiesCrawlerService implements CrawlingService {

    private static final String BASE_URL = "https://www.damacproperties.com/en/buy/dubai/";

    // Dependencies injected through constructor
    private final BrowserManager browserManager;
    private final AuthenticationService authenticationService;
    private final NavigationService navigationService;
    private final PropertyParserService propertyParserService;
    private final AdRepository adRepository;

    @Value("${damacproperties.crawler.username}")
    private String username;

    @Value("${damacproperties.crawler.password}")
    private String password;

    @Value("${damacproperties.crawler.page-wait-time:5}")
    private int pageWaitTime;

    /**
     * Scheduled method to run the crawler according to configuration
     */
    @Scheduled(cron = "${damacproperties.crawler.cron:0 0 */12 * * *}")
    public void scheduledCrawl() {
        log.info("Starting scheduled crawl of Damac Properties");
        crawlProperties();
    }

    /**
     * Crawls the Damac Properties website to extract property information
     */
    public void crawlProperties() {
        log.info("Starting crawl of Damac Properties");

        Browser browser = null;
        Page page = null;

        try {
            // Get a browser instance
            try {
                browser = browserManager.getBrowser();
            } catch (CrawlerException e) {
                log.error("Failed to connect to Browserless service: {}", e.getMessage());
                throw new CrawlerException("Failed to connect to Browserless service. Please ensure Browserless is running at " +
                                          "the configured URL and is accessible.", e);
            }

            // Create a new page
            try {
                page = browserManager.newPage();
            } catch (CrawlerException e) {
                log.error("Failed to create a new page: {}", e.getMessage());
                throw new CrawlerException("Failed to create a new page. Please check Browserless service status.", e);
            }

            try {
                // Navigate to the base URL
                log.info("Navigating to base URL: {}", BASE_URL);
                browserManager.navigateTo(page, BASE_URL);

                // Authenticate if needed
                if (username != null && !username.isEmpty() && password != null && !password.isEmpty()) {
                    log.info("Authenticating with provided credentials");
                    authenticationService.authenticate(page, username, password, pageWaitTime);
                } else {
                    log.info("No credentials provided, skipping authentication");
                }

                // Configure filters and search parameters
                try {
                    log.info("Configuring search filters");
                    navigationService.configureFilters(page, pageWaitTime);
                } catch (CrawlerException e) {
                    if (e.getMessage().contains("Authentication required")) {
                        log.warn("Authentication required but no credentials provided or invalid credentials");
                        throw new CrawlerException("Authentication required to access the website. Please provide valid credentials.", e);
                    } else {
                        throw e;
                    }
                }

                // For simplicity, we'll just process a fixed number of pages
                int totalPages = 3; // Assuming 3 pages for testing
                log.info("Processing {} pages of properties", totalPages);

                int totalProperties = 0;

                // Process each page
                for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                    log.info("Processing page {} of {}", pageNum, totalPages);

                    if (pageNum > 1) {
                        // Navigate to the next page using the existing method
                        boolean hasNextPage = navigationService.goToNextPage(page, pageWaitTime);
                        if (!hasNextPage) {
                            log.info("No more pages available, stopping at page {}", pageNum - 1);
                            break;
                        }
                    }

                    try {
                        // Parse properties on current page
                        List<AdModel> properties = propertyParserService.parsePropertiesFromCurrentPage(page, pageWaitTime);

                        List<AdRecord> propertyRecords = properties.stream().map(property ->
                                     AdRecord.builder()
                                            .urn(property.getAdUrn())
                                            .data(property)
                                            .emittedAt(ZonedDateTime.now())
                                            .build()
                                ).toList();

                        log.info("Found {} properties on page {}", properties.size(), pageNum);
                        totalProperties += properties.size();

                        // Save properties to database
                        for (AdRecord property : propertyRecords) {
                            try {
                                adRepository.save(property);
                                log.debug("Saved property: {}", property.getUrn());
                            } catch (Exception e) {
                                log.error("Error saving property {}: {}", property.getUrn(), e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error processing page {}: {}", pageNum, e.getMessage(), e);
                    }
                }

                log.info("Completed crawl of Damac Properties. Total properties found: {}", totalProperties);
            } finally {
                // Close the page
                if (page != null) {
                    try {
                        page.close();
                        log.debug("Page closed successfully");
                    } catch (Exception e) {
                        log.warn("Error closing page: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error during crawl: {}", e.getMessage(), e);
            throw new CrawlerException("Failed to crawl Damac Properties", e);
        }
    }

    /**
     * Manual trigger for the crawler
     * Used by the controller for on-demand crawling
     */
    public void triggerCrawl() {
        log.info("Manual trigger received for Damac Properties crawler");
        try {
            crawlProperties();
            log.info("Manual crawl completed successfully");
        } catch (Exception e) {
            log.error("Error during manual crawl: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Helper method to save properties to the repository
     */
    private void saveProperties(List<AdRecord> properties) {
        properties.forEach(property -> {
            try {
                adRepository.save(property);
                log.debug("Saved property: {}", property.getUrn());
            } catch (Exception e) {
                log.error("Error saving property {}: {}", property.getUrn(), e.getMessage());
            }
        });
    }
}
