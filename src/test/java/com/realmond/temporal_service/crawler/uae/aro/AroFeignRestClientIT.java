package com.realmond.temporal_service.crawler.uae.aro;

import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import com.realmond.temporal_service.crawler.uae.aro.model.AroApiResponse;
import com.realmond.temporal_service.crawler.uae.aro.model.Amenity;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectDetail;
import com.realmond.temporal_service.crawler.uae.aro.model.ProjectSummary;
import com.realmond.temporal_service.crawler.uae.aro.model.UnitStats;
import com.realmond.temporal_service.crawler.uae.aro.model.Building;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;

import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.Map;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for {@link AroFeignRestClient} that hit the real ARO.ae endpoints.
 *
 * NOTE: These tests require internet access. They are disabled by default unless the
 * environment variable "RUN_INTEGRATION_TESTS" is set to "true".
 */
@SpringBootTest(classes = {AroFeignRestClientIT.TestConfig.class}, webEnvironment = WebEnvironment.NONE)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
class AroFeignRestClientIT {

    @Configuration
    @EnableFeignClients(basePackageClasses = AroFeignRestClient.class)
    @Import({
            FingerprintGenerator.class,
            com.realmond.temporal_service.crawler.uae.aro.AroSettings.class})
    @ImportAutoConfiguration({
            org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
            org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration.class,
            org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.class
    })
    static class TestConfig {
        // AroFeignConfiguration now handles missing ProxyManager gracefully via ObjectProvider
    }

    @Autowired
    private AroFeignRestClient aroClient;

    private ProjectSummary firstProject;

    @BeforeAll
    void setup() {
        AroApiResponse<ProjectSummary> projects = aroClient.getProjects(1, 10);
        assertThat(projects).isNotNull();
        assertThat(projects.getData()).isNotEmpty();
        firstProject = projects.getData().get(0);
    }

    @Test
    void testGetProjects() {
        AroApiResponse<ProjectSummary> projects = aroClient.getProjects(1, 5);
        assertThat(projects).isNotNull();
        assertThat(projects.getData()).isNotEmpty();
        assertThat(projects.getPaging()).isNotNull();
    }

    @Test
    void testGetProjectDetail() {
        ProjectDetail detail = aroClient.getProjectBySlug(firstProject.getSlug());
        assertThat(detail).isNotNull();
        assertThat(detail.getId()).isEqualTo(firstProject.getId());
        assertThat(detail.getTitle()).isEqualTo(firstProject.getTitle());
    }

    @Test
    void testGetAmenitiesAndUnitStats() {
        List<Amenity> amenities = aroClient.getProjectAmenities(firstProject.getId());
        assertThat(amenities).isNotNull();
        // Amenities list may be empty for some projects

        List<UnitStats> stats = aroClient.getUnitStats(firstProject.getId(), 1, 9);
        assertThat(stats).isNotNull();
    }

    @Test
    void testGetProjectBuildings() {
        List<Building> buildings = aroClient.getProjectBuildings(firstProject.getId(), "sqft");
        assertThat(buildings).isNotNull();
        // API may return empty list for some projects
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "MANUAL_INTEGRATION_TESTS", matches = "true")
    void testExploreAllProjectsAndPropertyTypes() {
        System.out.println("=== ARO.ae API Exploration ===");
        System.out.println("Fetching all projects and analyzing building data...\n");

        Set<String> allPropertyTypes = new HashSet<>();
        Set<String> allAmenityNames = new HashSet<>();
        Map<String, Integer> propertyTypeCount = new HashMap<>();
        Map<String, Integer> unitTypeCount = new HashMap<>();

        int totalProjects = 0;
        int projectsWithBuildings = 0;
        int totalBuildings = 0;

        try {
            // Start with first page to get total count
            AroApiResponse<ProjectSummary> firstPage = aroClient.getProjects(1, 30);
            assertThat(firstPage).isNotNull();

            if (firstPage.getPaging() == null) {
                System.out.println("No pagination info available, testing with first page only");
                return;
            }

            int totalPages = firstPage.getPaging().getTotal();
            System.out.printf("Found %d total projects across %d pages%n%n", firstPage.getPaging().getTotal(), totalPages);

            // Iterate through all pages
            for (int page = 1; page <= totalPages; page++) { // Analyze ALL projects, no limit
                System.out.printf("Processing page %d/%d...%n", page, totalPages);

                AroApiResponse<ProjectSummary> projects = aroClient.getProjects(page, 30);
                if (projects.getData() == null) continue;

                for (ProjectSummary project : projects.getData()) {
                    totalProjects++;

                    try {
                        // Small delay to be respectful to the API
                        Thread.sleep(100);

                        // Get building details for this project
                        List<Building> buildings = aroClient.getProjectBuildings(project.getId(), "sqft");

                        if (buildings != null && !buildings.isEmpty()) {
                            projectsWithBuildings++;
                            totalBuildings += buildings.size();

                            // Collect property types
                            for (Building building : buildings) {
                                if (building.getCategory() != null) {
                                    allPropertyTypes.add(building.getCategory());
                                    propertyTypeCount.merge(building.getCategory(), 1, Integer::sum);
                                }
                            }
                        }

                        // Get amenities for variety
                        try {
                            Thread.sleep(50);
                            List<Amenity> amenities = aroClient.getProjectAmenities(project.getId());
                            if (amenities != null) {
                                for (Amenity amenity : amenities) {
                                    if (amenity.getTitle() != null) {
                                        allAmenityNames.add(amenity.getTitle());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // Some projects might not have amenities, that's ok
                        }

                        // Get unit statistics
                        try {
                            Thread.sleep(50);
                            List<UnitStats> unitStats = aroClient.getUnitStats(project.getId(), 1, 10);
                            if (unitStats != null) {
                                for (UnitStats stat : unitStats) {
                                    String bedrooms = stat.getBedrooms() != null ? stat.getBedrooms() + " BR" : "Unknown BR";
                                    unitTypeCount.merge(bedrooms, stat.getCount() != null ? stat.getCount() : 1, Integer::sum);
                                }
                            }
                        } catch (Exception e) {
                            // Some projects might not have unit stats, that's ok
                        }

                    } catch (Exception e) {
                        System.out.printf("  Error processing project %s: %s%n", project.getTitle(), e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            System.out.printf("Error during exploration: %s%n", e.getMessage());
        }

        // Print comprehensive results
        System.out.println("\n=== EXPLORATION RESULTS ===");
        System.out.printf("Total Projects Analyzed: %d%n", totalProjects);
        System.out.printf("Projects with Buildings: %d%n", projectsWithBuildings);
        System.out.printf("Total Buildings Found: %d%n", totalBuildings);

        System.out.println("\n=== PROPERTY TYPES (BUILDING CATEGORIES) ===");
        allPropertyTypes.stream()
            .sorted()
            .forEach(type -> System.out.printf("- %s (%d buildings)%n", type, propertyTypeCount.get(type)));

        System.out.println("\n=== UNIT TYPES BY BEDROOMS ===");
        unitTypeCount.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> System.out.printf("- %s: %d units%n", entry.getKey(), entry.getValue()));

        if (!allAmenityNames.isEmpty()) {
            System.out.println("\n=== SAMPLE AMENITIES ===");
            allAmenityNames.stream()
                .sorted()
                .limit(20) // Show first 20 amenities
                .forEach(amenity -> System.out.printf("- %s%n", amenity));

            if (allAmenityNames.size() > 20) {
                System.out.printf("... and %d more amenities%n", allAmenityNames.size() - 20);
            }
        }

        System.out.println("\n=== TEST ASSERTIONS ===");
        assertThat(totalProjects).isGreaterThan(0);
        assertThat(allPropertyTypes).isNotEmpty();

        System.out.println("✅ All assertions passed!");
        System.out.println("=== END EXPLORATION ===\n");
    }
}
