import * as pulumi from "@pulumi/pulumi";
import * as k8s from "@pulumi/kubernetes";
import {stringify} from 'yaml';
import {Tags} from "./utils";
import {createHorizontalPodAutoscaler} from "./hpa";
import {createService} from "./service";
import {createSecret} from "./secret";
import {createDeployment} from "./deployment";
import {WorkerConfig} from "./config";

export type WorkerArgs = {
  appName: string;
  config: WorkerConfig;
  tags: Tags;
}

export const n8nPort = 5678;

const n8nNamespace = "n8n-etl";

export class Worker extends pulumi.ComponentResource {
  private readonly namespace: k8s.core.v1.Namespace;
  private readonly secret: k8s.core.v1.Secret;
  private readonly deployment: k8s.apps.v1.Deployment;
  // private readonly svc: k8s.core.v1.Service;
  private readonly hpa: k8s.autoscaling.v2.HorizontalPodAutoscaler;


  constructor(name: string, args: WorkerArgs, opts: pulumi.ComponentResourceOptions) {
    super("realmond:app:WorkflowWorker", name, {}, opts);
    const {appName, config, tags} = args;
    const defaultOpts = {...opts, parent: this};


    const env = config.containerEnv;

    this.namespace = new k8s.core.v1.Namespace(name, {
      metadata: {
        name,
      }
    }, defaultOpts);
    const namespace = this.namespace.metadata.name;
    const serviceArgs = {appName, namespace, tags};

    this.secret = createSecret(name, {env, namespace, tags}, defaultOpts);
    const configMap = new k8s.core.v1.ConfigMap("app-config", {
      metadata: {
        name: "app-config",
        namespace: namespace
      },
      data: {
        "application.yaml": stringify(config.applicationYaml)
      },
    }, defaultOpts);

    this.deployment = createDeployment(`worker`, {
      appYamlConfigMap: configMap,
      appName,
      namespace,
      tags,
      config,
      secret: this.secret
    }, defaultOpts);
    // const mainPort = this.deployment.spec.template.spec.containers[0].ports[0];
    // this.svc = createService("worker", {...serviceArgs, port: mainPort}, defaultOpts);
    this.hpa = createHorizontalPodAutoscaler("worker", {
      namespace,
      appName,
      tags,
    }, defaultOpts);

    this.registerOutputs({});
  }

  getOutput() {
    return {
      namespace: this.namespace.metadata.name,
      secret: this.secret.metadata.name,
    }
  }

}
