package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.Page;
import com.realmond.etl.model.AdModel;
import com.realmond.etl.model.ImageModel;
import com.realmond.etl.model.PriceModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.AdRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.AdRepository;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.BrowserManager;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.PropertyParserService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.UrnGeneratorService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.ImageStorageService;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.config.S3Config.S3Properties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Implementation of PropertyParserService for Damac Properties using Playwright
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DamacPropertyParserService implements PropertyParserService {

    /**
     * Helper class to store property data during extraction
     */
    private static class PropertyData {
        String projectName;
        String productName;
        String unit;
        String propertyType;
        String bedrooms;
        String areaSqftText;
        PriceModel price;
        String unitType;
        String viewType;
        String constructionPercent;
        String propertyStatus;
        String acd;
        String plotAreaText;
        String basePrice;
        String vat;
        String parking;
        Integer bedroomsInt;
        Double areaSqm;
        Integer parkingInt;
        String assignedTo;
        String pricePerSqft;
        List<Map<String, String>> paymentPlans = new ArrayList<>();
        List<String> attributes = new ArrayList<>();
        Map<String, Object> additionalData = new HashMap<>();
    }

    private final BrowserManager browserManager;
    private final UrnGeneratorService urnGeneratorService;
    private final AdRepository propertyRepository;
    private final ImageStorageService imageStorageService;
    private final S3Properties s3Properties;

    private static final String DEVELOPER_URN = "urn:realmond:developer:damac-properties";
    private static final String SOURCE_URN = "urn:source:agents.damacproperties.com";

    @Override
    public List<AdModel> parsePropertiesFromCurrentPage(Page page, int waitTimeSeconds) {
        log.info("Starting to parse properties from current page with wait time {} seconds", waitTimeSeconds);
        List<AdModel> properties = new ArrayList<>();

        int successCount = 0;
        int errorCount = 0;

        try {
            // Try both selectors to be compatible with both implementations
            String[] tableSelectors = {
                "table#projectSearchResultId",  // Python selector
                ".unitSearchResultsTable"       // Java selector
            };

            String foundSelector = null;
            for (String selector : tableSelectors) {
                log.debug("Trying to wait for property table with selector: {}", selector);
                try {
                    browserManager.waitForSelector(page, selector, 5000);
                    foundSelector = selector;
                    log.info("Found property table with selector: {}", selector);
                    break;
                } catch (Exception e) {
                    log.debug("Selector not found: {}", selector);
                }
            }

            if (foundSelector == null) {
                log.warn("No property table found with any selector, attempting to continue anyway");
                // Take a screenshot for debugging
                String screenshotPath = "missing-property-table-" + System.currentTimeMillis() + ".png";
                page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(screenshotPath)));
                log.warn("Screenshot saved to: {}", screenshotPath);
            }

            // Wait for the rows to be loaded
            browserManager.sleep(2000);

            // Try both row selectors to find property rows
            List<ElementHandle> propertyRows = new ArrayList<>();
            String[] rowSelectors = {
                "table#projectSearchResultId > tbody > tr",  // Python selector
                ".unitSearchResultsTable tbody tr"           // Java selector
            };

            for (String selector : rowSelectors) {
                log.debug("Trying to find property rows with selector: {}", selector);
                List<ElementHandle> rows = page.querySelectorAll(selector);
                if (!rows.isEmpty()) {
                    propertyRows = rows;
                    log.info("Found {} property rows with selector: {}", rows.size(), selector);
                    break;
                }
            }

            log.info("Found {} property rows on current page", propertyRows.size());

            if (propertyRows.isEmpty()) {
                log.warn("No property rows found on current page, checking for no-data message");
                if (browserManager.elementExists(page, ".no-data", 2000)) {
                    log.info("No data message found - no properties available on this page");
                    return properties;
                }

                // Take a screenshot to help debug the issue
                String screenshotPath = "no-property-rows-" + System.currentTimeMillis() + ".png";
                page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get(screenshotPath)));
                log.warn("No property rows found. Screenshot saved to: {}", screenshotPath);

                // Show page content for debugging
                log.debug("Page content excerpt: {}", page.content().substring(0, Math.min(1000, page.content().length())));
            }

            for (int i = 0; i < propertyRows.size(); i++) {
                try {
                    ElementHandle row = propertyRows.get(i);
                    String rowText = row.textContent();

                    if (rowText.trim().isEmpty()) {
                        log.debug("Skipping empty row at index {}", i);
                        continue;
                    }

                    log.debug("Parsing row {}: {}", i, rowText.substring(0, Math.min(50, rowText.length())) + "...");

                    AdModel property = parsePropertyFromRow(page, row, i);
                    if (property != null) {
                        properties.add(property);
                        successCount++;
                        log.debug("Successfully parsed property: {}", property.getAdUrn());
                    }
                } catch (Exception e) {
                    log.error("Error parsing property at index {}: {}", i, e.getMessage(), e);
                    errorCount++;
                    // Continue with next property
                }
            }

            log.info("Successfully parsed {} properties, with {} errors", successCount, errorCount);
            return properties;
        } catch (Exception e) {
            log.error("Error parsing properties from current page: {}", e.getMessage(), e);
            throw new CrawlerException("Failed to parse properties from current page", e);
        }
    }

    @Override
    public AdModel parseProperty(Page page, int index) {
        log.info("Starting to parse property at index {}", index);
        // For backward compatibility, convert index to row selector
        List<ElementHandle> propertyRows = page.querySelectorAll(".unitSearchResultsTable tbody tr");
        if (index < 0 || index >= propertyRows.size()) {
            log.warn("Index {} out of bounds for property rows (size: {})", index, propertyRows.size());
            return null;
        }

        ElementHandle row = propertyRows.get(index);
        return parsePropertyFromRow(page, row, index);
    }

    private AdModel parsePropertyFromRow(Page page, ElementHandle row, int index) {
        try {
            // Get the raw text content for debugging
            String rawRowText = row.textContent().trim();
            log.debug("Starting to parse property at index {}: {}", index, rawRowText.substring(0, Math.min(100, rawRowText.length())) + "...");

            // Initialize property data object to collect all extracted information
            PropertyData data = new PropertyData();

            try {
                extractProjectInfo(row, data);
                extractUnitInfo(row, data);
                extractProductInfo(row, data);
                extractPropertyType(row, data);
                extractBedrooms(row, data);
                extractArea(row, data);
                extractPaymentPlans(row, data);
                extractAttributes(row, data);
                extractAssignedTo(row, data);
                extractPrice(row, data);

                if (data.projectName != null && !data.projectName.isEmpty() &&
                    data.unit != null && !data.unit.isEmpty()) {

                    try {
                        log.debug("Attempting to expand details for property");
                        ElementHandle detailsDiv = expandDetailsRow(page, row, index);

                        if (detailsDiv != null) {
                            String detailsDivId = detailsDiv.getAttribute("id");
                            if (detailsDivId != null && !detailsDivId.isEmpty()) {
                                log.debug("Details div found with ID: {}", detailsDivId);

                                // Extract additional fields from expanded details
                                String additionalDetailsSelector = "div#" + detailsDivId + " > div.unitSearchAccordianContent > div.row > div:nth-child(2) > div.unitSearchAccordianRight > div.childRowHeader > div.row";

                                // Extract basic details from expanded section
                                extractUnitTypeFromDetails(page, additionalDetailsSelector, data);
                                extractConstructionPercentFromDetails(page, additionalDetailsSelector, data);
                                extractPropertyStatusFromDetails(page, additionalDetailsSelector, data);

                                // Extract view type using multiple approaches
                                extractViewTypeFromDetails(page, additionalDetailsSelector, data);

                                // Extract additional details from expanded section
                                extractPricePerSqftFromDetails(page, additionalDetailsSelector, data);
                                extractBasePriceFromDetails(page, additionalDetailsSelector, data);
                                extractVatFromDetails(page, additionalDetailsSelector, data);
                                extractParkingFromDetails(page, additionalDetailsSelector, data);

                                // Make sure to collapse details row
                                try {
                                    row.click();
                                    page.waitForTimeout(500);
                                } catch (Exception e) {
                                    log.debug("Error collapsing details row: {}", e.getMessage());
                                }

                                // Prepare additional data for the AdModel
                                prepareAdditionalData(page, data);

                                // Create and return the AdModel using the Builder pattern
                                AdModel.AdModelBuilderBase<?> adBuilder = buildAdModelBuilder(data);

                                // Extract and add images
                                List<ImageModel> imageModels = extractAndProcessImages(page, data);
                                if (!imageModels.isEmpty()) {
                                    addImagesToAdModel(adBuilder, imageModels);
                                }

                                AdModel adModel = adBuilder.build();

                                // Save the property to the database
                                savePropertyToDatabase(adModel);

                                // Log summary information
                                logPropertySummary(adModel, data, imageModels.size(), index);

                                return adModel;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Error extracting expanded details: {}", e.getMessage());
                    }
                }

                // If we have enough basic info but couldn't get expanded details, still create a minimal AdModel
                if (data.projectName != null && !data.projectName.isEmpty() &&
                    data.unit != null && !data.unit.isEmpty()) {

                    // Create a minimal AdModel with the available data
                    return createMinimalAdModel(data);
                }

                    log.warn("Missing required fields for property at index {}: project={}, unit={}",
                    index, data.projectName, data.unit);
                    return null;
            } catch (Exception e) {
                log.error("Error parsing property from row at index {}: {}", index, e.getMessage());
                e.printStackTrace();
                return null;
            }
        } catch (Exception e) {
            log.error("Error parsing property from row at index {}: {}", index, e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Extract project information from the row
     */
    private void extractProjectInfo(ElementHandle row, PropertyData data) {
        ElementHandle projectElement = row.querySelector("td:nth-child(4)");
        if (projectElement != null) {
            data.projectName = projectElement.textContent().trim();
            log.debug("Extracted project name: {}", data.projectName);
        }
    }

    /**
     * Extract unit information from the row
     */
    private void extractUnitInfo(ElementHandle row, PropertyData data) {
        ElementHandle unitElement = row.querySelector("td:nth-child(5)");
        if (unitElement != null) {
            data.unit = unitElement.textContent().trim();
            log.debug("Extracted unit ID: {}", data.unit);

            // If project name is empty but unit has a project identifier, use that
            if ((data.projectName == null || data.projectName.isEmpty()) && data.unit.contains(" - ")) {
                data.projectName = data.unit.substring(0, data.unit.indexOf(" - "));
                log.debug("Extracted project name from unit: {}", data.projectName);
            }
        }
    }

    /**
     * Extract product information from the row
     */
    private void extractProductInfo(ElementHandle row, PropertyData data) {
        ElementHandle productNameElement = row.querySelector("td:nth-child(3) > a");
        if (productNameElement != null) {
            data.productName = productNameElement.textContent().trim();

            // If project name is still missing, use product name
            if (data.projectName == null || data.projectName.isEmpty()) {
                data.projectName = data.productName;
                log.debug("Set project name from product name: {}", data.productName);
            }
        }
    }

    /**
     * Extract property type from the row
     */
    private void extractPropertyType(ElementHandle row, PropertyData data) {
        ElementHandle propertyTypeElement = row.querySelector("td:nth-child(6)");
        if (propertyTypeElement != null) {
            data.propertyType = propertyTypeElement.textContent().trim();
            log.debug("Extracted property type: {}", data.propertyType);
        }
    }

    /**
     * Parse area from text (e.g., "1,234.56 sq.ft.")
     */
    private double parseArea(String areaText) {
        if (areaText == null || areaText.trim().isEmpty()) {
            log.debug("Area text is null or empty, returning 0.0");
            return 0.0;
        }

        try {
            // Extract numeric value
            String numericPart = areaText.replaceAll("[^0-9.]", "");
            log.debug("Extracted numeric part from area text '{}': '{}'", areaText, numericPart);

            if (numericPart.isEmpty()) {
                log.debug("No numeric values found in area text, returning 0.0");
                return 0.0;
            }

            double areaSqft = Double.parseDouble(numericPart);
            double areaSqm = areaSqft * 0.092903;
            log.debug("Converted area from {} sqft to {} sqm", areaSqft, areaSqm);
            return areaSqm;
        } catch (Exception e) {
            log.warn("Could not parse area from text '{}': {}", areaText, e.getMessage());
            return 0.0;
        }
    }

    /**
     * Extract bedrooms information from the row
     */
    private void extractBedrooms(ElementHandle row, PropertyData data) {
        ElementHandle bedroomsElement = row.querySelector("td:nth-child(7)");
        if (bedroomsElement != null) {
            data.bedrooms = bedroomsElement.textContent().trim();
            log.debug("Extracted bedrooms: {}", data.bedrooms);

            // Parse bedrooms to integer following Python code
            if (data.bedrooms.equals("STD")) {
                data.bedroomsInt = 0;
            } else if (data.bedrooms.endsWith("BR")) {
                String bedroomDigits = data.bedrooms.split(" ")[0];
                try {
                    data.bedroomsInt = Integer.parseInt(bedroomDigits);
                } catch (NumberFormatException e) {
                    log.warn("Could not parse bedrooms: {}", data.bedrooms);
                }
            } else {
                // Try to extract digits
                String digits = data.bedrooms.replaceAll("[^0-9]", "");
                if (!digits.isEmpty()) {
                    try {
                        data.bedroomsInt = Integer.parseInt(digits);
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse bedrooms from digits: {}", digits);
                    }
                }
            }
            log.debug("Parsed bedrooms to integer: {}", data.bedroomsInt);
        }
    }

    /**
     * Extract area information from the row
     */
    private void extractArea(ElementHandle row, PropertyData data) {
        ElementHandle areaElement = row.querySelector("td:nth-child(8)");
        if (areaElement != null) {
            data.areaSqftText = areaElement.textContent().trim();
            log.debug("Extracted area: {}", data.areaSqftText);

            // Parse area to double and convert to square meters
            double areaSqft = parseArea(data.areaSqftText);
            if (areaSqft > 0) {
                // Convert to square meters (1 sq ft = 0.092903 sq m)
                data.areaSqm = areaSqft * 0.092903;
                log.debug("Converted area to sqm: {}", data.areaSqm);
            }
        }
    }

    /**
     * Extract payment plans from the row
     */
    private void extractPaymentPlans(ElementHandle row, PropertyData data) {
        try {
            List<ElementHandle> paymentPlanElements = row.querySelectorAll("td:nth-child(9) > select > option");
            for (ElementHandle paymentPlanElement : paymentPlanElements) {
                String optionValue = paymentPlanElement.getAttribute("value");
                String title = paymentPlanElement.textContent().trim();

                if (optionValue != null && !optionValue.equals("--None--")) {
                    Map<String, String> paymentPlan = new HashMap<>();
                    paymentPlan.put("title", title);
                    paymentPlan.put("option_value", optionValue);
                    data.paymentPlans.add(paymentPlan);
                }
            }
            log.debug("Extracted {} payment plans", data.paymentPlans.size());
        } catch (Exception e) {
            log.warn("Failed to extract payment plans: {}", e.getMessage());
        }
    }

    /**
     * Extract attributes from the row
     */
    private void extractAttributes(ElementHandle row, PropertyData data) {
        try {
            List<ElementHandle> attributeElements = row.querySelectorAll("td:nth-child(10) *");
            for (ElementHandle attributeElement : attributeElements) {
                String attribute = attributeElement.getAttribute("title");
                if (attribute != null && !attribute.trim().isEmpty()) {
                    data.attributes.add(attribute);
                }
            }
            log.debug("Extracted {} attributes", data.attributes.size());
        } catch (Exception e) {
            log.warn("Failed to extract attributes: {}", e.getMessage());
        }
    }

    /**
     * Extract assigned to information from the row
     */
    private void extractAssignedTo(ElementHandle row, PropertyData data) {
        try {
            ElementHandle assignedToElement = row.querySelector("td:nth-child(11)");
            if (assignedToElement != null) {
                data.assignedTo = assignedToElement.textContent().trim();
                log.debug("Extracted assigned to: {}", data.assignedTo);
            }
        } catch (Exception e) {
            log.warn("Failed to extract assigned to: {}", e.getMessage());
        }
    }

    /**
     * Generate project URN following Python logic
     */
    private String generateProjectUrn(String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            log.warn("Project name is null or empty, using default URN");
            return "urn:realmond:project:damac-properties:unknown";
        }
        String slug = slugify(projectName);
        String urn = "urn:realmond:project:damac-properties:" + slug;
        log.debug("Generated project URN: {} from name: {}", urn, projectName);
        return urn;
    }

    /**
     * Generate ad URN following Python logic
     */
    private String generateAdUrn(String adId, String projectName) {
        if (adId == null || adId.isEmpty()) {
            log.warn("Ad ID is null or empty, using UUID for URN");
            String uuid = java.util.UUID.randomUUID().toString();
            return "urn:realmond:ad:damac-properties:unknown:" + uuid;
        }

        String projectSlug = projectName != null ? slugify(projectName) : "unknown";
        String adSlug = slugify(adId.replace("/", "-"));
        String urn = "urn:realmond:ad:damac-properties:" + projectSlug + ":" + adSlug;
        log.debug("Generated ad URN: {} from adId: {} and project: {}", urn, adId, projectName);
        return urn;
    }

    /**
     * Generate building URN following Python logic
     */
    private String generateBuildingUrn(String unitId, String projectName) {
        if (unitId == null || unitId.isEmpty() || projectName == null || projectName.isEmpty()) {
            return null;
        }
        String projectUrn = generateProjectUrn(projectName);
        String buildingSlug = extractBuildingSlug(unitId);
        return String.format("%s:building:%s", projectUrn, buildingSlug);
    }

    /**
     * Extract building slug from unit ID
     */
    private String extractBuildingSlug(String unitId) {
        // Simple implementation - in a real scenario would extract building information from unit ID
        return slugify(unitId);
    }

    /**
     * Slugify text following Python logic
     */
    private String slugify(String text) {
        if (text == null) return "";

        // Apply replacements similar to Python code
        String result = text.toLowerCase()
            .replace("@", "at")
            .replace("&", "and")
            .replace("%", "percent")
            .replace("|", "or");

        // Remove special characters and replace spaces with hyphens
        result = result.replaceAll("[^a-z0-9\\s-]", "")
            .replaceAll("\\s+", "-")
            .replaceAll("-+", "-");

        return result.trim();
    }

    /**
     * Prepare additional data for the AdModel
     */
    private void prepareAdditionalData(Page page, PropertyData data) {
        // Initialize additional data map for extended properties
        data.additionalData = new HashMap<>();

        // Add area in sqft
        if (data.areaSqftText != null && !data.areaSqftText.isEmpty()) {
            data.additionalData.put("area_sqft", data.areaSqftText);
        }

        // Add unit type
        if (data.unitType != null && !data.unitType.isEmpty()) {
            data.additionalData.put("unit_type", data.unitType);
        }

        // Add view type details if available
        if (data.viewType != null && !data.viewType.isEmpty()) {
            // Store the view type directly in additionalData
            data.additionalData.put("view_type", data.viewType);

            // Also store detailed information for debugging
            Map<String, String> viewTypeDetails = new HashMap<>();
            viewTypeDetails.put("view_type_value", data.viewType);

            // Try to capture the full HTML context for debugging
            try {
                // Get the entire view type row HTML for debugging
                ElementHandle viewTypeRow = page.querySelector(".row:has(.childRowBox:has(h6:text('View Type')))");
                if (viewTypeRow != null) {
                    String rowHtml = page.evaluate("el => el.outerHTML", viewTypeRow).toString();
                    viewTypeDetails.put("view_type_row_html", rowHtml);
                }
            } catch (Exception e) {
                log.debug("Error capturing view type HTML context: {}", e.getMessage());
            }

            data.additionalData.put("view_type_details", viewTypeDetails);
        }

        // Add base price
        if (data.basePrice != null && !data.basePrice.isEmpty()) {
            data.additionalData.put("base_price", data.basePrice);
        }

        // Add VAT
        if (data.vat != null && !data.vat.isEmpty()) {
            data.additionalData.put("vat", data.vat);
        }

        // Add price per sqft
        if (data.pricePerSqft != null && !data.pricePerSqft.isEmpty()) {
            data.additionalData.put("price_per_sqft", data.pricePerSqft);
        }

        // Add plot area if available
        if (data.plotAreaText != null && !data.plotAreaText.isEmpty()) {
            data.additionalData.put("plot_area_sqft", data.plotAreaText);
        }

        // Add assigned to
        if (data.assignedTo != null && !data.assignedTo.isEmpty()) {
            data.additionalData.put("assigned_to", data.assignedTo);
        }

        // Add payment plans
        if (!data.paymentPlans.isEmpty()) {
            data.additionalData.put("payment_plans", data.paymentPlans);
        }

        // Add project name to additionalData
        if (data.productName != null && !data.productName.isEmpty()) {
            data.additionalData.put("product_name", data.productName);
        }

        if (data.projectName != null && !data.projectName.isEmpty()) {
            data.additionalData.put("project_name", data.projectName);
        }

        // Add property type to additionalData
        if (data.propertyType != null) {
            data.additionalData.put("property_type", data.propertyType.toUpperCase());
        }

        // Create payment plan models list if payment plans exist
        if (!data.paymentPlans.isEmpty()) {
            List<Map<String, String>> paymentPlanModels = new ArrayList<>();
            for (Map<String, String> plan : data.paymentPlans) {
                Map<String, String> modelPlan = new HashMap<>();
                modelPlan.put("name", plan.get("title"));
                if (data.projectName != null && !data.projectName.isEmpty()) {
                    modelPlan.put("project_urn", generateProjectUrn(data.projectName));
                }
                paymentPlanModels.add(modelPlan);
            }
            data.additionalData.put("payment_plan_models", paymentPlanModels);
        }
    }

    /**
     * Build the AdModel builder from the property data
     */
    private AdModel.AdModelBuilderBase<?> buildAdModelBuilder(PropertyData data) {
        AdModel.AdModelBuilderBase<?> adBuilder = AdModel.builder()
            .withDeveloperUrn(DEVELOPER_URN)
            .withSourceUrn(SOURCE_URN)
            .withExternalId(data.unit)
            .withAdUrn(generateAdUrn(data.unit, data.projectName))
            .withProjectUrn(generateProjectUrn(data.projectName))
            .withPrice(data.price)
            .withBuildingUrn(generateBuildingUrn(data.unit, data.projectName))
            .withAreaSqm(data.areaSqm)
            .withBedrooms(data.bedroomsInt)
            .withBedroomsTitle(data.bedrooms)
            .withPropertyType(mapPropertyType(data.propertyType))
            .withViewType(data.viewType)
            .withFurnished(AdModel.Furnished.NONE) // Default to NONE
            .withPurpose(AdModel.Purpose.FOR_SALE) // Default to FOR_SALE
            .withParking(data.parkingInt)
            .withAdditionalData(data.additionalData);

        // Add completion status if available
        if (data.propertyStatus != null) {
            if (data.propertyStatus.equalsIgnoreCase("Ready") ||
                data.propertyStatus.equalsIgnoreCase("Completed") ||
                data.propertyStatus.contains("100%")) {
                adBuilder.withCompletionStatus(AdModel.ConstructionStatusModel.FINISHED);
            } else if (data.propertyStatus.equalsIgnoreCase("Under Construction") ||
                      data.propertyStatus.contains("Construction")) {
                adBuilder.withCompletionStatus(AdModel.ConstructionStatusModel.ACTIVE);
            }
        }

        // Set completion date if available
        if (data.acd != null && !data.acd.isEmpty()) {
            adBuilder.withCompletionDate(data.acd);
        }

        return adBuilder;
    }

    /**
     * Extract and process images for the property
     */
    private List<ImageModel> extractAndProcessImages(Page page, PropertyData data) {
        List<ImageModel> imageModels = new ArrayList<>();
        try {
            // Get the property unit identifier to filter images
            String unitIdentifier = data.unit.replace("/", "").toUpperCase();
            String projectIdentifier = data.projectName != null ? data.projectName.replaceAll("[^A-Za-z0-9]", "").toUpperCase() : "";

            log.debug("Starting to extract images for property: {} ({})", data.unit, data.projectName);
            log.debug("Unit identifier for image filtering: {}", unitIdentifier);
            log.debug("Project identifier for image filtering: {}", projectIdentifier);

            // Try to find images in the carousel
            List<ElementHandle> carouselImageElements = page.querySelectorAll("div.carousel-inner img[src]");
            log.debug("Found {} carousel images to check", carouselImageElements.size());

            int carouselImagesAdded = 0;
            for (ElementHandle imageElement : carouselImageElements) {
                String imgSrc = imageElement.getAttribute("src");
                if (imgSrc != null && !imgSrc.isEmpty() && !imgSrc.contains("AP_UnitSearch")) {
                    log.debug("Checking carousel image: {}", imgSrc);
                    // Only include images specific to this property
                    if (isPropertySpecificImage(imgSrc, unitIdentifier, projectIdentifier)) {
                        ImageModel image = new ImageModel();
                        try {
                            image.setUrl(new URI(imgSrc));
                            imageModels.add(image);
                            carouselImagesAdded++;
                            log.debug("Added carousel image #{}: {}", carouselImagesAdded, imgSrc);

                            // Limit to 3 images per property
                            if (imageModels.size() >= 3) {
                                log.debug("Reached maximum of 3 images, stopping image search");
                                break;
                            }
                        } catch (Exception e) {
                            log.warn("Invalid image URL: {}", imgSrc);
                        }
                    } else {
                        log.debug("Skipping non-property-specific carousel image: {}", imgSrc);
                    }
                } else {
                    log.debug("Skipping empty or AP_UnitSearch carousel image: {}", imgSrc);
                }
            }

            log.debug("Added {} property-specific images from carousel", carouselImagesAdded);

            // If no images found in carousel, try thumbnails
            if (imageModels.isEmpty()) {
                log.debug("No carousel images found, checking thumbnails");
                List<ElementHandle> thumbnailImageElements = page.querySelectorAll("ul.hide-bullets img[src]");
                log.debug("Found {} thumbnail images to check", thumbnailImageElements.size());

                int thumbnailImagesAdded = 0;
                for (ElementHandle thumbnailElement : thumbnailImageElements) {
                    String imgSrc = thumbnailElement.getAttribute("src");
                    if (imgSrc != null && !imgSrc.isEmpty() && !imgSrc.contains("AP_UnitSearch")) {
                        log.debug("Checking thumbnail image: {}", imgSrc);
                        // Only include images specific to this property
                        if (isPropertySpecificImage(imgSrc, unitIdentifier, projectIdentifier)) {
                            ImageModel image = new ImageModel();
                            try {
                                image.setUrl(new URI(imgSrc));
                                imageModels.add(image);
                                thumbnailImagesAdded++;
                                log.debug("Added thumbnail image #{}: {}", thumbnailImagesAdded, imgSrc);

                                // Limit to 3 images per property
                                if (imageModels.size() >= 3) {
                                    log.debug("Reached maximum of 3 images, stopping image search");
                                    break;
                                }
                            } catch (Exception e) {
                                log.warn("Invalid thumbnail URL: {}", imgSrc);
                            }
                        } else {
                            log.debug("Skipping non-property-specific thumbnail image: {}", imgSrc);
                        }
                    } else {
                        log.debug("Skipping empty or AP_UnitSearch thumbnail image: {}", imgSrc);
                    }
                }

                log.debug("Added {} property-specific images from thumbnails", thumbnailImagesAdded);
            }

            log.debug("Total property-specific images found: {}", imageModels.size());
            if (!imageModels.isEmpty()) {
                log.debug("Image URLs:");
                for (ImageModel img : imageModels) {
                    log.debug(" - {}", img.getUrl());
                }
            } else {
                log.debug("No property-specific images found");
            }
        } catch (Exception e) {
            log.warn("Error extracting images: {}", e.getMessage(), e);
        }

        return imageModels;
    }

    /**
     * Add images to the AdModel builder
     */
    private void addImagesToAdModel(AdModel.AdModelBuilderBase<?> adBuilder, List<ImageModel> imageModels) {
        try {
            log.info("Uploading {} images to S3 storage", imageModels.size());
            List<ImageModel> uploadedImages = imageModels.stream()
                .map(imageStorageService::uploadImage)
                .collect(Collectors.toList());

            // Filter out any images that failed to upload (URL remained the same)
            List<ImageModel> successfulUploads = uploadedImages.stream()
                .filter(img -> img.getUrl() != null && img.getUrl().toString().contains(s3Properties.getBucket()))
                .collect(Collectors.toList());

            log.info("Successfully uploaded {} out of {} images to S3",
                    successfulUploads.size(), imageModels.size());

            if (!successfulUploads.isEmpty()) {
                adBuilder.withImages(successfulUploads);
            } else {
                // If no uploads succeeded, use original images
                adBuilder.withImages(imageModels);
                log.warn("Using original image URLs as S3 upload failed for all images");
            }
        } catch (Exception e) {
            log.error("Error uploading images to S3: {}", e.getMessage(), e);
            // Fall back to original images if upload fails
            adBuilder.withImages(imageModels);
        }
    }

    /**
     * Save property to database
     */
    private void savePropertyToDatabase(AdModel adModel) {
        try {
            AdRecord adRecord = AdRecord.builder()
                .urn(adModel.getAdUrn())
                .data(adModel)
                .emittedAt(ZonedDateTime.now())
                .build();
            propertyRepository.save(adRecord);
            log.debug("Saving property {} to database", adModel.getAdUrn());
        } catch (Exception e) {
            log.error("Error saving property {} to database: {}", adModel.getAdUrn(), e.getMessage());
        }
    }

    /**
     * Log property summary information
     */
    private void logPropertySummary(AdModel adModel, PropertyData data, int imageCount, int index) {
        log.debug("Successfully built AdModel with URN: {}", adModel.getAdUrn());
        log.debug("Finished parsing property at index {}: {}", index, data.unit);

        // At the end of successful parsing, add a summary log
        log.info("Successfully parsed property: project={}, unit={}, type={}, bedrooms={}, area={}, price={}, images={}",
            data.projectName,
            data.unit,
            data.propertyType,
            data.bedrooms,
            data.areaSqm,
            data.price != null ? data.price.getValue() : "N/A",
            imageCount);
    }

    /**
     * Create a minimal AdModel with the available data
     */
    private AdModel createMinimalAdModel(PropertyData data) {
        // Generate URNs
        String projectUrn = generateProjectUrn(data.projectName);
        String adUrn = generateAdUrn(data.unit, data.projectName);

        // Create a basic AdModel with available information
        Map<String, Object> additionalData = new HashMap<>();
        if (data.productName != null) additionalData.put("product_name", data.productName);
        if (data.projectName != null) additionalData.put("project_name", data.projectName);
        if (data.propertyType != null) additionalData.put("property_type", data.propertyType);
        if (data.areaSqftText != null) additionalData.put("area_sqft", data.areaSqftText);
        if (data.assignedTo != null) additionalData.put("assigned_to", data.assignedTo);
        if (!data.attributes.isEmpty()) additionalData.put("attributes", data.attributes);
        if (!data.paymentPlans.isEmpty()) additionalData.put("payment_plans", data.paymentPlans);

        // Ensure price exists
        if (data.price == null) {
            data.price = new PriceModel();
            data.price.setCurrency("AED");
            data.price.setValue(0.0);
        }

        // For the minimal builder case, ensure we have a default view type
        if (data.viewType == null && data.projectName != null) {
            // Use the project name as a fallback
            data.viewType = data.projectName;
            log.debug("Setting default view type to project name: '{}'", data.viewType);
        }

        return AdModel.builder()
            .withAdUrn(adUrn)
            .withExternalId(data.unit)
            .withProjectUrn(projectUrn)
            .withDeveloperUrn(DEVELOPER_URN)
            .withSourceUrn(SOURCE_URN)
            .withPrice(data.price)
            .withAreaSqm(data.areaSqm)
            .withBedrooms(data.bedroomsInt)
            .withBedroomsTitle(data.bedrooms)
            .withPropertyType(mapPropertyType(data.propertyType))
            .withPurpose(AdModel.Purpose.FOR_SALE)
            .withViewType(data.viewType) // Ensure view type is set
            .withAdditionalData(additionalData)
            .build();
    }

    @Override
    public boolean saveProperties(List<AdModel> properties) {
        log.info("Saving {} properties to database", properties.size());
        int successCount = 0;

        try {
        List<AdRecord> propertyRecords = properties.stream().map(property ->
                AdRecord.builder()
                        .urn(property.getAdUrn())
                        .data(property)
                        .emittedAt(ZonedDateTime.now())
                        .build()
        ).toList();
        for (AdModel property : properties) {
            try {
                // Make sure required fields are set
                if (property.getPropertyType() == null) {
                    property.setPropertyType(AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK);
                    log.info("Setting default propertyType for ad {}", property.getAdUrn());
                }

                if (property.getCompletionStatus() == null) {
                    property.setCompletionStatus(AdModel.ConstructionStatusModel.ACTIVE);
                    log.info("Setting default completionStatus for ad {}", property.getAdUrn());
                }

                if (property.getFurnished() == null) {
                    property.setFurnished(AdModel.Furnished.NONE);
                    log.info("Setting default furnished status for ad {}", property.getAdUrn());
                }

                if (property.getPurpose() == null) {
                    property.setPurpose(AdModel.Purpose.FOR_SALE);
                    log.info("Setting default purpose for ad {}", property.getAdUrn());
                }

                // Check if the property already exists and update or insert accordingly
                var existingPropertyOptional = propertyRepository.findById(property.getAdUrn());
                if (existingPropertyOptional.isPresent()) {
                    log.debug("Property already exists, updating: {}", property.getAdUrn());
                    propertyRepository.save(AdRecord.builder()
                            .urn(property.getAdUrn())
                            .data(property)
                            .emittedAt(ZonedDateTime.now())
                            .build());
                } else {
                    log.info("Saving new property: {}", property.getAdUrn());
                    propertyRepository.save(AdRecord.builder()
                            .urn(property.getAdUrn())
                            .data(property)
                            .emittedAt(ZonedDateTime.now())
                            .build());
                }
                    successCount++;
            } catch (Exception e) {
                log.error("Error saving property: {}", e.getMessage(), e);
            }
        }

            log.info("Successfully saved {}/{} properties to database", successCount, properties.size());
        return true;
        } catch (Exception e) {
            log.error("Error saving properties to database: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Maps the property type from string to the appropriate enum value
     * @param propertyType The property type as a string
     * @return The PropertyTypeModel enum value
     */
    private AdModel.PropertyTypeModel mapPropertyType(String propertyType) {
        if (propertyType == null || propertyType.isEmpty()) {
            log.debug("Property type is null or empty, defaulting to RESIDENTIAL_FALLBACK");
            return AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
        }

        String type = propertyType.toLowerCase();
        log.debug("Mapping property type: '{}'", type);

        if (type.contains("apartment")) {
            log.debug("Mapped property type '{}' to APARTMENT", type);
            return AdModel.PropertyTypeModel.APARTMENT;
        } else if (type.contains("duplex")) {
            log.debug("Mapped property type '{}' to DUPLEX", type);
            return AdModel.PropertyTypeModel.DUPLEX;
        } else if (type.contains("retail")) {
            log.debug("Mapped property type '{}' to RETAIL", type);
            return AdModel.PropertyTypeModel.RETAIL;
        } else if (type.contains("villa")) {
            log.debug("Mapped property type '{}' to VILLA", type);
            return AdModel.PropertyTypeModel.VILLA;
        } else if (type.contains("townhouse")) {
            log.debug("Mapped property type '{}' to TOWNHOUSE", type);
            return AdModel.PropertyTypeModel.TOWNHOUSE;
        } else if (type.contains("office")) {
            log.debug("Mapped property type '{}' to OFFICE", type);
            return AdModel.PropertyTypeModel.OFFICE;
        } else if (type.contains("penthouse")) {
            log.debug("Mapped property type '{}' to PENTHOUSE", type);
            return AdModel.PropertyTypeModel.PENTHOUSE;
        } else if (type.contains("hotel")) {
            log.debug("Mapped property type '{}' to HOTEL_APARTMENT", type);
            return AdModel.PropertyTypeModel.HOTEL_APARTMENT;
        } else if (type.contains("commercial")) {
            log.debug("Mapped property type '{}' to COMMERCIAL_FALLBACK", type);
            return AdModel.PropertyTypeModel.COMMERCIAL_FALLBACK;
        } else {
            log.debug("Could not map property type '{}', defaulting to RESIDENTIAL_FALLBACK", type);
            return AdModel.PropertyTypeModel.RESIDENTIAL_FALLBACK;
        }
    }

    /**
     * Extract price information from the row
     */
    private void extractPrice(ElementHandle row, PropertyData data) {
        ElementHandle priceElement = row.querySelector("td:nth-child(12) > span");
        if (priceElement != null) {
            String priceText = priceElement.textContent().trim();
            log.debug("Extracted raw price text: {}", priceText);

            // Extract the price value using our helper method
            String priceValue = extractNumericValue(priceText);
            log.debug("Extracted numeric price value: '{}'", priceValue);

            if (priceValue != null && !priceValue.isEmpty()) {
                try {
                    double value = Double.parseDouble(priceValue);
                    log.debug("Successfully parsed price value: {}", value);

                    // Sanity check - if the price is extremely large, it's likely wrong
                    if (value > 1_000_000_000) { // Over 1 billion (unlikely real estate price)
                        log.warn("Price value {} seems unreasonably large - attempting to fix", value);

                        // Try to detect the actual price from the original text
                        // Common pattern: 499,800 AED or AED 499,800
                        String simpleDigits = priceText.replaceAll("[^0-9]", "");

                        // If the number of digits is reasonable for a property price (5-7 digits typically)
                        if (simpleDigits.length() >= 5 && simpleDigits.length() <= 7) {
                            try {
                                value = Double.parseDouble(simpleDigits);
                                log.debug("Corrected price to a more reasonable: {}", value);
                            } catch (NumberFormatException e) {
                                log.warn("Failed to convert simple digits to price: {}", simpleDigits);
                                // Keep the original value if we can't fix it
                            }
                        } else {
                            // If the price is still too large, use a default value
                            log.warn("Could not determine a reasonable price, using 0.0");
                            value = 0.0;
                        }
                    }

                    // Create the price model
                    data.price = new PriceModel();
                    data.price.setCurrency("AED");
                    data.price.setValue(value);
                    log.debug("Created price model: currency={}, value={}", data.price.getCurrency(), data.price.getValue());
                } catch (NumberFormatException e) {
                    log.warn("Failed to parse price value '{}': {}", priceValue, e.getMessage());
                    // Create a default price model
                    data.price = new PriceModel();
                    data.price.setCurrency("AED");
                    data.price.setValue(0.0);
                    log.debug("Created default price model after parsing failure");
                }
            } else {
                log.warn("Empty price value extracted from '{}'", priceText);
                // Create a default price model
                data.price = new PriceModel();
                data.price.setCurrency("AED");
                data.price.setValue(0.0);
                log.debug("Created default price model due to empty price value");
            }
        } else {
            // If no price element found, create default price to avoid null
            log.warn("No price element found, creating default price");
            data.price = new PriceModel();
            data.price.setCurrency("AED");
            data.price.setValue(0.0);
        }
    }

    /**
     * Expand details row to get additional information
     */
    private ElementHandle expandDetailsRow(Page page, ElementHandle row, int index) {
        try {
            // Click on the row to expand details
            row.click();
            page.waitForTimeout(1500);

            // Following Python calculation for row indices
            int adDetailsId = index + 1;
            int adDetails2Id = index + 2;

            // Get the next row which contains the details
            String detailsSelector = String.format("#projectSearchResultId > tbody > tr:nth-child(%d) > td:first-child > div", adDetails2Id);
            ElementHandle detailsDiv = null;

            try {
                // First try the specific selector from Python
                detailsDiv = page.querySelector(detailsSelector);

                // If that fails, try the more generic selector as fallback
                if (detailsDiv == null) {
                    detailsDiv = page.querySelector("tr.childRow > td > div");
                }
            } catch (Exception e) {
                log.debug("Error finding details div using specific selector: {}", e.getMessage());
                // Try the more generic selector as fallback
                try {
                    detailsDiv = page.querySelector("tr.childRow > td > div");
                } catch (Exception e2) {
                    log.debug("Error finding details div using generic selector: {}", e2.getMessage());
                }
            }

            return detailsDiv;
        } catch (Exception e) {
            log.warn("Error expanding details row: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Checks if an image URL is specific to the current property
     * @param imgSrc The image URL
     * @param unitIdentifier The unit identifier
     * @param projectIdentifier The project identifier
     * @return true if the image is specific to this property
     */
    private boolean isPropertySpecificImage(String imgSrc, String unitIdentifier, String projectIdentifier) {
        log.debug("Checking if image is specific to property: {}", imgSrc);
        log.debug("Unit identifier: {}, Project identifier: {}", unitIdentifier, projectIdentifier);

        boolean isSpecific = imgSrc.toUpperCase().contains(unitIdentifier) ||
                             (projectIdentifier.length() > 2 && imgSrc.toUpperCase().contains(projectIdentifier));

        log.debug("Image {} is{} specific to property", imgSrc, isSpecific ? "" : " not");
        return isSpecific;
    }

    /**
     * Extract unit type from expanded details
     */
    private void extractUnitTypeFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle unitTypeElement = page.querySelector(additionalDetailsSelector + " > div:first-child > div:first-child > div.childRowBox");
        if (unitTypeElement != null) {
            String unitTypeText = unitTypeElement.textContent().trim();
            if (unitTypeText.contains("Unit Type")) {
                data.unitType = unitTypeText.replace("Unit Type", "").trim();
            } else {
                data.unitType = unitTypeText;
            }
            log.debug("Extracted unit type: {}", data.unitType);
        }
    }

    /**
     * Extract construction percentage from expanded details
     */
    private void extractConstructionPercentFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle constructionPercentElement = page.querySelector(additionalDetailsSelector + " > div:first-child > div:nth-child(2) > div.childRowBox");
        if (constructionPercentElement != null) {
            String constructionText = constructionPercentElement.textContent().trim();
            if (constructionText.startsWith("CONSTRUCTION %")) {
                data.constructionPercent = constructionText.substring("CONSTRUCTION %".length()).trim();
            } else {
                data.constructionPercent = constructionText;
            }
            log.debug("Extracted construction percent: {}", data.constructionPercent);
        }
    }

    /**
     * Extract property status from expanded details
     */
    private void extractPropertyStatusFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle propertyStatusElement = page.querySelector(additionalDetailsSelector + " > div:first-child > div:nth-child(3) > div.childRowBox");
        if (propertyStatusElement != null) {
            String statusText = propertyStatusElement.textContent().trim();
            if (statusText.startsWith("PROPERTY STATUS")) {
                data.propertyStatus = statusText.substring("PROPERTY STATUS".length()).trim();
            } else {
                data.propertyStatus = statusText;
            }
            log.debug("Extracted property status: {}", data.propertyStatus);
        }
    }

    /**
     * Extract view type from expanded details using multiple approaches
     */
    private void extractViewTypeFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        // Method 1: Try to find it in the childRowBox structure with h6/h5 elements
        try {
            ElementHandle viewTypeBoxElement = page.querySelector(".childRowBox:has(h6:text('View Type'))");
            if (viewTypeBoxElement != null) {
                ElementHandle viewTypeValueElement = viewTypeBoxElement.querySelector("h5");
                if (viewTypeValueElement != null) {
                    data.viewType = viewTypeValueElement.textContent().trim();
                    log.debug("Method 1: Extracted view type from h5 element: '{}'", data.viewType);
                }
            }
        } catch (Exception e) {
            log.debug("Error in Method 1 for view type extraction: {}", e.getMessage());
        }

        // Method 2: Try to find it using a more specific selector for the structure in the example
        if (data.viewType == null || data.viewType.isEmpty()) {
            try {
                ElementHandle viewTypeRow = page.querySelector(".row:has(.childRowBox:has(h6:text('View Type')))");
                if (viewTypeRow != null) {
                    ElementHandle viewTypeH5 = viewTypeRow.querySelector(".childRowBox h5");
                    if (viewTypeH5 != null) {
                        data.viewType = viewTypeH5.textContent().trim();
                        log.debug("Method 2: Extracted view type: '{}'", data.viewType);
                    }
                }
            } catch (Exception e) {
                log.debug("Error in Method 2 for view type extraction: {}", e.getMessage());
            }
        }

        // Method 3: Try to find it using direct evaluation of the page content
        if (data.viewType == null || data.viewType.isEmpty()) {
            try {
                // This JavaScript will find the view type in the specific HTML structure from the example
                String jsScript = ""
                    + "const viewTypeBoxes = Array.from(document.querySelectorAll('.childRowBox'));"
                    + "for (const box of viewTypeBoxes) {"
                    + "  const h6 = box.querySelector('h6');"
                    + "  if (h6 && h6.textContent.trim() === 'View Type') {"
                    + "    const h5 = box.querySelector('h5');"
                    + "    if (h5) return h5.textContent.trim();"
                    + "  }"
                    + "}"
                    + "return null;";

                Object result = page.evaluate(jsScript);
                if (result != null && !result.toString().isEmpty()) {
                    data.viewType = result.toString().trim();
                    log.debug("Method 3: Extracted view type using JavaScript: '{}'", data.viewType);
                }
            } catch (Exception e) {
                log.debug("Error in Method 3 for view type extraction: {}", e.getMessage());
            }
        }

        // Method 4: Try the original method with additionalDetailsSelector
        if (data.viewType == null || data.viewType.isEmpty()) {
            try {
                ElementHandle viewTypeElement = page.querySelector(additionalDetailsSelector + ":has-text('View Type')");
                if (viewTypeElement != null) {
                    String viewTypeText = viewTypeElement.textContent().trim();
                    log.debug("Method 4: Raw view type text: '{}'", viewTypeText);

                    if (viewTypeText.contains("View Type")) {
                        data.viewType = viewTypeText.replace("View Type", "").trim();
                        log.debug("Method 4: Extracted view type after removing prefix: '{}'", data.viewType);

                        if (data.viewType.contains("%")) {
                            // Parse complex view type like "Retail Construction %100% Property Status Ready OSR - Ground..."
                            String[] parts = data.viewType.split("Construction|Property Status|Price/SQFT|ACD|Plot Area|Base Price|VAT|Parking");
                            if (parts.length > 0) {
                                data.viewType = parts[0].trim();
                                log.debug("Method 4: Extracted clean view type: '{}'", data.viewType);
                            }
                        }
                    } else {
                        data.viewType = viewTypeText.trim();
                        log.debug("Method 4: Using raw view type (no prefix detected): '{}'", data.viewType);
                    }
                } else {
                    log.debug("Method 4: View type element not found using selector: {}", additionalDetailsSelector + ":has-text('View Type')");
                }
            } catch (Exception e) {
                log.debug("Error in Method 4 for view type extraction: {}", e.getMessage());
            }
        }

        // Log the final view type for debugging
        if (data.viewType != null && !data.viewType.isEmpty()) {
            log.debug("Final view type value: '{}'", data.viewType);
        }
    }

    /**
     * Extract price per sqft from expanded details
     */
    private void extractPricePerSqftFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle pricePerSqftElement = page.querySelector(additionalDetailsSelector + " > div:nth-child(2) > div:nth-child(2) > div.childRowBox");
        if (pricePerSqftElement != null) {
            String pricePerSqftText = pricePerSqftElement.textContent().trim();
            if (pricePerSqftText.contains("Price/SQFT")) {
                data.pricePerSqft = extractNumericValue(pricePerSqftText.replace("Price/SQFT", "").trim());
                log.debug("Extracted price per sqft: {}", data.pricePerSqft);
            }
        }
    }

    /**
     * Extract base price from expanded details
     */
    private void extractBasePriceFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle basePriceElement = page.querySelector(additionalDetailsSelector + " > div:nth-child(3) > div:nth-child(2) > div.childRowBox");
        if (basePriceElement != null) {
            String basePriceText = basePriceElement.textContent().trim();
            if (basePriceText.contains("Base Price")) {
                data.basePrice = extractNumericValue(basePriceText.replace("Base Price", "").trim());
                log.debug("Extracted base price: {}", data.basePrice);
            }
        }
    }

    /**
     * Extract VAT from expanded details
     */
    private void extractVatFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle vatElement = page.querySelector(additionalDetailsSelector + " > div:nth-child(3) > div:nth-child(3) > div.childRowBox");
        if (vatElement != null) {
            String vatText = vatElement.textContent().trim();
            if (vatText.contains("VAT")) {
                data.vat = extractNumericValue(vatText.replace("VAT", "").trim());
                log.debug("Extracted VAT: {}", data.vat);
            }
        }
    }

    /**
     * Extract parking from expanded details
     */
    private void extractParkingFromDetails(Page page, String additionalDetailsSelector, PropertyData data) {
        ElementHandle parkingElement = page.querySelector(additionalDetailsSelector + " > div:nth-child(4) > div:nth-child(1) > div.childRowBox");
        if (parkingElement != null) {
            String parkingText = parkingElement.textContent().trim();
            if (parkingText.contains("Parking")) {
                data.parking = parkingText.replace("Parking", "").trim();
                log.debug("Extracted parking: {}", data.parking);
                // Parse to integer
                try {
                    data.parkingInt = Integer.parseInt(data.parking);
                } catch (NumberFormatException e) {
                    log.warn("Could not parse parking: {}", data.parking);
                }
            }
        }
    }

    /**
     * Extracts a numeric value from a string, handling various formats
     * @param text The input text that may contain a numeric value
     * @return The extracted numeric value as a string, or the original text if extraction fails
     */
    private String extractNumericValue(String text) {
        if (text == null || text.isEmpty()) {
            log.debug("Text is null or empty, returning empty string");
            return "";
        }

        log.debug("Extracting numeric value from: '{}'", text);

        try {
            // Special case for our specific format: "499800 AED 499,800"
            // Split by whitespace and check if first part is numeric
            String[] parts = text.trim().split("\\s+");
            if (parts.length > 0) {
                String firstPart = parts[0];

                // Check if the first part is entirely numeric
                if (firstPart.matches("\\d+")) {
                    log.debug("First part is numeric: '{}'", firstPart);
                    return firstPart;
                }

                // Check if it starts with a number but has commas (like "2,500,000")
                if (firstPart.matches("\\d[\\d,]*")) {
                    // Remove commas and return
                    String withoutCommas = firstPart.replaceAll(",", "");
                    log.debug("First part has commas, extracted: '{}'", withoutCommas);
                    return withoutCommas;
                }

                // Special case: if first part is a currency code (like "AED")
                if (firstPart.matches("[A-Za-z]+") && parts.length > 1) {
                    // Take the second part and extract numeric value
                    String secondPart = parts[1];
                    log.debug("First part is currency code, checking second part: '{}'", secondPart);

                    // Remove any non-numeric chars (except commas for thousands)
                    String cleanedSecondPart = secondPart.replaceAll("[^0-9,]", "");
                    // Then remove commas
                    String numericSecondPart = cleanedSecondPart.replaceAll(",", "");

                    if (!numericSecondPart.isEmpty()) {
                        log.debug("Extracted numeric value from second part: '{}'", numericSecondPart);
                        return numericSecondPart;
                    }
                }
            }

            // General case: extract all digits from the text
            log.debug("Using fallback digit extraction");
            String allDigits = text.replaceAll("[^0-9]", "");

            if (!allDigits.isEmpty()) {
                log.debug("Extracted all digits: '{}'", allDigits);
                return allDigits;
            }

            // If we get here, we couldn't extract any digits
            log.warn("Could not extract any numeric value from '{}'", text);
            return "";
        } catch (Exception e) {
            log.warn("Error extracting numeric value from '{}': {}", text, e.getMessage());
            // Fallback to just extracting digits from the full text
            String digitsOnly = text.replaceAll("[^0-9]", "");
            log.debug("Exception fallback - extracting digits only: '{}'", digitsOnly);
            return digitsOnly;
        }
    }
    /**
     * Extracts the view type from the property details page using multiple methods
     * @param page The Playwright page
     * @param additionalDetailsSelector Base selector for additional details
     * @return The extracted view type or empty string if not found
     */
    private String extractViewType(Page page, String additionalDetailsSelector) {
        String viewType = "";

        // Method 1: Try to find it in the childRowBox structure with h6/h5 elements
        try {
            ElementHandle viewTypeBoxElement = page.querySelector(".childRowBox:has(h6:text('View Type'))");
            if (viewTypeBoxElement != null) {
                ElementHandle viewTypeValueElement = viewTypeBoxElement.querySelector("h5");
                if (viewTypeValueElement != null) {
                    viewType = viewTypeValueElement.textContent().trim();
                    log.debug("Method 1: Extracted view type from h5 element: '{}'", viewType);
                }
            }
        } catch (Exception e) {
            log.debug("Error in Method 1 for view type extraction: {}", e.getMessage());
        }

        // Method 2: Try to find it using a more specific selector for the structure
        if (viewType == null || viewType.isEmpty()) {
            try {
                ElementHandle viewTypeRow = page.querySelector(".row:has(.childRowBox:has(h6:text('View Type')))");
                if (viewTypeRow != null) {
                    ElementHandle viewTypeH5 = viewTypeRow.querySelector(".childRowBox h5");
                    if (viewTypeH5 != null) {
                        viewType = viewTypeH5.textContent().trim();
                        log.debug("Method 2: Extracted view type: '{}'", viewType);
                    }
                }
            } catch (Exception e) {
                log.debug("Error in Method 2 for view type extraction: {}", e.getMessage());
            }
        }

        // Method 3: Try to find it using direct evaluation of the page content
        if (viewType == null || viewType.isEmpty()) {
            try {
                // This JavaScript will find the view type in the specific HTML structure
                String jsScript = ""
                    + "const viewTypeBoxes = Array.from(document.querySelectorAll('.childRowBox'));"
                    + "for (const box of viewTypeBoxes) {"
                    + "  const h6 = box.querySelector('h6');"
                    + "  if (h6 && h6.textContent.trim() === 'View Type') {"
                    + "    const h5 = box.querySelector('h5');"
                    + "    if (h5) return h5.textContent.trim();"
                    + "  }"
                    + "}"
                    + "return null;";

                viewType = executeJavaScript(page, jsScript);
                if (!viewType.isEmpty()) {
                    log.debug("Method 3: Extracted view type using JavaScript: '{}'", viewType);
                }
            } catch (Exception e) {
                log.debug("Error in Method 3 for view type extraction: {}", e.getMessage());
            }
        }

        // Method 4: Try the original method with additionalDetailsSelector
        if (viewType == null || viewType.isEmpty()) {
            try {
                ElementHandle viewTypeElement = page.querySelector(additionalDetailsSelector + ":has-text('View Type')");
                if (viewTypeElement != null) {
                    String viewTypeText = viewTypeElement.textContent().trim();
                    log.debug("Method 4: Raw view type text: '{}'", viewTypeText);

                    if (viewTypeText.contains("View Type")) {
                        viewType = viewTypeText.replace("View Type", "").trim();
                        log.debug("Method 4: Extracted view type after removing prefix: '{}'", viewType);

                        if (viewType.contains("%")) {
                            // Parse complex view type like "Retail Construction %100% Property Status Ready OSR - Ground..."
                            String[] parts = viewType.split("Construction|Property Status|Price/SQFT|ACD|Plot Area|Base Price|VAT|Parking");
                            if (parts.length > 0) {
                                viewType = parts[0].trim();
                                log.debug("Method 4: Extracted clean view type: '{}'", viewType);
                            }
                        }
                    } else {
                        viewType = viewTypeText.trim();
                        log.debug("Method 4: Using raw view type (no prefix detected): '{}'", viewType);
                    }
                }
            } catch (Exception e) {
                log.debug("Error in Method 4 for view type extraction: {}", e.getMessage());
            }
        }

        return viewType != null ? viewType : "";
    }

    /**
     * Extracts the unit type from the property details page
     * @param page The Playwright page
     * @param additionalDetailsSelector Base selector for additional details
     * @return The extracted unit type or empty string if not found
     */
    private String extractUnitType(Page page, String additionalDetailsSelector) {
        try {
            ElementHandle unitTypeElement = page.querySelector(additionalDetailsSelector + " > div:first-child > div:first-child > div.childRowBox");
            if (unitTypeElement != null) {
                String unitTypeText = extractText(unitTypeElement);
                log.debug("Raw unit type text: '{}'", unitTypeText);

                // Extract the unit type value (after "Unit Type" prefix)
                if (unitTypeText.contains("Unit Type")) {
                    String unitType = unitTypeText.replace("Unit Type", "").trim();
                    log.debug("Extracted unit type: '{}'", unitType);
                    return unitType;
                } else {
                    log.debug("Using raw unit type (no prefix detected): '{}'", unitTypeText);
                    return unitTypeText;
                }
            }
        } catch (Exception e) {
            log.debug("Error extracting unit type: {}", e.getMessage());
        }
        return "";
    }

    /**
     * Extracts the property status from the property details page
     * @param page The Playwright page
     * @param additionalDetailsSelector Base selector for additional details
     * @return The extracted property status or empty string if not found
     */
    private String extractPropertyStatus(Page page, String additionalDetailsSelector) {
        try {
            ElementHandle statusElement = page.querySelector(additionalDetailsSelector + ":has-text('Property Status')");
            if (statusElement != null) {
                String statusText = extractText(statusElement);
                log.debug("Raw property status text: '{}'", statusText);

                // Extract the status value (after "Property Status" prefix)
                if (statusText.contains("Property Status")) {
                    String status = statusText.replace("Property Status", "").trim();
                    log.debug("Extracted property status: '{}'", status);
                    return status;
                } else {
                    log.debug("Using raw property status (no prefix detected): '{}'", statusText);
                    return statusText;
                }
            }
        } catch (Exception e) {
            log.debug("Error extracting property status: {}", e.getMessage());
        }
        return "";
    }

    /**
     * Extracts the price from a text string
     * @param priceText The text containing the price
     * @return A map with currency and value keys
     */
    private Map<String, Object> extractPrice(String priceText) {
        Map<String, Object> priceMap = new HashMap<>();
        priceMap.put("currency", "AED"); // Default currency
        priceMap.put("value", 0.0);      // Default value

        if (priceText == null || priceText.isEmpty()) {
            return priceMap;
        }

        try {
            // Extract numeric part from price text
            String numericValue = extractNumericValue(priceText);
            if (!numericValue.isEmpty()) {
                double value = Double.parseDouble(numericValue);
                priceMap.put("value", value);
            }

            // Try to extract currency
            if (priceText.contains("AED")) {
                priceMap.put("currency", "AED");
            } else if (priceText.contains("USD")) {
                priceMap.put("currency", "USD");
            }
        } catch (Exception e) {
            log.debug("Error parsing price '{}': {}", priceText, e.getMessage());
        }

        return priceMap;
    }

    /**
     * Creates a map of additional data from extracted property details
     * @param unitType The unit type
     * @param propertyStatus The property status
     * @param viewType The view type
     * @param page The Playwright page (for capturing HTML context)
     * @return A map of additional data
     */
    private Map<String, Object> createAdditionalDataMap(String unitType, String propertyStatus, String viewType, Page page) {
        Map<String, Object> additionalData = new HashMap<>();

        // Add unit type if available
        if (unitType != null && !unitType.isEmpty()) {
            additionalData.put("unit_type", unitType);
        }

        // Add property status if available
        if (propertyStatus != null && !propertyStatus.isEmpty()) {
            additionalData.put("property_status", propertyStatus);
        }

        // Add view type if available
        if (viewType != null && !viewType.isEmpty()) {
            additionalData.put("view_type", viewType);

            // Also store detailed information for debugging
            Map<String, String> viewTypeDetails = new HashMap<>();
            viewTypeDetails.put("view_type_value", viewType);

            // Try to capture the full HTML context for debugging
            try {
                // Get the entire view type row HTML for debugging
                ElementHandle viewTypeRow = page.querySelector(".row:has(.childRowBox:has(h6:text('View Type')))");
                if (viewTypeRow != null) {
                    String rowHtml = page.evaluate("el => el.outerHTML", viewTypeRow).toString();
                    viewTypeDetails.put("view_type_row_html", rowHtml);
                }
            } catch (Exception e) {
                log.debug("Error capturing view type HTML context: {}", e.getMessage());
            }

            additionalData.put("view_type_details", viewTypeDetails);
        }

        return additionalData;
    }

    // Utility methods for element extraction

    /**
     * Safely extracts text content from an ElementHandle
     * @param element The element to extract text from
     * @return The trimmed text content or empty string if element is null
     */
    private String extractText(ElementHandle element) {
        if (element == null) {
            return "";
        }
        try {
            return element.textContent().trim();
        } catch (Exception e) {
            log.debug("Error extracting text from element: {}", e.getMessage());
            return "";
        }
    }

    /**
     * Safely finds an element using a CSS selector
     * @param page The Playwright page
     * @param selector The CSS selector
     * @return Optional containing the element if found
     */
    private Optional<ElementHandle> findElement(Page page, String selector) {
        try {
            ElementHandle element = page.querySelector(selector);
            return Optional.ofNullable(element);
        } catch (Exception e) {
            log.debug("Error finding element with selector '{}': {}", selector, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Extracts text from an element found using a CSS selector
     * @param page The Playwright page
     * @param selector The CSS selector
     * @return The text content or empty string if element not found
     */
    private String extractTextBySelector(Page page, String selector) {
        return findElement(page, selector)
                .map(this::extractText)
                .orElse("");
    }

    /**
     * Executes JavaScript on the page and returns the result as a string
     * @param page The Playwright page
     * @param script The JavaScript to execute
     * @return The result as a string or empty string if execution fails
     */
    private String executeJavaScript(Page page, String script) {
        try {
            Object result = page.evaluate(script);
            return result != null ? result.toString().trim() : "";
        } catch (Exception e) {
            log.debug("Error executing JavaScript: {}", e.getMessage());
            return "";
        }
    }
}
