package com.realmond.temporal_service.crawler.uae.aro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

/**
 * Represents detailed project information from the project detail API.
 */
@Data
public class ProjectDetail {
    private Integer id;
    private String slug;
    private String title;
    private String description;
    @JsonProperty("handover_date")
    private ZonedDateTime handoverDate;
    private Developer developer;
    private List<String> images;
    private List<GalleryItem> gallery;
    @JsonProperty("youtube_videos")
    private List<String> youtubeVideos;
    @JsonProperty("unit_description")
    private String unitDescription;
    @JsonProperty("floorplan_description")
    private String floorplanDescription;
    @JsonProperty("is_favorite")
    private Boolean isFavorite;
    private String address;
    @JsonProperty("launch_date")
    private ZonedDateTime launchDate;
    @JsonProperty("construction_start_date")
    private ZonedDateTime constructionStartDate;
    @JsonProperty("has_brochure")
    private Boolean hasBrochure;
    @JsonProperty("is_exclusive")
    private Boolean isExclusive;
    @JsonProperty("in_folder")
    private Boolean inFolder;
    @JsonProperty("pricing_category")
    private String pricingCategory;
    @JsonProperty("price_per_sqft")
    private Price pricePerSqft;
    private Integer rating;
    @JsonProperty("rating_count")
    private Integer ratingCount;
    @JsonProperty("rating_distribution")
    private Map<String, Integer> ratingDistribution;

    @Data
    public static class GalleryItem {
        private String path;
        private List<String> tags;
    }
} 