package com.realmond.temporal_service.translate.svc;

import com.realmond.temporal_service.translate.cache.BlackListEntityCache;
import com.realmond.temporal_service.translate.client.ReactiveStrapiTranslationClient;
import com.realmond.temporal_service.translate.client.StrapiTranslationClient;
import com.realmond.temporal_service.translate.dto.SingleTranslateRequest;
import com.realmond.temporal_service.translate.dto.SingleTranslateResponse;
import com.realmond.temporal_service.translate.dto.UnTranslatedEntities;
import com.realmond.temporal_service.translate.dto.UntranslatedTypes;
import com.realmond.temporal_service.translate.error.PermanentTranslationException;
import com.realmond.temporal_service.translate.error.TransientTranslationException;
import com.realmond.temporal_service.translate.error.TranslationException;
import com.realmond.temporal_service.translate.error.UntranslatedApiException;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class TranslateServiceImpl implements TranslateService {

    private final StrapiTranslationClient strapiTranslationClient;
    private final ReactiveStrapiTranslationClient reactiveStrapiTranslationClient;
    private final BlackListEntityCache blackListEntityCache;

    public TranslateServiceImpl(
            @Autowired StrapiTranslationClient strapiTranslationClient,
            @Autowired ReactiveStrapiTranslationClient reactiveStrapiTranslationClient,
            @Autowired BlackListEntityCache blackListEntityCache
    ) {
        this.strapiTranslationClient = strapiTranslationClient;
        this.reactiveStrapiTranslationClient = reactiveStrapiTranslationClient;
        this.blackListEntityCache = blackListEntityCache;
    }

    @Override
    public SingleTranslateResponse translate(SingleTranslateRequest request) throws TranslationException {
        try {
            SingleTranslateResponse response = strapiTranslationClient.singleTranslate(request);

            if (response.getError() != null) {
                throw new PermanentTranslationException("Strapi API Error: " + response.getError().getMessage());
            }

            return response;
        } catch (FeignException.InternalServerError | FeignException.BadRequest e){
            blackListEntityCache.addToSet(request.getContentType(), request.getId());
            throw new PermanentTranslationException("Bad Request: " + e.getMessage(), e);
        } catch (FeignException.ServiceUnavailable e) {
            throw new TransientTranslationException("Service Unavailable: " + e.getMessage(), e);
        } catch (FeignException e) {
            if (isTransient(e)) {
                throw new TransientTranslationException("Transient Feign Error: " + e.getMessage(), e);
            } else {
                throw new PermanentTranslationException("Permanent Feign Error: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new PermanentTranslationException("Unexpected Error: " + e.getMessage(), e);
        }
    }

    @Override
    public UnTranslatedEntities getUntranslatedEntityIds(String contentType, String targetLocale, int page, int pageSize) {
        try {
            return strapiTranslationClient.getUntranslatedEntityIds(contentType, targetLocale, page, pageSize);
        } catch (Exception e) {
            log.error("Error fetching untranslated entity IDs for content type {}: {}", contentType, e.getMessage());
            throw e; // Re-throw to be handled by the activity
        }
    }

    @Override
    public List<List<String>> getTranslatableContentTypes() {
        try {
            UntranslatedTypes response = strapiTranslationClient.getTranslatableTypes();
            if (response == null) {
                log.info("NULL response from getTranslatableTypes");
                return List.of();
            }

            log.info("Response data from getTranslatableTypes: {}", response.getData());
            return response.getData();

        } catch (Exception e) {
            log.error("Exception while fetching translatable content types", e);
            throw new UntranslatedApiException("Exception while fetching translatable content types: " + e.getMessage());
        }
    }

    /**
     * Determines if a FeignException is transient based on the status code.
     *
     * @param e FeignException
     * @return true if transient, false otherwise
     */
    private boolean isTransient(FeignException e) {
        int status = e.status();
        // Define status codes that are considered transient
        return status == 408 || // Request Timeout
                status == 429 || // Too Many Requests
                (status >= 500 && status < 600); // Server Errors
    }

    @Override
    public Mono<SingleTranslateResponse> translateReactive(SingleTranslateRequest request) {
        return reactiveStrapiTranslationClient.singleTranslateReactive(request)
            .flatMap(response -> {
                if (response.getError() != null) {
                    return Mono.error(new PermanentTranslationException("Strapi API Error: " + response.getError().getMessage()));
                }
                return Mono.just(response);
            })
            .onErrorResume(FeignException.class, e -> {
                if (e instanceof FeignException.InternalServerError || e instanceof FeignException.BadRequest) {
                    blackListEntityCache.addToSet(request.getContentType(), request.getId());
                    return Mono.error(new PermanentTranslationException("Bad Request: " + e.getMessage(), e));
                } else if (e instanceof FeignException.ServiceUnavailable) {
                    return Mono.error(new TransientTranslationException("Service Unavailable: " + e.getMessage(), e));
                } else if (isTransient(e)) {
                    return Mono.error(new TransientTranslationException("Transient Feign Error: " + e.getMessage(), e));
                } else {
                    return Mono.error(new PermanentTranslationException("Permanent Feign Error: " + e.getMessage(), e));
                }
            })
            .onErrorResume(Exception.class, e -> {
                if (!(e instanceof TranslationException)) {
                    return Mono.error(new PermanentTranslationException("Unexpected Error: " + e.getMessage(), e));
                }
                return Mono.error(e);
            });
    }

    @Override
    public Mono<UnTranslatedEntities> getUntranslatedEntityIdsReactive(String contentType, String targetLocale, int page, int pageSize) {
        return reactiveStrapiTranslationClient.getUntranslatedEntityIdsReactive(contentType, targetLocale, page, pageSize)
            .onErrorResume(e -> {
                log.error("Error fetching untranslated entity IDs for content type {}: {}", contentType, e.getMessage());
                return Mono.error(e); // Re-throw to be handled by the activity
            });
    }

}
