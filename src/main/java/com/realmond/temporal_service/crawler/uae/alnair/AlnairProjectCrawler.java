package com.realmond.temporal_service.crawler.uae.alnair;

import com.realmond.etl.model.ProjectModel;
import com.realmond.temporal_service.crawler.CrawlRecord;
import com.realmond.temporal_service.crawler.Crawler;
import com.realmond.temporal_service.crawler.err.NonRetryableCrawlerException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.IntStream;

/**
 * Implementation of Alnair Crawler.
 * Uses existing Alnair parser components to fetch and parse project data.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlnairProjectCrawler implements Crawler<ProjectModel> {

    private final AlnairApiService alnairApiService;

    @Override
    public String getSourceUrn() {
        return AlnairSettings.SOURCE_URN;
    }

    @Override
    public Boolean supportsPagination() {
        return true;
    }

    @Override
    public List<String> fetchAllPageIds() {
        int pagesCount = alnairApiService.fetchProjectPagesCount();
        return IntStream.range(1, pagesCount + 1).boxed().map(Object::toString).toList();
    }

    @Override
    public List<CrawlRecord<ProjectModel>> parsePage(String pageNum) {
        log.info("Parsing Alnair project with ID: {}", pageNum);
        int n;
        try {
            n = Integer.parseInt(pageNum);
        } catch (NumberFormatException e) {
            throw new NonRetryableCrawlerException("failed to parse pag number: " + pageNum, e);
        }

        return alnairApiService.fetchProjectsPage(n);
    }

    @Override
    public List<CrawlRecord<ProjectModel>> fetchAll() {
        throw NonRetryableCrawlerException.PARSE_ALL_PAGES_NOT_SUPPORTED;
    }
}
