package com.realmond.temporal_service.crawler.uae.damacproperties.agents.config;

import com.microsoft.playwright.Playwright;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for Playwright and Browserless
 */
@Configuration
public class BrowserlessConfig {

    @Value("${damacproperties.browserless.skip-browser-download:true}")
    private boolean skipBrowserDownload;

    /**
     * Creates a Playwright instance for browser automation
     * This is the main entry point for Playwright/Browserless operations
     * @return Playwright instance
     */
    @Bean
    @Scope("singleton")
    public Playwright playwright() {
        // Set environment variable to skip browser downloads
        Map<String, String> env = new HashMap<>(System.getenv());
        env.put("PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD", skipBrowserDownload ? "1" : "0");

        // Create Playwright instance with options that prevent browser downloads
        Playwright.CreateOptions options = new Playwright.CreateOptions();
        options.setEnv(env);

        return Playwright.create(options);
    }
}
