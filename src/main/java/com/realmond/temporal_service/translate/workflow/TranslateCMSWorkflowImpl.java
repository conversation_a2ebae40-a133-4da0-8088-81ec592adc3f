package com.realmond.temporal_service.translate.workflow;

import com.realmond.temporal_service.translate.activity.GetTranslatableTypesActivity;
import com.realmond.temporal_service.translate.activity.ProcessTypeTranslationActivity;
import com.realmond.temporal_service.translate.activity.RestoreLocationParentLinksActivity;
import com.realmond.temporal_service.config.TranslationConfigProperties;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.workflow.Workflow;
import io.temporal.spring.boot.WorkflowImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

@Component
@WorkflowImpl(taskQueues = "translation-task-queue")
public class TranslateCMSWorkflowImpl implements TranslateCMSWorkflow {
    private static final Logger LOGGER = LoggerFactory.getLogger(TranslateCMSWorkflowImpl.class);

    private final List<String> targetLocales;
    private final int pageSize;
    private final int syncCycleSleepMinutes;

    public TranslateCMSWorkflowImpl(TranslationConfigProperties config) {
        this.targetLocales = config.getTargetLocales();
        this.pageSize = config.getPageSize();
        this.syncCycleSleepMinutes = config.getSyncCycleSleepMinutes();
    }

    @Override
    public void syncUntranslatedEntities() {
        //processSyncCycle();
        //Workflow.sleep(Duration.ofMinutes(syncCycleSleepMinutes));
        //Workflow.continueAsNew(TranslateCMSWorkflowImpl.class);
    }

    private void processSyncCycle() {
        GetTranslatableTypesActivity typesActivity = Workflow.newActivityStub(
                GetTranslatableTypesActivity.class,
                ActivityOptions.newBuilder()
                        .setStartToCloseTimeout(Duration.ofSeconds(30))
                        .build()
        );
        List<List<String>> orderedContentTypes = typesActivity.getTranslatableContentTypes();

        for (List<String> contentTypes : orderedContentTypes) {
             for (String targetLocale : targetLocales) {
                 for (String contentType : contentTypes) {
                     LOGGER.info("Started to translate {} to language {}", contentType, targetLocale);
                     processContentTypeLocale(contentType, targetLocale);
                     LOGGER.info("Finished to translate {} to language {}", contentType, targetLocale);
                 }
            }
        }

        LOGGER.info("All type+locale combinations processed in this cycle.");
    }

    /**
     * Processes translation for a single content type and target locale.
     */
    private void processContentTypeLocale(String contentType, String targetLocale) {
        int processedCount = 0;
        ProcessTypeTranslationActivity processActivity = Workflow.newActivityStub(
                ProcessTypeTranslationActivity.class,
                ActivityOptions.newBuilder()
                        .setStartToCloseTimeout(Duration.ofMinutes(60))
                        .setRetryOptions(RetryOptions.newBuilder()
                                .setMaximumAttempts(1)
                                .build())
                        .build()
        );
        try {
            processedCount = processActivity.processTypeTranslation(contentType, targetLocale, pageSize);

            // If this is a location content type, restore parent links after translation
            if ("api::location.location".equals(contentType)) {
                RestoreLocationParentLinksActivity restoreActivity = Workflow.newActivityStub(
                        RestoreLocationParentLinksActivity.class,
                        ActivityOptions.newBuilder()
                                .setStartToCloseTimeout(Duration.ofMinutes(10))
                                .setRetryOptions(RetryOptions.newBuilder()
                                        .setMaximumAttempts(3)
                                        .build())
                                .build()
                );

                int restoredCount = restoreActivity.restoreLocationParentLinks();
                LOGGER.info("Restored parent links for {} locations after translating to {}",
                        restoredCount, targetLocale);
            }
        } catch (Exception e) {
            LOGGER.info("ProcessTypeTranslationActivity for type {} and locale {} failed with exception {}",
                    contentType, targetLocale, e);
        }
        LOGGER.info("Processed {} entities for content type {} and target locale {}.",
                processedCount, contentType, targetLocale);
    }
}
