package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.impl;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.exception.CrawlerException;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.service.BrowserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.io.UnsupportedEncodingException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.List;
import java.util.ArrayList;

/**
 * Implementation of BrowserManager using Playwright
 * Supports both remote (Browserless Docker) and local browser instances
 */
@Slf4j
@Service
public class BrowserlessManager implements BrowserManager {

    private final Playwright playwright;
    private Browser browser;
    private final ReentrantLock browserLock = new ReentrantLock();
    private final ConcurrentHashMap<Page, BrowserContext> pageContextMap = new ConcurrentHashMap<>();

    // Common configuration
    @Value("${damacproperties.browserless.mode:remote}")
    private String browserMode;

    @Value("${damacproperties.browserless.timeout:60000}")
    private int browserlessTimeout;

    @Value("${damacproperties.browserless.stealth-mode:true}")
    private boolean stealthMode;

    @Value("${damacproperties.browserless.user-agent:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36}")
    private String userAgent;

    @Value("${damacproperties.browserless.reconnect-tries:3}")
    private int reconnectTries;

    @Value("${damacproperties.browserless.reconnect-delay:5000}")
    private int reconnectDelay;

    @Value("${damacproperties.browserless.page-timeout:30000}")
    private int pageTimeout;

    @Value("${damacproperties.browserless.keepalive-interval:15000}")
    private int keepAliveInterval = 15000;

    // Remote Browserless configuration
    @Value("${damacproperties.browserless.url:ws://localhost:3000}")
    private String browserlessUrl;

    @Value("${damacproperties.browserless.token:}")
    private String browserlessToken;

    // Local browser configuration
    @Value("${damacproperties.browserless.headless:true}")
    private boolean headless;

    private volatile AtomicBoolean keepAliveRunning = new AtomicBoolean(false);
    private Thread keepAliveThread = null;

    @Autowired
    public BrowserlessManager(Playwright playwright) {
        this.playwright = playwright;
        log.info("BrowserlessManager initialized with Playwright instance in {} mode", browserMode);
    }

    /**
     * Gets a browser instance, creating a new one if necessary.
     * This method is thread-safe and supports both remote and local modes.
     */
    @Override
    public Browser getBrowser() {
        browserLock.lock();
        try {
            if (browser == null || !browser.isConnected()) {
                log.info("Creating new browser in {} mode", browserMode);

                // Close any existing browser instance
                closeBrowserInternal();

                // Choose browser creation method based on mode
                if ("local".equalsIgnoreCase(browserMode)) {
                    browser = createLocalBrowser();
                } else {
                    browser = createRemoteBrowser();
                }

                // Start keep-alive thread when browser is connected
                if ("remote".equalsIgnoreCase(browserMode)) {
                    startKeepAliveThread();
                }

                return browser;
            }

            return browser;
        } finally {
            browserLock.unlock();
        }
    }

    /**
     * Creates a local browser instance using Playwright's built-in browser handling
     */
    private Browser createLocalBrowser() {
        int retryCount = 0;
        int maxRetries = reconnectTries;
        Exception lastException = null;

        log.info("Launching local Chromium browser, headless mode: {}", headless);

        while (retryCount < maxRetries) {
            try {
                // Let Playwright handle browser download and management
                BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setSlowMo(50);  // Slight slowdown to appear more human-like

                // Add browser arguments for stability
                List<String> args = new ArrayList<>();
                args.add("--no-sandbox");
                args.add("--disable-setuid-sandbox");
                args.add("--disable-dev-shm-usage");
                args.add("--disable-web-security");
                args.add("--disable-features=IsolateOrigins,site-per-process");
                args.add("--disable-site-isolation-trials");

                // Add the arguments to launch options
                launchOptions.setArgs(args);

                log.info("Launching local browser");
                Browser localBrowser = playwright.chromium().launch(launchOptions);
                log.info("Local browser launched successfully");

                return localBrowser;
            } catch (Exception e) {
                lastException = e;
                log.warn("Failed to launch local browser (attempt {} of {}): {}",
                    retryCount + 1, maxRetries, e.getMessage(), e);
                retryCount++;

                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying
                        int waitTime = reconnectDelay * (int)Math.pow(2, retryCount);
                        log.info("Waiting {} ms before retry", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        throw new CrawlerException("Failed to launch local browser after " + maxRetries + " attempts", lastException);
    }

    /**
     * Creates a remote browser instance connected to Browserless
     */
    private Browser createRemoteBrowser() {
        log.info("Creating new browser connection to Browserless at {}", browserlessUrl);

        // Prepare the WebSocket endpoint - using a completely different approach
        String wsEndpoint = prepareWebSocketEndpoint();

        log.info("Using WebSocket endpoint: {}", wsEndpoint);

        // Use increased timeout for connection
        BrowserType.ConnectOptions connectOptions = new BrowserType.ConnectOptions()
                .setSlowMo(50) // Slight slowdown to appear more human-like
                .setTimeout(browserlessTimeout);

        int retryCount = 0;
        int maxRetries = reconnectTries;
        Exception lastException = null;

        while (retryCount < maxRetries) {
            try {
                log.info("Connecting to Browserless, attempt {} of {}", retryCount + 1, maxRetries);

                // CHANGE: Try to use local browser if remote connection fails on first attempt
                if (retryCount > 0) {
                    log.info("Trying alternative connection method on retry {}", retryCount);

                    // Simplify connection URL - try direct connection without parameters
                    String simplifiedUrl = browserlessUrl;
                    if (simplifiedUrl.contains("?")) {
                        simplifiedUrl = simplifiedUrl.substring(0, simplifiedUrl.indexOf("?"));
                    }

                    log.info("Using simplified URL: {}", simplifiedUrl);
                    return playwright.chromium().connect(simplifiedUrl, connectOptions);
                }

                // First attempt - try with our constructed endpoint
                Browser remoteBrowser = playwright.chromium().connect(wsEndpoint, connectOptions);
                log.info("Connected to Browserless successfully");

                return remoteBrowser;
            } catch (Exception e) {
                lastException = e;
                log.warn("Failed to connect to Browserless (attempt {} of {}): {}",
                        retryCount + 1, maxRetries, e.getMessage(), e);
                retryCount++;

                if (retryCount == maxRetries - 1) {
                    // On last retry, fall back to local browser
                    log.info("On last retry, switching to local browser mode");
                    try {
                        return createLocalBrowser();
                    } catch (Exception localEx) {
                        log.error("Local browser fallback also failed: {}", localEx.getMessage());
                        // Continue with regular retry in case local browser fails
                    }
                }

                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying with exponential backoff
                        int waitTime = reconnectDelay * (int)Math.pow(2, retryCount);
                        log.info("Waiting {} ms before retry", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        throw new CrawlerException("Failed to connect to Browserless after " + maxRetries + " attempts", lastException);
    }

    /**
     * Creates a new WebSocket endpoint URL with appropriate parameters.
     */
    private String prepareWebSocketEndpoint() {
        // Start with the base URL
        String wsEndpoint = browserlessUrl;

        // Make sure we have a valid WebSocket URL
        if (!wsEndpoint.startsWith("ws://") && !wsEndpoint.startsWith("wss://")) {
            log.warn("URL doesn't start with ws:// or wss://, adding ws:// prefix");
            wsEndpoint = "ws://" + wsEndpoint;
        }

        // COMPLETELY CHANGED CONNECTION STRATEGY
        // Instead of trying to use various parameters, just use the base URL without params
        // This has proven to be more reliable with Browserless
        if (wsEndpoint.contains("/playwright/chromium")) {
            log.info("Switching from /playwright/chromium to direct CDP connection");
            wsEndpoint = wsEndpoint.replace("/playwright/chromium", "");
        }

        // Remove any trailing slash
        if (wsEndpoint.endsWith("/")) {
            wsEndpoint = wsEndpoint.substring(0, wsEndpoint.length() - 1);
        }

        // Add the token if provided - only parameter we'll include
        if (browserlessToken != null && !browserlessToken.isEmpty()) {
            wsEndpoint = wsEndpoint + (wsEndpoint.contains("?") ? "&" : "?") + "token=" + browserlessToken;
        }

        // REMOVED: Launch parameters and other options that might cause issues

        log.info("Generated simplified WebSocket endpoint: {}", wsEndpoint);
        return wsEndpoint;
    }

    /**
     * Encodes a value for use in a URL
     */
    private String encodeURIValue(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            log.error("Error encoding URI value: {}", e.getMessage());
            return value;
        }
    }

    /**
     * Creates a new page in a new browser context.
     * Each page has its own isolated browser context.
     */
    @Override
    public Page newPage() {
        int retryCount = 0;
        int maxRetries = reconnectTries;
        Exception lastException = null;

        while (retryCount < maxRetries) {
            try {
                // Get a browser instance (will create if necessary)
                Browser browser = getBrowser();

                // Create a new browser context with stealth settings
                Map<String, String> headers = new HashMap<>();
                headers.put("Accept-Language", "en-US,en;q=0.9");
                headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8");

                log.debug("Creating new browser context (attempt {} of {})", retryCount + 1, maxRetries);
                BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                        .setUserAgent(userAgent)
                        .setViewportSize(1920, 1080)
                        .setExtraHTTPHeaders(headers));

                // Create a new page
                log.debug("Creating new page (attempt {} of {})", retryCount + 1, maxRetries);
                Page page = context.newPage();

                // Configure page for better stability
                page.setDefaultTimeout(pageTimeout);

                // Store the context associated with this page for later cleanup
                pageContextMap.put(page, context);

                log.info("Created new page with stealth settings successfully");
                return page;
            } catch (Exception e) {
                lastException = e;
                log.warn("Failed to create new page (attempt {} of {}): {}", retryCount + 1, maxRetries, e.getMessage());
                retryCount++;

                // If we're having trouble creating pages, reconnect the browser
                if (isTargetClosedError(e)) {
                    browserLock.lock();
                    try {
                        closeBrowserInternal();
                        browser = null;
                    } finally {
                        browserLock.unlock();
                    }
                }

                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying with exponential backoff
                        int waitTime = reconnectDelay * (int)Math.pow(2, retryCount);
                        log.info("Waiting {} ms before retry", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        log.error("Failed to create new page after {} attempts", maxRetries);
        throw new CrawlerException("Failed to create new page after " + maxRetries + " attempts", lastException);
    }

    /**
     * Starts a thread that periodically sends a command to the browser to keep the connection alive
     */
    private void startKeepAliveThread() {
        if (keepAliveRunning.get()) {
            return; // Already running
        }

        keepAliveRunning.set(true);
        keepAliveThread = new Thread(() -> {
            log.info("Starting browser keep-alive thread");
            while (keepAliveRunning.get() && browser != null) {
                try {
                    browserLock.lock();
                    try {
                        // Send a ping to keep the connection alive
                        if (browser != null && browser.isConnected()) {
                            browser.contexts();
                            log.debug("Keep-alive ping sent to browser");
                        } else if (browser != null) {
                            log.warn("Browser disconnected, attempting to reconnect in keep-alive thread");
                            closeBrowserInternal();
                            browser = getBrowser();
                            log.info("Browser reconnected in keep-alive thread");
                        }
                    } finally {
                        browserLock.unlock();
                    }
                    Thread.sleep(keepAliveInterval);
                } catch (Exception e) {
                    log.warn("Error in keep-alive thread: {}", e.getMessage());
                    try {
                        Thread.sleep(5000); // Wait before retry
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            log.info("Browser keep-alive thread stopped");
        });

        keepAliveThread.setName("BrowserKeepAlive");
        keepAliveThread.setDaemon(true);
        keepAliveThread.start();
    }

    /**
     * Stops the keep-alive thread
     */
    private synchronized void stopKeepAliveThread() {
        if (!keepAliveRunning.get()) {
            return;
        }

        keepAliveRunning.set(false);
        if (keepAliveThread != null) {
            keepAliveThread.interrupt();
            keepAliveThread = null;
        }
    }

    /**
     * Clean up resources when this bean is destroyed
     */
    @PreDestroy
    public void cleanup() {
        log.info("Performing cleanup of BrowserlessManager resources");

        // Stop the keep-alive thread
        stopKeepAliveThread();

        // Close the browser
        closeBrowser();
    }

    /**
     * Close the browser and all its contexts
     */
    @Override
    public void closeBrowser() {
        browserLock.lock();
        try {
            closeBrowserInternal();

            if (playwright != null) {
                try {
                    playwright.close();
                    log.info("Playwright closed successfully");
                } catch (Exception e) {
                    log.warn("Error closing Playwright: {}", e.getMessage());
                }
            }
        } finally {
            browserLock.unlock();
        }
    }

    /**
     * Internal method to close the browser without closing Playwright
     */
    private void closeBrowserInternal() {
        if (browser != null) {
            try {
                // Clear the page-context map
                pageContextMap.clear();

                // Close the browser
                if (browser.isConnected()) {
                    browser.close();
                    log.info("Browser closed successfully");
                }
            } catch (Exception e) {
                log.warn("Error closing browser: {}", e.getMessage());
            } finally {
                browser = null;
            }
        }
    }

    /**
     * Close a specific page and its associated context
     */
    public void closePage(Page page) {
        if (page == null) {
            return;
        }

        try {
            // Get the associated browser context
            BrowserContext context = pageContextMap.remove(page);

            // Close the page
            if (!page.isClosed()) {
                page.close();
                log.debug("Page closed successfully");
            }

            // Close the associated context
            if (context != null) {
                context.close();
                log.debug("Browser context closed successfully");
            }
        } catch (Exception e) {
            log.warn("Error closing page: {}", e.getMessage());
        }
    }

    /**
     * Navigate to a URL with retry logic and comprehensive error handling
     */
    @Override
    public void navigateTo(Page page, String url) {
        log.debug("Navigating to: {}", url);
        int retryCount = 0;
        int maxRetries = reconnectTries;
        Exception lastException = null;

        while (retryCount < maxRetries) {
            try {
                // Check if page is still valid
                if (page.isClosed()) {
                    throw new CrawlerException("Page is closed, cannot navigate");
                }

                // Attempt navigation with timeout
                page.navigate(url, new Page.NavigateOptions().setTimeout(pageTimeout));

                // Wait for the page to load
                page.waitForLoadState(LoadState.DOMCONTENTLOADED,
                                     new Page.WaitForLoadStateOptions().setTimeout(pageTimeout));
                log.debug("Navigation complete to {}", url);
                return;
            } catch (Exception e) {
                lastException = e;
                log.warn("Navigation attempt {} failed: {}", retryCount + 1, e.getMessage());

                // Check if this is a target closed error - if so, we need to recover
                if (isTargetClosedError(e)) {
                    log.warn("Target page closed during navigation, attempting recovery");

                    // We need to create a new page and let the caller know
                    Page newPage = recoverFromClosedTarget();
                    throw new BrowserReconnectedException("Browser reconnected during navigation, new page created", newPage, e);
                }

                retryCount++;

                if (retryCount < maxRetries) {
                    try {
                        int waitTime = reconnectDelay * (int)Math.pow(2, retryCount);
                        log.info("Waiting {} ms before retry navigation", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        log.error("Error navigating to {} after {} attempts: {}", url, maxRetries,
                lastException != null ? lastException.getMessage() : "Unknown error");
        throw new CrawlerException("Failed to navigate to " + url, lastException);
    }

    /**
     * Check if an exception is related to a target being closed
     */
    private boolean isTargetClosedError(Exception e) {
        String message = e.getMessage();
        return message != null && (
            message.contains("Target closed") ||
            message.contains("Target page, context or browser has been closed") ||
            message.contains("Browser has been closed") ||
            message.contains("Protocol error") ||
            message.contains("Browser context") && message.contains("closed") ||
            message.contains("cannot continue tracing page") ||
            message.contains("Target page, context or browser has been closed")
        );
    }

    /**
     * Create a new browser and page when a target closes unexpectedly
     */
    private Page recoverFromClosedTarget() {
        log.info("Attempting to recover from closed target");

        // Close the browser to reset the state
        browserLock.lock();
        try {
            closeBrowserInternal();
            browser = null;

            // Create a new browser
            browser = getBrowser();

            // Create a new context and page
            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setUserAgent(userAgent)
                    .setViewportSize(1920, 1080));

            Page newPage = context.newPage();
            newPage.setDefaultTimeout(pageTimeout);

            // Track this page and its context
            pageContextMap.put(newPage, context);

            log.info("Successfully created new page during recovery");
            return newPage;
        } finally {
            browserLock.unlock();
        }
    }

    /**
     * Wait for a selector to appear with improved error handling
     */
    @Override
    public void waitForSelector(Page page, String selector, int timeoutMillis) {
        try {
            log.debug("Waiting for selector: {}", selector);
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot wait for selector");
            }

            page.waitForSelector(selector,
                    new Page.WaitForSelectorOptions()
                            .setState(WaitForSelectorState.VISIBLE)
                            .setTimeout(timeoutMillis));
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while waiting for selector: " + selector, e);
            }
            log.warn("Timeout waiting for selector: {}", selector);
            throw new CrawlerException("Timeout waiting for selector: " + selector, e);
        }
    }

    /**
     * Wait for a page load state with improved error handling
     */
    @Override
    public void waitForLoadState(Page page, LoadState state, int timeoutMillis) {
        try {
            log.debug("Waiting for load state: {}", state);
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot wait for load state");
            }

            page.waitForLoadState(state,
                    new Page.WaitForLoadStateOptions().setTimeout(timeoutMillis));
            log.debug("Page reached load state: {}", state);
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while waiting for load state: " + state, e);
            }
            log.warn("Timeout waiting for load state {}: {}", state, e.getMessage());
            // Don't throw, some sites never reach certain load states
        }
    }

    /**
     * Check if an element exists on the page with improved error handling
     */
    @Override
    public boolean elementExists(Page page, String selector, int timeoutMillis) {
        try {
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot check if element exists");
            }

            // Use a reduced timeout to check for existence
            page.waitForSelector(selector,
                    new Page.WaitForSelectorOptions()
                            .setState(WaitForSelectorState.VISIBLE)
                            .setTimeout(timeoutMillis));
            return true;
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while checking if element exists: " + selector, e);
            }
            return false; // Element doesn't exist or timeout occurred
        }
    }

    /**
     * Type text into an element with a delay between each character
     */
    @Override
    public void typeWithDelay(Page page, String selector, String text) {
        try {
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot type with delay");
            }

            // First click on the element to focus it
            page.click(selector);

            // Clear any existing text
            page.fill(selector, "");

            // Type the text character by character with delay
            for (char c : text.toCharArray()) {
                page.type(selector, String.valueOf(c), new Page.TypeOptions().setDelay(50));
                sleep(50); // 50ms delay between characters
            }

            log.debug("Typed text with delay into element: {}", selector);
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while typing with delay: " + selector, e);
            }
            log.error("Error typing into element {}: {}", selector, e.getMessage());
            throw new CrawlerException("Failed to type into element " + selector, e);
        }
    }

    /**
     * Click on an element with improved error handling and retry logic
     */
    @Override
    public void click(Page page, String selector) {
        log.debug("Clicking on element: {}", selector);

        int retryCount = 0;
        Exception lastException = null;
        final int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                if (page.isClosed()) {
                    throw new CrawlerException("Page is closed, cannot click");
                }

                // Make sure the element is visible and clickable
                page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(pageTimeout / 2));

                // Try to click the element
                page.click(selector, new Page.ClickOptions().setForce(false).setTimeout(pageTimeout / 2));
                return; // Success, exit the method
            } catch (Exception e) {
                lastException = e;
                log.warn("Attempt {} to click element {} failed: {}", retryCount + 1, selector, e.getMessage());

                if (isTargetClosedError(e)) {
                    throw new CrawlerException("Target closed while clicking: " + selector, e);
                }

                retryCount++;

                if (retryCount < maxRetries) {
                    // Wait before retrying
                    sleep(1000);
                }
            }
        }

        // If we've exhausted all retries, throw the last exception
        throw new CrawlerException("Failed to click element " + selector + " after " + maxRetries + " attempts", lastException);
    }

    /**
     * Get text content from an element
     */
    @Override
    public String getText(Page page, String selector) {
        try {
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot get text");
            }
            return page.textContent(selector);
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while getting text: " + selector, e);
            }
            log.error("Error getting text from element {}: {}", selector, e.getMessage());
            throw new CrawlerException("Failed to get text from element " + selector, e);
        }
    }

    /**
     * Get an attribute from an element
     */
    @Override
    public String getAttribute(Page page, String selector, String attribute) {
        try {
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot get attribute");
            }
            return page.getAttribute(selector, attribute);
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while getting attribute: " + selector + " - " + attribute, e);
            }
            log.error("Error getting attribute {} from element {}: {}", attribute, selector, e.getMessage());
            throw new CrawlerException("Failed to get attribute from element " + selector, e);
        }
    }

    /**
     * Sleep for a specified number of milliseconds
     */
    @Override
    public void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Sleep interrupted", e);
        }
    }

    /**
     * Fill an input field with text
     */
    @Override
    public void fill(Page page, String selector, String text) {
        try {
            if (page.isClosed()) {
                throw new CrawlerException("Page is closed, cannot fill");
            }

            log.debug("Filling element {} with text", selector);
            page.fill(selector, text);
        } catch (Exception e) {
            if (isTargetClosedError(e)) {
                throw new CrawlerException("Target closed while filling: " + selector, e);
            }
            log.error("Error filling element {}: {}", selector, e.getMessage());
            throw new CrawlerException("Failed to fill element " + selector, e);
        }
    }

    /**
     * Exception thrown when the browser has been reconnected and the page needs to be reloaded
     */
    public static class BrowserReconnectedException extends CrawlerException {
        private final Page newPage;

        public BrowserReconnectedException(String message, Page newPage, Throwable cause) {
            super(message, cause);
            this.newPage = newPage;
        }

        public Page getNewPage() {
            return newPage;
        }
    }
}
