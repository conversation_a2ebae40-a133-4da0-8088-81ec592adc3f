package com.realmond.temporal_service.client;

import com.realmond.temporal_service.client.feign.PropertyFinderFeignClient;
import com.realmond.temporal_service.crawler.fingerprint.BrowserFingerprint;
import com.realmond.temporal_service.crawler.fingerprint.FingerprintGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Implementation of PropertyFinderClient using Feign client with fingerprinting.
 * This implementation uses direct HTTP requests instead of browser automation,
 * which can be more efficient and less likely to be detected as a bot.
 */
@Slf4j
@Component
@Primary  // Mark as primary to override the existing implementation
public class PropertyFinderFeignClientImpl implements PropertyFinderClient {

    private final PropertyFinderFeignClient feignClient;
    private final FingerprintGenerator fingerprintGenerator;
    private final Random random = new Random();

    @Value("${propertyfinder.max-retries:3}")
    private int maxRetries;

    @Value("${propertyfinder.retry-delay-ms:2000}")
    private int retryDelayMs;

    @Autowired
    public PropertyFinderFeignClientImpl(
            PropertyFinderFeignClient feignClient,
            FingerprintGenerator fingerprintGenerator) {
        this.feignClient = feignClient;
        this.fingerprintGenerator = fingerprintGenerator;
        log.info("Initialized PropertyFinderFeignClientImpl with fingerprinting");
    }

    @Override
    public String fetchProjectsList(int pageNumber) {
        String logMessage = "Fetching projects list page " + pageNumber;
        log.info(logMessage);
        return fetchWithRetry(() -> feignClient.getProjectsList(pageNumber, createHeaders()), logMessage);
    }

    @Override
    public String fetchProjectDetail(String shareUrl) {
        String logMessage = "Fetching project detail for " + shareUrl;
        log.info(logMessage);
        return fetchWithRetry(() -> feignClient.getProjectDetail(shareUrl, createHeaders()), logMessage);
    }

    /**
     * Creates HTTP headers with a random browser fingerprint to avoid detection.
     * @return Map of HTTP headers
     */
    private Map<String, String> createHeaders() {
        // Generate a random fingerprint for this request
        BrowserFingerprint fingerprint = fingerprintGenerator.generateRandomFingerprint();
        log.debug("Using fingerprint: {}", fingerprint);

        Map<String, String> headers = new HashMap<>();

        // Essential headers
        headers.put("User-Agent", fingerprint.getUserAgent());
        headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        headers.put("Accept-Language", fingerprint.getLanguage());
        headers.put("Accept-Encoding", "gzip, deflate, br");

        // Additional headers to make the request look more like a browser
        headers.put("Connection", "keep-alive");
        headers.put("Upgrade-Insecure-Requests", "1");
        headers.put("Sec-Fetch-Dest", "document");
        headers.put("Sec-Fetch-Mode", "navigate");
        headers.put("Sec-Fetch-Site", "none");
        headers.put("Sec-Fetch-User", "?1");
        headers.put("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"");
        headers.put("Sec-Ch-Ua-Mobile", "?0");
        headers.put("Sec-Ch-Ua-Platform", "\"" + fingerprint.getPlatform() + "\"");

        // Add a random cache-control header to avoid caching issues
        if (random.nextBoolean()) {
            headers.put("Cache-Control", "max-age=0");
        } else {
            headers.put("Pragma", "no-cache");
            headers.put("Cache-Control", "no-cache");
        }

        // Add a random referer sometimes to make it look more natural
        if (random.nextBoolean()) {
            headers.put("Referer", "https://www.google.com/");
        }

        return headers;
    }

    /**
     * Executes a request with retry logic.
     * @param supplier The function to execute
     * @param logMessage The log message for debugging
     * @return The response from the supplier
     */
    private String fetchWithRetry(RequestSupplier supplier, String logMessage) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // Add a small random delay to make requests look more natural
                int randomDelay = random.nextInt(1000);
                Thread.sleep(randomDelay);

                String response = supplier.get();

                // Check if the response contains the expected content
                if (response != null && !response.isEmpty() && response.contains("__NEXT_DATA__")) {
                    log.debug("Successfully fetched content on attempt {}", attempt);
                    return response;
                } else {
                    log.warn("Response doesn't contain expected content on attempt {}", attempt);
                    lastException = new RuntimeException("Response doesn't contain expected content");
                }
            } catch (Exception e) {
                log.warn("{} failed on attempt {}: {}", logMessage, attempt, e.getMessage());
                lastException = e;

                // Wait before retrying
                try {
                    Thread.sleep(retryDelayMs * attempt); // Exponential backoff
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread interrupted during retry delay", ie);
                }
            }
        }

        // If we get here, all attempts failed
        throw new RuntimeException("Failed after " + maxRetries + " attempts: " + logMessage, lastException);
    }

    /**
     * Functional interface for request suppliers.
     */
    @FunctionalInterface
    private interface RequestSupplier {
        String get() throws Exception;
    }
}
