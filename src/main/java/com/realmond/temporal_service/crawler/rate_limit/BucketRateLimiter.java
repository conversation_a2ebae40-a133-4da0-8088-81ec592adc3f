package com.realmond.temporal_service.crawler.rate_limit;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.redis.lettuce.cas.LettuceBasedProxyManager;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.function.Supplier;

/**
 * Simple wrapper around Bucket4j that hides all Bucket4j/Redis internals and
 * provides a very small API for the crawler to use.
 */
@Slf4j
public class BucketRateLimiter {

    private final boolean useRedis;
    private final Bandwidth limit;
    private final ProxyManager<String> proxyManager; // null when local

    private final java.util.concurrent.ConcurrentHashMap<String, Bucket> localBuckets = new java.util.concurrent.ConcurrentHashMap<>();

    /**
     * Creates a rate limiter that relies on an externally managed {@link ProxyManager} instance.
     * This variant should be preferred when you want to share a single Redis connection (and
     * therefore proxy manager) across many limiter instances.  Simply pass {@code null} for
     * {@code proxyManager} to fall back to an in-memory bucket.
     */
    public BucketRateLimiter(RateLimiterSettings settings, ProxyManager<String> proxyManager) {
        this.limit = Bandwidth.builder()
              .capacity(settings.getCapacity())
              .refillGreedy(settings.getRefillTokens(), Duration.ofSeconds(settings.getRefillPeriodSeconds()))
              .build();

        this.proxyManager = proxyManager;
        this.useRedis = proxyManager != null;

        if (useRedis) {
            log.info("Initialized Redis-backed rate limiter (external manager) with limit: {} tokens / {} s", settings.getRefillTokens(), settings.getRefillPeriodSeconds());
        } else {
            log.info("Initialized in-memory rate limiter with limit: {} tokens / {} s", settings.getRefillTokens(), settings.getRefillPeriodSeconds());
        }
    }

    public BucketRateLimiter(RateLimiterSettings settings) {
        this.limit = Bandwidth.builder()
              .capacity(settings.getCapacity())
              .refillGreedy(
                    settings.getRefillTokens(),
                    Duration.ofSeconds(settings.getRefillPeriodSeconds())
              )
              .build();

        this.useRedis = settings.isUseRedis();

        if (useRedis) {
            this.proxyManager = initRedisProxyManager(settings);
            log.info(
                  "Initialized Redis-backed rate limiter with limit: {} tokens / {} s",
                  settings.getRefillTokens(),
                  settings.getRefillPeriodSeconds()
            );
        } else {
            this.proxyManager = null;
            log.info(
                  "Initialized in-memory rate limiter with limit: {} tokens / {} s",
                  settings.getRefillTokens(),
                  settings.getRefillPeriodSeconds()
            );
        }
    }

    private ProxyManager<String> initRedisProxyManager(RateLimiterSettings settings) {
        RedisURI uri = RedisURI.builder()
              .withHost(settings.getRedisHost())
              .withPort(settings.getRedisPort())
              .withDatabase(settings.getRedisDatabase())
              .build();
        if (settings.getRedisPassword() != null && !settings.getRedisPassword().isBlank()) {
            uri.setPassword(settings.getRedisPassword());
        }
        RedisClient client = RedisClient.create(uri);
        StatefulRedisConnection<String, byte[]> connection = client.connect(RedisCodec.of(
              StringCodec.UTF8,
              ByteArrayCodec.INSTANCE
        ));

        return LettuceBasedProxyManager.builderFor(connection).build();
    }

    private Bucket resolve(String key) {
        if (useRedis) {
            Supplier<io.github.bucket4j.BucketConfiguration> configSupplier = () -> io.github.bucket4j.BucketConfiguration.builder()
                  .addLimit(limit)
                  .build();
            return proxyManager.builder().build(key, configSupplier);
        }

        // In-memory bucket map
        return localBuckets.computeIfAbsent(key, k -> Bucket.builder().addLimit(limit).build());
    }

    /**
     * Try to consume {@code tokens}. Returns false if not available
     */
    public boolean tryConsume(String key, long tokens) {
        return resolve(key).tryConsume(tokens);
    }

    public boolean tryConsume(String key) {
        return tryConsume(key, 1);
    }

    /**
     * Blocking consume - waits until token available.
     */
    public void consume(String key, long tokens) {
        try {
            resolve(key).asBlocking().consume(tokens);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public void consume(String key) {
        consume(key, 1);
    }

    public long getAvailableTokens(String key) {
        return resolve(key).getAvailableTokens();
    }
}
