package com.realmond.temporal_service.translate.svc;

import com.realmond.temporal_service.translate.dto.SingleTranslateRequest;
import com.realmond.temporal_service.translate.dto.SingleTranslateResponse;
import com.realmond.temporal_service.translate.dto.UnTranslatedEntities;
import com.realmond.temporal_service.translate.error.TranslationException;
import reactor.core.publisher.Mono;

import java.util.List;

public interface TranslateService {
    SingleTranslateResponse translate(SingleTranslateRequest singleTranslateRequest) throws TranslationException;

    /**
     * Fetches untranslated entity IDs for a specific content type and target locale
     *
     * @param contentType the content type to fetch untranslated entities for
     * @param targetLocale the target locale
     * @param page the page number (1-based)
     * @param pageSize the number of items per page
     * @return UnTranslatedEntities containing the list of entity IDs
     */
    UnTranslatedEntities getUntranslatedEntityIds(String contentType, String targetLocale, int page, int pageSize);

    /**
     * Fetches all translatable content types from the API
     *
     * @return List of content type groups
     */
    List<List<String>> getTranslatableContentTypes();

    /**
     * Translates a single entity reactively
     *
     * @param singleTranslateRequest the translation request
     * @return Mono emitting the translation response
     */
    Mono<SingleTranslateResponse> translateReactive(SingleTranslateRequest singleTranslateRequest);

    /**
     * Fetches untranslated entity IDs reactively
     *
     * @param contentType the content type to fetch untranslated entities for
     * @param targetLocale the target locale
     * @param page the page number (1-based)
     * @param pageSize the number of items per page
     * @return Mono emitting the untranslated entities
     */
    Mono<UnTranslatedEntities> getUntranslatedEntityIdsReactive(String contentType, String targetLocale, int page, int pageSize);

}
