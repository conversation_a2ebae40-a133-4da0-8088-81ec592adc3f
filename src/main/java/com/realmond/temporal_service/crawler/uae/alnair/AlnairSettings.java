package com.realmond.temporal_service.crawler.uae.alnair;

import com.realmond.temporal_service.crawler.DefaultCrawlerSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Component
@ConfigurationProperties(prefix = "crawler.uae.alnair")
public class AlnairSettings extends DefaultCrawlerSettings {
    public static final String SOURCE_URN = "alnair.ae";
}
