package com.realmond.temporal_service.crawler;

import com.realmond.temporal_service.crawler.rate_limit.RateLimiterSettings;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DefaultCrawlerSettings {
    private int maxPages = 100;
    private int pageSize = 100;

    /** HTTP retry policy */
    private RequestRetrySettings retry = new RequestRetrySettings();

    /** Rate limiter policy */
    private RateLimiterSettings rateLimiter = new RateLimiterSettings();
}
