package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Page;
import com.realmond.etl.model.AdModel;

import java.util.List;

/**
 * Interface for parsing property information from web pages
 * Following the Single Responsibility Principle by isolating parsing logic
 */
public interface PropertyParserService {

    /**
     * Parse properties from the current page
     * @param page The page instance
     * @param pageWaitTimeSeconds Page wait time in seconds
     * @return List of parsed Property objects
     */
    List<AdModel> parsePropertiesFromCurrentPage(Page page, int pageWaitTimeSeconds);

    /**
     * Parse a single property from the table
     * @param page The page instance
     * @param index The index of the property in the table
     * @return The parsed Property object
     */
    AdModel parseProperty(Page page, int index);

    /**
     * Save a list of properties to the database
     * @param properties The list of properties to save
     * @return true if successful, false otherwise
     */
    boolean saveProperties(List<AdModel> properties);
}
