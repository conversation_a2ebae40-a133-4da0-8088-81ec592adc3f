package com.realmond.temporal_service.etl.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class DynamicRepositoryFactory {

    @PersistenceContext
    private EntityManager entityManager;

    private final JdbcTemplate jdbcTemplate;
    private final Map<String, DynamicRepository> repositoryCache = new HashMap<>();

    /**
     * Gets or creates a repository for the specified table
     * @param tableName the name of the table to use
     * @return a repository instance for the specified table
     */
    @Transactional
    public DynamicRepository getRepositoryForTable(String tableName) {
        String sanitizedTableName = sanitizeTableName(tableName);

        // Return cached repository if available
        if (repositoryCache.containsKey(sanitizedTableName)) {
            return repositoryCache.get(sanitizedTableName);
        }

        // Create table if it doesn't exist
        createTableIfNotExists(sanitizedTableName);

        // Create repository instance
        DynamicRepository repository = new TableSpecificDynamicRepository(
            entityManager,
            sanitizedTableName
        );

        // Cache the repository
        repositoryCache.put(sanitizedTableName, repository);
        return repository;
    }

    /**
     * Creates the table if it doesn't exist
     */
    private void createTableIfNotExists(String tableName) {
        try {
            String createTableSql = String.format(
                "CREATE TABLE IF NOT EXISTS %s (" +
                "urn VARCHAR(256) PRIMARY KEY, " +
                "source_urn VARCHAR(256) NOT NULL, " +
                "data JSONB NOT NULL, " +
                "metadata JSONB, " +
                "sync_execution_id VARCHAR(256) NOT NULL, " +
                "synced_at TIMESTAMPTZ NOT NULL" +
                ")",
                tableName);

            jdbcTemplate.execute(createTableSql);
            log.info("Ensured dynamic table exists: {}", tableName);
        } catch (Exception e) {
            log.error("Failed to create table {}: {}", tableName, e.getMessage(), e);
            throw new RuntimeException("Failed to create repository table", e);
        }
    }

    /**
     * Sanitizes table name to prevent SQL injection
     */
    private String sanitizeTableName(String tableName) {
        return tableName.replaceAll("[^a-zA-Z0-9_]", "_").toLowerCase();
    }
}
