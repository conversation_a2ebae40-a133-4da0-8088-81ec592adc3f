package com.realmond.temporal_service.crawler.uae.property_finder.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import java.util.List;

/**
 * Represents a phase within a payment plan.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@SuperBuilder
@NoArgsConstructor
public class PaymentPhaseModel {

    @JsonProperty("title")
    private String title; // Note: The JSON provides "label" for phase, mapping to "title" for consistency.

    @JsonProperty("miles")
    private List<PaymentMileModel> miles;

    @JsonProperty("label") // Keeping label for direct mapping from JSON
    private String label;

    @JsonProperty("value") // Keeping value for direct mapping from JSON
    private Double value;

}
