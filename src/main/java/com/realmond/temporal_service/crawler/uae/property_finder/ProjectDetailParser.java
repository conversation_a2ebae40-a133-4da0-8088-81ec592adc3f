package com.realmond.temporal_service.crawler.uae.property_finder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.realmond.etl.model.*;
import com.realmond.etl.model.common.BrochureModel;
import com.realmond.etl.model.common.LocationModel;
import com.realmond.etl.model.UnitStats;
import com.realmond.etl.model.AdModel;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.model.DeveloperRecord;
import com.realmond.temporal_service.crawler.uae.damacproperties.agents.repository.DeveloperRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import com.realmond.temporal_service.crawler.uae.property_finder.dto.PaymentPlanModel;

@Slf4j
@Component
@RequiredArgsConstructor
public class ProjectDetailParser {

    private final ObjectMapper objectMapper;
    private final DeveloperRepository developerRepository;

    private static final String DEFAULT_SOURCE_URN = "propertyfinder:ae"; // Default or configurable

    /**
     * Parses the HTML content of a single project page to extract project details
     * and map them to a ProjectModel.
     *
     * @param htmlContent The HTML content as a string.
     * @param projectUrl The URL of the project page, used for deriving developer/project slugs and URNs.
     * @return An Optional containing the populated ProjectModel, or Optional.empty() if parsing fails.
     */
    public Optional<ProjectModel> parseProjectDetails(String htmlContent, String projectUrl) {
        if (htmlContent == null || htmlContent.isEmpty()) {
            log.warn("HTML content is null or empty. Cannot parse project details.");
            return Optional.empty();
        }
        if (projectUrl == null || projectUrl.isEmpty()) {
            log.warn("Project URL is null or empty. Cannot generate URNs.");
            return Optional.empty();
        }

        try {
            Document doc = Jsoup.parse(htmlContent);
            Element scriptElement = doc.selectFirst("script#__NEXT_DATA__");

            if (scriptElement == null) {
                log.warn("Could not find script tag with id '__NEXT_DATA__' in the HTML.");
                return Optional.empty();
            }

            String jsonData = scriptElement.data();
            if (jsonData.isEmpty()) {
                log.warn("Script tag '__NEXT_DATA__' is empty.");
                return Optional.empty();
            }

            JsonNode rootNode = objectMapper.readTree(jsonData);
            JsonNode detailResultNode = rootNode.path("props").path("pageProps").path("detailResult");

            if (detailResultNode.isMissingNode()) {
                log.warn("Could not find 'detailResult' node in the JSON data at props.pageProps.detailResult.");
                return Optional.empty();
            }

            return Optional.of(mapJsonToProjectModel(detailResultNode, projectUrl));

        } catch (JsonProcessingException e) {
            log.error("Error processing JSON data from script tag: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("An unexpected error occurred during parsing project details: {}", e.getMessage(), e);
        }

        return Optional.empty();
    }

    private ProjectModel mapJsonToProjectModel(JsonNode detailResult, String projectUrl) {
        ProjectModel.ProjectModelBuilder builder = (ProjectModel.ProjectModelBuilder) ProjectModel.builder();

        String developerSlug = extractSlugFromUrl(projectUrl, -2);
        String projectSlug = extractSlugFromUrl(projectUrl, -1);
        developerSlug = developerSlug.toLowerCase();
        projectSlug = projectSlug.toLowerCase();
        String developerUrn = DEFAULT_SOURCE_URN + ":developer:" + developerSlug;
        String projectUrn = developerUrn + ":project:" + projectSlug;

        builder.withProjectUrn(projectUrn);
        builder.withDeveloperUrn(developerUrn);
        builder.withProjectSlug(projectSlug);
        builder.withSourceUrn(DEFAULT_SOURCE_URN);
        builder.withExternalId(detailResult.path("id").asText(null));
        builder.withTitle(detailResult.path("title").asText(null));
        builder.withDescription(parseHtmlDescription(detailResult.path("description").asText(null)));
        builder.withLocation(mapLocation(detailResult.path("location"), detailResult.path("locationTree")));
        builder.withCoordinates(mapCoordinates(detailResult.path("location").path("coordinates")));
        builder.withPolygon(null);
        builder.withImages(mapImages(detailResult.path("images")));
        builder.withCoverImage(getFirstImage(detailResult.path("images")));
        builder.withVideos(mapVideos(detailResult.path("images")));
        builder.withProjectStatus(mapConstructionStatus(detailResult.path("constructionPhase").asText(null)));
        builder.withProjectStats(mapProjectStats(detailResult));
        builder.withCurrency(detailResult.path("currency").asText("AED"));
        builder.withAmenities(mapAmenities(detailResult.path("amenities")));
        builder.withPaymentPlans(mapPaymentPlans(detailResult.path("paymentPlans")));
        builder.withEoi(null);
        builder.withBrochures(mapBrochures(detailResult.path("brochureUrl")));
        builder.withVirtualTour(parseUri(detailResult.path("virtualTour").asText(null)).orElse(null));
        builder.withFloorPlans(null);
        List<URI> urls = new ArrayList<>();
        if (projectUrl != null) {
            parseUri(projectUrl).ifPresent(urls::add);
        }
        builder.withUrls(urls);
        // Extract additional data and developer information
        builder.withAdditionalData(extractAdditionalData(detailResult));

        return builder.build();
    }

    private String parseHtmlDescription(String htmlDescription) {
        if (htmlDescription == null || htmlDescription.isEmpty()) {
            return null;
        }
        return Jsoup.parse(htmlDescription).text();
    }

    private LocationModel mapLocation(JsonNode locationNode, JsonNode locationTree) {
        if (locationNode == null || locationNode.isMissingNode()) {
            log.warn("mapLocation called with null or missing locationNode");
            return null;
        }
        LocationModel.LocationModelBuilderBase locBuilder = LocationModel.builder();
        locBuilder.withAddress(locationNode.path("fullName").asText(null));

        String city = null;
        String state = null;
        String cityDistrict = null;

        if (locationTree != null && !locationTree.isMissingNode() && locationTree.isArray() && locationTree instanceof ArrayNode) {
             ArrayNode locationTreeArray = (ArrayNode) locationTree;
             if (locationTreeArray.size() > 0) {
                 JsonNode cityNode = locationTreeArray.get(0);
                 if (cityNode != null && !cityNode.isNull()) {
                    city = cityNode.path("name").asText(null);
                    state = city;
                 }
             }
             if (locationTreeArray.size() > 1) {
                 JsonNode communityNode = locationTreeArray.get(1);
                 if (communityNode != null && !communityNode.isNull()) {
                    cityDistrict = communityNode.path("name").asText(null);
                 }
             }
        } else {
            log.warn("locationTree passed to mapLocation is null, missing, not an array, or wrong type: {}", locationTree != null ? locationTree.toString() : "null");
        }

        locBuilder.withCity(city);
        locBuilder.withState(state);
        locBuilder.withCityDistrict(cityDistrict);
        locBuilder.withCountry("AE");

        return locBuilder.build();
    }

    private CoordinatesModel mapCoordinates(JsonNode coordNode) {
        if (coordNode.isMissingNode()) {
            return null;
        }
        try {
            double lat = coordNode.path("lat").asDouble();
            double lon = coordNode.path("lon").asDouble(coordNode.path("lng").asDouble());
            CoordinatesModel.CoordinatesModelBuilderBase builder = CoordinatesModel.builder();
            builder.withLat(lat);
            builder.withLng(lon);
            return builder.build();
        } catch (Exception e) {
            log.warn("Could not parse coordinates: {}", coordNode.toString(), e);
            return null;
        }
    }

    private List<ImageModel> mapImages(JsonNode imagesNode) {
        if (!imagesNode.isArray()) {
            return Collections.emptyList();
        }
        List<ImageModel> images = new ArrayList<>();
        for (JsonNode imgNode : imagesNode) {
            if ("image".equals(imgNode.path("type").asText())) {
                ImageModel.ImageModelBuilderBase imgBuilder = ImageModel.builder();
                imgBuilder.withUrl(parseUri(imgNode.path("source").asText(null)).orElse(null));
                images.add(imgBuilder.build());
            }
        }
        return images;
    }

    private ImageModel getFirstImage(JsonNode imagesNode) {
        if (!imagesNode.isArray()) {
            return null;
        }
        for (JsonNode imgNode : imagesNode) {
            if ("image".equals(imgNode.path("type").asText())) {
                ImageModel.ImageModelBuilderBase builder = ImageModel.builder();
                builder.withUrl(parseUri(imgNode.path("source").asText(null)).orElse(null));
                return builder.build();
            }
        }
        return null;
    }

    private List<Video> mapVideos(JsonNode imagesNode) {
        if (!imagesNode.isArray()) {
            return Collections.emptyList();
        }
        List<Video> videos = new ArrayList<>();
        for (JsonNode videoNode : imagesNode) {
            if ("video".equals(videoNode.path("type").asText())) {
                Video.VideoBuilderBase videoBuilder = Video.builder();
                videoBuilder.withUrl(parseUri(videoNode.path("source").asText(null)).orElse(null));
                videos.add(videoBuilder.build());
            }
        }
        return videos;
    }

    private AdModel.ConstructionStatusModel mapConstructionStatus(String statusText) {
        if (statusText == null) {
            return null;
        }
        return switch (statusText.toLowerCase()) {
            case "under_construction" -> AdModel.ConstructionStatusModel.ACTIVE;
            case "completed", "ready" -> AdModel.ConstructionStatusModel.FINISHED;
            case "off_plan" -> null;
            default -> null;
        };
    }

    private ProjectStats mapProjectStats(JsonNode detailResult) {
        ProjectStats.ProjectStatsBuilderBase statsBuilder = ProjectStats.builder();

        double startingPriceVal = detailResult.path("startingPrice").asDouble(0.0);
        PriceModel priceMinModel = PriceModel.builder()
                                        .withValue(startingPriceVal)
                                        .withCurrency(detailResult.path("currency").asText("AED"))
                                        .build();
        statsBuilder.withPriceMin(priceMinModel);

        statsBuilder.withPropertyTypes(mapPropertyTypes(detailResult.path("propertyTypes")));

        String completionDateStr = parseFlexibleDateToString(detailResult.path("deliveryDate").asText(null));
        String launchDateStr = parseFlexibleDateToString(detailResult.path("salesStartDate").asText(null));
        statsBuilder.withCompletionDate(completionDateStr);
        statsBuilder.withLaunchDate(launchDateStr);

        JsonNode unitsNode = detailResult.path("units");
        if (unitsNode.isArray() && !unitsNode.isEmpty()) {
             List<UnitStats> unitStatsList = new ArrayList<>();
             try {
                for(JsonNode buildingInfo : unitsNode) {
                    for(JsonNode unitTypeInfo : buildingInfo.path("units")) {
                        String propertyTypeStr = unitTypeInfo.path("propertyType").asText(null);
                        Optional<AdModel.PropertyTypeModel> propertyTypeEnumOpt = mapSinglePropertyType(unitTypeInfo.path("propertyType"));

                        for(JsonNode unitDetailsList : unitTypeInfo.path("list")) {
                            UnitStats.UnitStatsBuilderBase unitStatBuilder = UnitStats.builder();

                            propertyTypeEnumOpt.ifPresent(unitStatBuilder::withPropertyType);

                            unitStatBuilder.withBedrooms(unitDetailsList.path("bedrooms").asDouble());
                            unitStatBuilder.withAreaRangeSqm(unitDetailsList.path("areaFrom").asDouble(0.0));

                            double unitPriceVal = unitDetailsList.path("startingPrice").asDouble(0.0);
                            PriceModel unitPriceMin = PriceModel.builder()
                                                        .withValue(unitPriceVal)
                                                        .withCurrency(detailResult.path("currency").asText("AED"))
                                                        .build();
                            unitStatBuilder.withPriceMin(unitPriceMin);

                            unitStatsList.add(unitStatBuilder.build());
                        }
                    }
                }
                statsBuilder.withUnits(unitStatsList);
             } catch (Exception e) {
                 log.warn("Failed to parse complex unit structure: {}", unitsNode.toString(), e);
                 statsBuilder.withUnits(Collections.emptyList());
             }
        } else {
            statsBuilder.withUnits(Collections.emptyList());
        }
        return statsBuilder.build();
    }

    private List<AdModel.PropertyTypeModel> mapPropertyTypes(JsonNode arrayNode) {
        if (!arrayNode.isArray()) {
            return Collections.emptyList();
        }
        return StreamSupport.stream(arrayNode.spliterator(), false)
                .map(this::mapSinglePropertyType)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<AdModel.PropertyTypeModel> mapSinglePropertyType(JsonNode node) {
        String typeString = node.asText(null);
        if (typeString == null || typeString.isEmpty()) {
            return Optional.empty();
        }
        try {
            String enumName = typeString.toUpperCase().replace('-', '_');
            return Optional.of(AdModel.PropertyTypeModel.valueOf(enumName));
        } catch (IllegalArgumentException e) {
            log.warn("Could not map property type string '{}' to PropertyTypeModel enum.", typeString);
            return Optional.empty();
        }
    }

    private List<AmenityModel> mapAmenities(JsonNode amenitiesNode) {
        if (!amenitiesNode.isArray()) {
            return Collections.emptyList();
        }
        return StreamSupport.stream(amenitiesNode.spliterator(), false)
                .map(node -> {
                    AmenityModel.AmenityModelBuilderBase amenityBuilder = AmenityModel.builder();
                    amenityBuilder.withLabel(node.path("name").asText(null));
                    return amenityBuilder.build();
                 })
                .collect(Collectors.toList());
    }

    private List<Object> mapPaymentPlans(JsonNode paymentPlansNode) {
        if (paymentPlansNode == null || paymentPlansNode.isMissingNode() || !paymentPlansNode.isArray()) {
            log.debug("Payment plans node is missing or not an array.");
            return Collections.emptyList();
        }
        List<Object> structuredPlans = new ArrayList<>();
        try {
            // Parse into specific DTOs first for type safety and structure validation
            List<PaymentPlanModel> plans = objectMapper.convertValue(paymentPlansNode,
                objectMapper.getTypeFactory().constructCollectionType(List.class, PaymentPlanModel.class));

            if (plans != null) {
                // Convert back to List<Object> for ProjectModel compatibility
                structuredPlans.addAll(plans);
                log.debug("Successfully parsed payment plans using DTOs. Resulting list size: {}", structuredPlans.size());
            } else {
                 log.warn("Payment plans node parsed to null using DTOs.");
                 return Collections.emptyList();
            }
            return structuredPlans;
        } catch (IllegalArgumentException e) {
            log.warn("Could not convert paymentPlansNode to List<PaymentPlanModel>: {}", paymentPlansNode.toString(), e);
             // Fallback to raw List<Object> if DTO mapping fails? Or return empty? Stick with empty for now.
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Unexpected error mapping payment plans: {}", paymentPlansNode.toString(), e);
            return Collections.emptyList();
        }
    }

    private List<BrochureModel> mapBrochures(JsonNode brochureUrlNode) {
        if (brochureUrlNode.isMissingNode() || !brochureUrlNode.isTextual()) {
            return Collections.emptyList();
        }
        String url = brochureUrlNode.asText();
        return parseUri(url)
                .map(uri -> {
                    BrochureModel.BrochureModelBuilderBase brochureBuilder = BrochureModel.builder();
                    brochureBuilder.withUrl(uri);
                    return Collections.singletonList(brochureBuilder.build());
                })
                .orElse(Collections.emptyList());
    }

    private Object extractAdditionalData(JsonNode detailResult) {
        // Extract additional potentially useful data into a map
        Map<String, Object> additionalData = new HashMap<>();

        JsonNode developerNode = detailResult.path("developer");
        if (!developerNode.isMissingNode()) {
            String developerName = developerNode.path("name").asText(null);
            String developerLogoUrl = developerNode.path("logoUrl").asText(null);
            boolean developerPageEnabled = developerNode.path("devPageEnabled").asBoolean(false);
            String developerId = developerNode.path("id").asText(null);

            // Add to additionalData for ProjectModel
            additionalData.put("developerName", developerName);
            additionalData.put("developerLogoUrl", developerLogoUrl);
            additionalData.put("developerPageEnabled", developerPageEnabled);
            additionalData.put("developerId", developerId);

            // Extract and save developer information
            extractAndSaveDeveloper(developerName, developerLogoUrl, developerId);
        }

        JsonNode masterPlanNode = detailResult.path("masterPlan");
        if (!masterPlanNode.isMissingNode()) {
            additionalData.put("masterPlanDescription", parseHtmlDescription(masterPlanNode.path("description").asText(null)));
            parseUri(masterPlanNode.path("image").asText(null)).ifPresent(uri -> additionalData.put("masterPlanImage", uri.toString()));
        }

        JsonNode faqsNode = detailResult.path("faqs");
         if (faqsNode.isArray() && !faqsNode.isEmpty()) {
             try {
                List<Map<String, String>> faqsList = objectMapper.convertValue(faqsNode,
                    objectMapper.getTypeFactory().constructCollectionType(List.class,
                        objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, String.class)));
                 // Clean HTML from answers
                 faqsList.forEach(faq -> faq.computeIfPresent("answer", (k, v) -> parseHtmlDescription(v)));
                additionalData.put("faqs", faqsList);
            } catch (IllegalArgumentException e) {
                 log.warn("Could not convert faqsNode to List<Map<String, String>>: {}", faqsNode.toString(), e);
             }
         }

        additionalData.put("ownershipType", detailResult.path("ownershipType").asText(null)); // May be null
        additionalData.put("salesPhase", detailResult.path("salesPhase").asText(null));
        additionalData.put("hotnessLevel", detailResult.path("hotnessLevel").asInt(-1)); // Use -1 if not found/not number
        additionalData.put("lastInspectionDate", parseFlexibleDateToString(detailResult.path("lastInspectionDate").asText(null)));

        JsonNode timelinePhasesNode = detailResult.path("timelinePhases");
        if (timelinePhasesNode.isArray() && !timelinePhasesNode.isEmpty()) {
             try {
                 List<Map<String, Object>> timelinePhasesList = objectMapper.convertValue(timelinePhasesNode,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));
                additionalData.put("timelinePhases", timelinePhasesList);
             } catch (IllegalArgumentException e) {
                 log.warn("Could not convert timelinePhasesNode to List<Map<String, Object>>: {}", timelinePhasesNode.toString(), e);
             }
        }


        // Return the map, or null if it's empty? Return map for consistency.
        return additionalData.isEmpty() ? null : additionalData;
    }

    // --- Utility Methods ---

    private Optional<URI> parseUri(String uriString) {
        if (uriString == null || uriString.isEmpty()) {
            return Optional.empty();
        }
        try {
            return Optional.of(new URI(uriString));
        } catch (URISyntaxException e) {
            log.warn("Invalid URI syntax: {}", uriString, e);
            return Optional.empty();
        }
    }

    private String extractSlugFromUrl(String url, int positionFromEnd) {
        try {
            URI uri = new URI(url);
            String path = uri.getPath();
            if (path == null || path.isEmpty()) {
                return "unknown";
            }
            if (path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            String[] parts = path.split("/");
            int index = parts.length + positionFromEnd;
            if (index >= 0 && index < parts.length && !parts[index].isEmpty()) {
                return parts[index];
            }
        } catch (URISyntaxException e) {
            log.warn("Could not parse URL to extract slug: {}", url, e);
        }
        return "unknown";
    }

    private String getTextFromPath(JsonNode node, String path) {
         if (node == null || node.isMissingNode() || node.isNull()) {
             return null;
         }
         JsonNode valueNode = node.path(path);
         if (valueNode.isMissingNode() || valueNode.isNull()) {
            return null;
         }
        return valueNode.asText(null);
    }

    private List<String> getJsonArrayAsStrings(JsonNode arrayNode) {
        if (!arrayNode.isArray()) {
            return Collections.emptyList();
        }
        return StreamSupport.stream(arrayNode.spliterator(), false)
                .map(JsonNode::asText)
                .collect(Collectors.toList());
    }

    /**
     * Extracts developer information and saves it to the database.
     * Only unique developers are saved, based on the developer URN.
     *
     * @param developerName The name of the developer
     * @param developerLogoUrl The URL to the developer's logo
     * @param developerId The external ID of the developer
     */
    private void extractAndSaveDeveloper(String developerName, String developerLogoUrl, String developerId) {
        if (developerName == null || developerName.isEmpty()) {
            log.warn("Developer name is null or empty, skipping developer extraction");
            return;
        }

        try {
            // Generate developer URN and slug
            String developerSlug = slugify(developerName);
            String developerUrn = DEFAULT_SOURCE_URN + ":developer:" + developerSlug;

            // Check if developer already exists
            if (developerRepository.existsById(developerUrn)) {
                log.debug("Developer {} already exists, skipping", developerName);
                return;
            }

            // Create DeveloperModel
            DeveloperModel.DeveloperModelBuilderBase developerBuilder = DeveloperModel.builder()
                    .withDeveloperUrn(developerUrn)
                    .withSourceUrn(DEFAULT_SOURCE_URN)
                    .withTitle(developerName)
                    .withExternalId(developerId);

            // Add logo URL if available
            if (developerLogoUrl != null && !developerLogoUrl.isEmpty()) {
                parseUri(developerLogoUrl).ifPresent(uri -> {
                    developerBuilder.withLogoUrl(uri.toString());
                });
            }

            // Add URLs if available

            String developerUrl = DEFAULT_SOURCE_URN.replace(":", ".") + "/developers/" + developerSlug;
            parseUri(developerUrl).ifPresent(uri -> developerBuilder.withWebsite(uri.toString()));

            // Build the developer model
            DeveloperModel developerModel = developerBuilder.build();

            // Create and save developer record
            DeveloperRecord record = new DeveloperRecord();
            record.setUrn(developerUrn);
            record.setData(developerModel);
            record.setEmittedAt(ZonedDateTime.now());

            developerRepository.save(record);
            log.info("Successfully saved developer: {}", developerName);
        } catch (Exception e) {
            log.error("Error extracting and saving developer {}: {}", developerName, e.getMessage(), e);
        }
    }

    /**
     * Converts a string to a slug format (lowercase, hyphens instead of spaces, alphanumeric only).
     *
     * @param input The string to convert to a slug
     * @return The slug version of the input string
     */
    private String slugify(String input) {
        if (input == null || input.isEmpty()) {
            return "unknown";
        }

        // Convert to lowercase
        String slug = input.toLowerCase();

        // Replace spaces and special characters with hyphens
        slug = slug.replaceAll("[^a-z0-9]+", "-");

        // Remove leading and trailing hyphens
        slug = slug.replaceAll("^-|-$", "");

        return slug;
    }

    private String parseFlexibleDateToString(String dateString) {
         if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        try {
            return OffsetDateTime.parse(dateString, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                                 .atZoneSameInstant(ZoneOffset.UTC)
                                 .toLocalDate()
                                 .format(DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (DateTimeParseException e1) {
            try {
                return LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE)
                                .format(DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e2) {
                 try {
                    if (dateString.matches("Q[1-4]-\\d{4}")) {
                        String[] parts = dateString.split("-");
                        int year = Integer.parseInt(parts[1]);
                        int month = switch (parts[0]) {
                            case "Q1" -> 3;
                            case "Q2" -> 6;
                            case "Q3" -> 9;
                            case "Q4" -> 12;
                            default -> throw new DateTimeParseException("Invalid Quarter", dateString, 0);
                        };
                        return LocalDate.of(year, month, 1).withDayOfMonth(LocalDate.of(year, month, 1).lengthOfMonth())
                                        .format(DateTimeFormatter.ISO_LOCAL_DATE);
                    }
                } catch (Exception e3) {
                     log.warn("Could not parse date string to string: {}", dateString, e3);
                }
                 log.warn("Could not parse date string with known formats to string: {}", dateString);
                return dateString;
            }
        }
    }
}
