package com.realmond.temporal_service.crawler;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Generic HTTP-retry settings shared by all crawlers.
 */
@Data
@NoArgsConstructor
public class RequestRetrySettings {
    /** Maximum retry attempts. */
    private int maxRetries = 3;
    /** Base delay in milliseconds between retries (actual delay may be multiplied per attempt). */
    private long retryDelayMs = 2000;
    /** Maximum delay in milliseconds between retries (cap for exponential back-off). */
    private long maxPeriodMs = 10_000;
    /** Random jitter added before each request (0..cap ms) to look human. */
    private int randomDelayCapMs = 1_000;
}
