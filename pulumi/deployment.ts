import * as k8s from '@pulumi/kubernetes';
import * as pulumi from '@pulumi/pulumi';

import { WorkerConfig } from './config';
import { WorkerArgs } from './worker';

type CreateDeploymentArgs = {
  namespace: pulumi.Output<string>;
  secret: k8s.core.v1.Secret;
  appYamlConfigMap: k8s.core.v1.ConfigMap;
  config: WorkerConfig;
} & WorkerArgs;

export function createDeployment(
  name: string,
  args: CreateDeploymentArgs,
  opts: pulumi.ComponentResourceOptions,
): k8s.apps.v1.Deployment {
  const envSecretRefs = [
    // @ts-ignore
    args.secret.metadata.name.apply((name) => ({
      secretRef: { name },
    })),
  ];

  const podLabels = {
    component: name,
    release: args.appName,
  };
  let appConfigVolume = "app-config-volume";
  return new k8s.apps.v1.Deployment(
    name,
    {
      kind: 'Deployment',
      apiVersion: 'apps/v1',
      metadata: {
        name: `${args.appName}-${name}`,
        namespace: args.namespace,
        labels: {
          ...args.tags,
          component: name,
          release: args.appName,
        },
      },
      spec: {
        selector: {
          matchLabels: {
            component: name,
            release: args.appName,
          },
        },
        replicas: args.config.replicas ?? 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxUnavailable: 0,
            maxSurge: 1,
          },
        },
        template: {
          metadata: {
            labels: podLabels,
          },
          spec: {
            containers: [
              {
                name: name,
                // resources: {
                //   limits: {
                //     cpu: '500m',
                //     memory: '1250Mi',
                //   },
                //   requests: {
                //     cpu: '125m',
                //     memory: '750Mi',
                //   },
                // },
                image: `${args.config.image}:${args.config.imageVersion}`,
                imagePullPolicy: 'Always',
                // ports: [
                //   {
                //     name: 'web',
                //     containerPort: 3000,
                //     protocol: 'TCP',
                //   },
                // ],
                env: [
                  {
                    name: "SPRING_PROFILES_ACTIVE",
                    value: "k8s,redis-black-list-cache"
                  }
                ],
                envFrom: envSecretRefs,
                volumeMounts: [{
                  name: appConfigVolume,
                  mountPath: "/app/application-k8s.yaml",
                  subPath: "application.yaml"
                }],
                readinessProbe: {
                  httpGet: {
                    path: '/api/health',
                    port: 3000,
                  },
                  initialDelaySeconds: 10,
                  timeoutSeconds: 10,
                  periodSeconds: 5,
                  successThreshold: 1,
                  failureThreshold: 20,
                },
                startupProbe: {
                  httpGet: {
                    path: '/api/health',
                    port: 3000,
                  },
                  initialDelaySeconds: 10,
                  timeoutSeconds: 10,
                  periodSeconds: 5,
                  successThreshold: 1,
                  failureThreshold: 20,
                },
              },
            ],
            volumes: [
              {
                name: appConfigVolume,
                configMap: {
                  name: args.appYamlConfigMap.metadata.name,
                  items: [{
                    key: "application.yaml",
                    path: "application.yaml"
                  }]
                }
              }
            ],
            topologySpreadConstraints: [
              {
                maxSkew: 1,
                topologyKey: 'topology.kubernetes.io/zone',
                whenUnsatisfiable: 'DoNotSchedule',
                labelSelector: {
                  matchLabels: podLabels,
                },
              },
            ],
          },
        },
      },
    },
    {
      ...opts,
      ignoreChanges: ['spec.replicas'],
    },
  );
}

