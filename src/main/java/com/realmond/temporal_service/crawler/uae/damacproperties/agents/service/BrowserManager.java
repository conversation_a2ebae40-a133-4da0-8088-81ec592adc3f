package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.LoadState;

/**
 * Interface for browser management operations.
 * Provides methods to interact with a browser and pages in a stateless way.
 */
public interface BrowserManager {

    /**
     * Get a browser instance, creating a new one if needed
     * @return A connected browser instance
     */
    Browser getBrowser();

    /**
     * Create a new page in a new browser context
     * @return A new page
     */
    Page newPage();

    /**
     * Close the browser and release all resources
     */
    void closeBrowser();

    /**
     * Close a specific page and its context
     * @param page The page to close
     */
    void closePage(Page page);

    /**
     * Navigate to a URL
     * @param page The page to navigate
     * @param url The URL to navigate to
     */
    void navigateTo(Page page, String url);

    /**
     * Wait for a selector to be visible
     * @param page The page to wait on
     * @param selector The CSS selector to wait for
     * @param timeoutMillis The timeout in milliseconds
     */
    void waitForSelector(Page page, String selector, int timeoutMillis);

    /**
     * Wait for a page load state
     * @param page The page to wait on
     * @param state The load state to wait for
     * @param timeoutMillis The timeout in milliseconds
     */
    void waitForLoadState(Page page, LoadState state, int timeoutMillis);

    /**
     * Check if an element exists on the page
     * @param page The page to check
     * @param selector The CSS selector to check for
     * @param timeoutMillis The timeout in milliseconds
     * @return true if the element exists, false otherwise
     */
    boolean elementExists(Page page, String selector, int timeoutMillis);

    /**
     * Type text into an element with a delay between characters
     * @param page The page to interact with
     * @param selector The CSS selector for the input element
     * @param text The text to type
     */
    void typeWithDelay(Page page, String selector, String text);

    /**
     * Click on an element
     * @param page The page to interact with
     * @param selector The CSS selector for the element to click
     */
    void click(Page page, String selector);

    /**
     * Get text content from an element
     * @param page The page to interact with
     * @param selector The CSS selector for the element
     * @return The text content of the element
     */
    String getText(Page page, String selector);

    /**
     * Get an attribute from an element
     * @param page The page to interact with
     * @param selector The CSS selector for the element
     * @param attribute The attribute name to get
     * @return The attribute value
     */
    String getAttribute(Page page, String selector, String attribute);

    /**
     * Fill an input field with text
     * @param page The page to interact with
     * @param selector The CSS selector for the input element
     * @param text The text to fill
     */
    void fill(Page page, String selector, String text);

    /**
     * Sleep for a specified number of milliseconds
     * @param milliseconds The time to sleep in milliseconds
     */
    void sleep(int milliseconds);
}
