package com.realmond.temporal_service.crawler.uae.damacproperties.agents.service;

/**
 * Interface for generating URNs
 * Following the Single Responsibility Principle by isolating URN generation logic
 */
public interface UrnGeneratorService {

    /**
     * Generate an Ad URN
     * @param adId The ad ID
     * @param projectName The project name
     * @return The Ad URN
     */
    String generateAdUrn(String adId, String projectName);

    /**
     * Generate a more unique Ad URN using additional property attributes
     * @param adId The ad ID (unit identifier)
     * @param projectName The project name
     * @param propertyType The property type (optional)
     * @param bedrooms The number of bedrooms (optional)
     * @param unitType The unit type (optional)
     * @return The unique Ad URN
     */
    String generateUniqueAdUrn(String adId, String projectName, String propertyType, String bedrooms, String unitType);

    /**
     * Generate a Source URN
     * @return The Source URN
     */
    String generateSourceUrn();
}
