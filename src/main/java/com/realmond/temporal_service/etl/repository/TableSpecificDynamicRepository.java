package com.realmond.temporal_service.etl.repository;

import com.realmond.temporal_service.etl.model.DynamicRecord;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public class TableSpecificDynamicRepository extends SimpleJpaRepository<DynamicRecord, String> implements DynamicRepository {

    private final EntityManager entityManager;
    private final String tableName;

    public TableSpecificDynamicRepository(EntityManager entityManager, String tableName) {
        super(DynamicRecord.class, entityManager);
        this.entityManager = entityManager;
        this.tableName = tableName;
    }

    @Override
    @Transactional
    public <S extends DynamicRecord> S save(S entity) {
        String sql = String.format(
            "INSERT INTO %s (urn, source_urn, data, metadata, sync_execution_id, synced_at) " +
            "VALUES (:urn, :sourceUrn, :data, :metadata, :syncExecutionId, :syncedAt) " +
            "ON CONFLICT (urn) DO UPDATE SET " +
            "source_urn = :sourceUrn, data = :data, metadata = :metadata, " +
            "sync_execution_id = :syncExecutionId, synced_at = :syncedAt",
            tableName
        );

        Query query = entityManager.createNativeQuery(sql)
            .setParameter("urn", entity.getUrn())
            .setParameter("sourceUrn", entity.getSourceUrn())
            .setParameter("data", entity.getData())
            .setParameter("metadata", entity.getMetadata())
            .setParameter("syncExecutionId", entity.getSyncExecutionId())
            .setParameter("syncedAt", entity.getSyncedAt());

        query.executeUpdate();
        return entity;
    }

    @Override
    public Optional<DynamicRecord> findById(String id) {
        String sql = String.format(
            "SELECT urn, source_urn, data, metadata, sync_execution_id, synced_at " +
            "FROM %s WHERE urn = :urn",
            tableName
        );

        try {
            Query query = entityManager.createNativeQuery(sql, DynamicRecord.class)
                .setParameter("urn", id);
            DynamicRecord result = (DynamicRecord) query.getSingleResult();
            return Optional.of(result);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public List<DynamicRecord> findBySourceUrn(String sourceUrn) {
        String sql = String.format(
            "SELECT urn, source_urn, data, metadata, sync_execution_id, synced_at " +
            "FROM %s WHERE source_urn = :sourceUrn",
            tableName
        );

        Query query = entityManager.createNativeQuery(sql, DynamicRecord.class)
            .setParameter("sourceUrn", sourceUrn);

        return query.getResultList();
    }

    @Override
    public List<DynamicRecord> findBySyncExecutionId(String syncExecutionId) {
        String sql = String.format(
            "SELECT urn, source_urn, data, metadata, sync_execution_id, synced_at " +
            "FROM %s WHERE sync_execution_id = :syncExecutionId",
            tableName
        );

        Query query = entityManager.createNativeQuery(sql, DynamicRecord.class)
            .setParameter("syncExecutionId", syncExecutionId);

        return query.getResultList();
    }

    @Override
    public long countBySourceUrn(String sourceUrn) {
        String sql = String.format(
            "SELECT COUNT(*) FROM %s WHERE source_urn = :sourceUrn",
            tableName
        );

        Query query = entityManager.createNativeQuery(sql)
            .setParameter("sourceUrn", sourceUrn);

        Number count = (Number) query.getSingleResult();
        return count.longValue();
    }
}
