import * as pulumi from "@pulumi/pulumi";
import * as k8s from "@pulumi/kubernetes";
import {Tags} from "./utils";

type CreateServiceArgs = {
    namespace: pulumi.Output<string>;
    appName: string;
    port: pulumi.Output<k8s.types.output.core.v1.ContainerPort>;
    tags: Tags;
}

export function createService(name: string, args: CreateServiceArgs, opts: pulumi.ComponentResourceOptions): k8s.core.v1.Service {
    return new k8s.core.v1.Service(name, {
        kind: "Service",
        apiVersion: "v1",
        metadata: {
            name: `${args.appName}-${name}`,
            namespace: args.namespace,
            labels: {
                ...args.tags,
                component: name,
                release: args.appName
            },
        },
        spec: {
            type: "ClusterIP",
            selector: {
                component: name,
                release: args.appName
            },
            ports: [
                args.port.apply(({name, containerPort}) => ({
                    name,
                    port: containerPort
                }))
            ]
        }
    }, opts);
}
