package com.realmond.temporal_service.crawler.rate_limit;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration properties for Bucket4j-based rate limiter.
 * <p>
 * All values have sane defaults so the limiter works out-of-the-box for a
 * single-node crawler.  Enabling {@code useRedis = true} switches the limiter
 * to the distributed mode backed by Redis.  Connection parameters are taken
 * from the fields below so that the limiter does not have any additional
 * external configuration dependencies.
 */
@Data
@NoArgsConstructor
public class RateLimiterSettings {

    /** Maximum number of tokens that can be stored in the bucket (burst size). */
    private long capacity = 60;

    /** Number of tokens that are added to the bucket every refill period. */
    private long refillTokens = 60;

    /** Refill period in seconds. */
    private long refillPeriodSeconds = 60;

    /** If {@code true} the limiter state is stored in Redis – useful when the crawler runs on several nodes. */
    private boolean useRedis = false;

    // ───────────────────────────────── Redis connection ─────────────────────────

    private String redisHost = "localhost";
    private int redisPort = 6379;
    private String redisPassword;
    private int redisDatabase = 0;

    /** Key that will be used in Redis to store the bucket state. */
    private String bucketKey = "crawler-rate-limit";
} 