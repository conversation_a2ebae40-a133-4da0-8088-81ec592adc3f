package com.realmond.temporal_service.crawler.uae.damacproperties.agents.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

/**
 * Configuration for S3 client to connect to SupaBase storage
 */
@Slf4j
@Configuration
@EnableConfigurationProperties
public class S3Config {

    @Bean
    @ConfigurationProperties(prefix = "storage.s3")
    public S3Properties s3Properties() {
        return new S3Properties();
    }

    @Bean
    public AwsCredentialsProvider awsCredentialsProvider(S3Properties s3Properties) {
        if (s3Properties.getAccessKey() != null && s3Properties.getSecretKey() != null) {
            log.debug("Creating StaticCredentialsProvider for S3 client");
            return StaticCredentialsProvider.create(
                    AwsBasicCredentials.create(s3Properties.getAccessKey(), s3Properties.getSecretKey()));
        } else {
            log.warn("S3 access key or secret key is missing. S3 client might not authenticate properly.");
            // Return a provider that yields no credentials or rely on default chain if keys are missing
            // For simplicity, returning null or throwing an error might be options depending on requirements
            // Let's return a default provider that might pick up env vars or instance profiles if available
            return software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider.create();
        }
    }

    @Bean
    public S3Client s3Client(S3Properties s3Properties, AwsCredentialsProvider credentialsProvider) {
        log.info("Initializing S3 client with endpoint: {}", s3Properties.getEndpoint());

        S3Configuration s3Configuration = S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .build();

        S3ClientBuilder builder = S3Client.builder()
                .region(Region.of(s3Properties.getRegion()))
                .credentialsProvider(credentialsProvider)
                .serviceConfiguration(s3Configuration);

        // Set custom endpoint if provided (required for SupaBase)
        if (s3Properties.getEndpoint() != null && !s3Properties.getEndpoint().isEmpty()) {
            builder.endpointOverride(URI.create(s3Properties.getEndpoint()));
        }

        return builder.build();
    }

    @Setter
    @Getter
    @ToString(exclude = {"accessKey", "secretKey"})
    public static class S3Properties implements InitializingBean {
        private String endpoint;
        private String region;
        private String bucket;
        private String accessKey;
        private String secretKey;
        private String basePath;
        private boolean enabled;

        @Override
        public void afterPropertiesSet() {
            log.info("S3 Storage configuration initialized: {}", this);
        }
    }
}
