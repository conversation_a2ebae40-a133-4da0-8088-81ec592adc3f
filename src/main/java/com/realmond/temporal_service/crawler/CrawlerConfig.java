package com.realmond.temporal_service.crawler;

import com.realmond.temporal_service.config.RedisConfigProperties;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.redis.lettuce.cas.LettuceBasedProxyManager;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CrawlerConfig {

    /**
     * Provides a singleton {@link ProxyManager} that all {@link com.realmond.temporal_service.crawler.rate_limit.BucketRateLimiter}
     * instances can share.  If the <code>redis-black-list-cache</code> Spring profile is not active
     * the RedisConfigProperties bean will not exist – in that case we simply return <code>null</code>
     * and every limiter falls back to in-memory buckets.
     */
    @Bean
    public ProxyManager<String> bucket4jProxyManager(org.springframework.beans.factory.ObjectProvider<RedisConfigProperties> redisPropsProvider) {
        RedisConfigProperties props = redisPropsProvider.getIfAvailable();
        if (props == null) {
            return null; // use local in-memory buckets
        }

        RedisURI uri = RedisURI.builder()
              .withHost(props.getHost())
              .withPort(props.getPort())
              .withDatabase(props.getDatabase())
              .build();
        if (props.getPassword() != null && !props.getPassword().isBlank()) {
            uri.setPassword(props.getPassword());
        }

        RedisClient client = RedisClient.create(uri);
        StatefulRedisConnection<String, byte[]> connection = client.connect(RedisCodec.of(StringCodec.UTF8, ByteArrayCodec.INSTANCE));
        return LettuceBasedProxyManager.builderFor(connection).build();
    }

}
