{"city": {"id": 1, "name": "Dubai", "country_id": 2, "latitude": "25.15433020", "longitude": "55.26127900", "currency": "AED", "is_private": false, "is_permit_number": 1}, "cities": [{"id": 1, "name": "Dubai", "country_id": 2, "latitude": "25.15433020", "longitude": "55.26127900", "currency": "AED", "is_private": false, "is_permit_number": 1}, {"id": 2, "name": "Abu Dhabi", "country_id": 2, "latitude": "24.39871220", "longitude": "54.47347640", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 3, "name": "Sharjah", "country_id": 2, "latitude": "25.34447620", "longitude": "55.40548330", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 5, "name": "<PERSON><PERSON>", "country_id": 2, "latitude": "25.77862550", "longitude": "55.95320280", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 6, "name": "Phuke<PERSON>", "country_id": 3, "latitude": "7.87897800", "longitude": "98.39839200", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 7, "name": "<PERSON><PERSON><PERSON>", "country_id": 2, "latitude": "25.40327750", "longitude": "55.47766910", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 8, "name": "<PERSON><PERSON>", "country_id": 3, "latitude": "9.49987710", "longitude": "99.93911410", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 9, "name": "Muscat", "country_id": 4, "latitude": "23.61018270", "longitude": "58.59334350", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 10, "name": "<PERSON><PERSON><PERSON>", "country_id": 4, "latitude": "17.01570000", "longitude": "54.09240000", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 11, "name": "<PERSON><PERSON><PERSON>", "country_id": 4, "latitude": "19.57340000", "longitude": "57.63320000", "currency": "OMR", "is_private": false, "is_permit_number": 0}, {"id": 12, "name": "Bangkok", "country_id": 3, "latitude": "13.75630000", "longitude": "100.50180000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 13, "name": "London (Intermark)", "country_id": 5, "latitude": "51.49582475", "longitude": "-0.09205173", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 14, "name": "<PERSON><PERSON>", "country_id": 2, "latitude": "25.54917200", "longitude": "55.54931700", "currency": "AED", "is_private": false, "is_permit_number": 0}, {"id": 15, "name": "<PERSON><PERSON><PERSON>", "country_id": 3, "latitude": "12.92360000", "longitude": "100.88280000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 16, "name": "Bali", "country_id": 6, "latitude": "-8.34050000", "longitude": "115.09200000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 17, "name": "<PERSON>", "country_id": 3, "latitude": "18.70610000", "longitude": "98.98170000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 18, "name": "<PERSON><PERSON>", "country_id": 3, "latitude": "12.56830000", "longitude": "99.96280000", "currency": "THB", "is_private": false, "is_permit_number": 0}, {"id": 19, "name": "Qatar", "country_id": 7, "latitude": "25.27698700", "longitude": "51.52639800", "currency": "AED", "is_private": true, "is_permit_number": 0}, {"id": 21, "name": "Limassol", "country_id": 8, "latitude": "34.70710000", "longitude": "33.02260000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 22, "name": "<PERSON><PERSON><PERSON>", "country_id": 8, "latitude": "34.77560000", "longitude": "32.42450000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 23, "name": "Larnaca", "country_id": 8, "latitude": "34.91993750", "longitude": "33.61554530", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 24, "name": "Nicosia", "country_id": 8, "latitude": "35.18560000", "longitude": "33.38230000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 25, "name": "Famagusta", "country_id": 8, "latitude": "35.11750000", "longitude": "33.94000000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 26, "name": "Manchester", "country_id": 5, "latitude": "53.47876500", "longitude": "-2.24945700", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 27, "name": "Birmingham", "country_id": 5, "latitude": "52.47930400", "longitude": "-1.89882750", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 28, "name": "Liverpool", "country_id": 5, "latitude": "53.40631400", "longitude": "-2.98152200", "currency": "GBP", "is_private": true, "is_permit_number": 0}, {"id": 29, "name": "Athens", "country_id": 9, "latitude": "37.98380000", "longitude": "23.72750000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 30, "name": "Chalkidiki", "country_id": 9, "latitude": "40.24440000", "longitude": "23.49970000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 31, "name": "Thessaloniki", "country_id": 9, "latitude": "40.64010000", "longitude": "22.94440000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 32, "name": "Barcelona", "country_id": 10, "latitude": "41.38510000", "longitude": "2.17340000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 33, "name": "Madrid", "country_id": 10, "latitude": "40.41680000", "longitude": "-3.70380000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 34, "name": "Malaga", "country_id": 10, "latitude": "36.72130000", "longitude": "-4.42140000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 35, "name": "Benidorm", "country_id": 10, "latitude": "38.54110000", "longitude": "-0.12250000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 36, "name": "Costa Del Sol", "country_id": 10, "latitude": "36.52980000", "longitude": "-4.88300000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 37, "name": "Costa Blanca", "country_id": 10, "latitude": "38.34520000", "longitude": "-0.48150000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 38, "name": "<PERSON>", "country_id": 10, "latitude": "41.55060000", "longitude": "2.42690000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 39, "name": "<PERSON>", "country_id": 10, "latitude": "41.20190000", "longitude": "1.52570000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 40, "name": "Costa Bravo", "country_id": 10, "latitude": "41.97940000", "longitude": "3.16610000", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 41, "name": "Gili Islands", "country_id": 6, "latitude": "-8.34050000", "longitude": "116.02750000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 42, "name": "Lombok", "country_id": 6, "latitude": "-8.65000000", "longitude": "116.32000000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 43, "name": "Sumbawa", "country_id": 6, "latitude": "-8.65290000", "longitude": "117.36160000", "currency": "USD", "is_private": false, "is_permit_number": 0}, {"id": 44, "name": "Glyfada", "country_id": 9, "latitude": "37.87443866", "longitude": "23.75560853", "currency": "EUR", "is_private": true, "is_permit_number": 0}, {"id": 45, "name": "<PERSON><PERSON><PERSON><PERSON>", "country_id": 9, "latitude": "37.38570216", "longitude": "23.24543336", "currency": "EUR", "is_private": true, "is_permit_number": 0}], "countries": [{"id": 2, "title": "United Arab Emirates", "currency": "AED"}, {"id": 3, "title": "Thailand", "currency": "THB"}, {"id": 4, "title": "Oman", "currency": "OMR"}, {"id": 5, "title": "United Kingdom", "currency": "GBP"}, {"id": 6, "title": "Indonesia", "currency": "USD"}, {"id": 7, "title": "Qatar", "currency": "AED"}, {"id": 8, "title": "Cyprus South", "currency": "EUR"}, {"id": 9, "title": "Greece", "currency": "EUR"}, {"id": 10, "title": "Spain", "currency": "EUR"}], "catalogs": {"spoken_languages": {"id": 387, "options": [{"id": 429, "value": "Сингальский", "key": "spoken_languages_sinhalese", "data": []}, {"id": 421, "value": "Норвежский", "key": "spoken_languages_norwegian", "data": []}, {"id": 422, "value": "Персидский", "key": "spoken_languages_persian", "data": []}, {"id": 423, "value": "Польский", "key": "spoken_languages_polish", "data": []}, {"id": 424, "value": "Португальский", "key": "spoken_languages_portuguese", "data": []}, {"id": 425, "value": "Пенджаби", "key": "spoken_languages_punjabi", "data": []}, {"id": 426, "value": "Румынский", "key": "spoken_languages_romanian", "data": []}, {"id": 427, "value": "Русский", "key": "spoken_languages_russian", "data": []}, {"id": 428, "value": "Сербский", "key": "spoken_languages_serbian", "data": []}, {"id": 430, "value": "Словацкий", "key": "spoken_languages_slovak", "data": []}, {"id": 431, "value": "Словенский", "key": "spoken_languages_slovene", "data": []}, {"id": 432, "value": "Сомалийский", "key": "spoken_languages_somali", "data": []}, {"id": 433, "value": "Испанский", "key": "spoken_languages_spanish", "data": []}, {"id": 434, "value": "Суахили", "key": "spoken_languages_swahili", "data": []}, {"id": 435, "value": "Шведский", "key": "spoken_languages_swedish", "data": []}, {"id": 436, "value": "Тагальский", "key": "spoken_languages_tagalog", "data": []}, {"id": 437, "value": "Тамильский", "key": "spoken_languages_tamil", "data": []}, {"id": 438, "value": "Телугу", "key": "spoken_languages_telugu", "data": []}, {"id": 439, "value": "Тайский", "key": "spoken_languages_thai", "data": []}, {"id": 440, "value": "Турецкий", "key": "spoken_languages_turkish", "data": []}, {"id": 441, "value": "Украинский", "key": "spoken_languages_ukrainian", "data": []}, {"id": 442, "value": "Урду", "key": "spoken_languages_urdu", "data": []}, {"id": 443, "value": "Узбекский", "key": "spoken_languages_uzbek", "data": []}, {"id": 393, "value": "Белорусский", "key": "spoken_languages_belarusian", "data": []}, {"id": 394, "value": "Бенгальский", "key": "spoken_languages_bengali", "data": []}, {"id": 408, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "key": "spoken_languages_gujarati", "data": []}, {"id": 413, "value": "Каннада", "key": "spoken_languages_kannada", "data": []}, {"id": 395, "value": "Берберский", "key": "spoken_languages_berber", "data": []}, {"id": 396, "value": "Болгарский", "key": "spoken_languages_bulgarian", "data": []}, {"id": 397, "value": "Кантонский", "key": "spoken_languages_cantonese", "data": []}, {"id": 398, "value": "Каталанский", "key": "spoken_languages_catalan", "data": []}, {"id": 399, "value": "Хорватский", "key": "spoken_languages_croatian", "data": []}, {"id": 400, "value": "Чешский", "key": "spoken_languages_czech", "data": []}, {"id": 401, "value": "Датский", "key": "spoken_languages_danish", "data": []}, {"id": 402, "value": "Голландский", "key": "spoken_languages_dutch", "data": []}, {"id": 403, "value": "Английский", "key": "spoken_languages_english", "data": []}, {"id": 404, "value": "Финский", "key": "spoken_languages_finnish", "data": []}, {"id": 405, "value": "Французский", "key": "spoken_languages_french", "data": []}, {"id": 406, "value": "Немецкий", "key": "spoken_languages_german", "data": []}, {"id": 407, "value": "Греческий", "key": "spoken_languages_greek", "data": []}, {"id": 409, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "spoken_languages_hindi", "data": []}, {"id": 410, "value": "Венгерский", "key": "spoken_languages_hungarian", "data": []}, {"id": 411, "value": "Итальянский", "key": "spoken_languages_italian", "data": []}, {"id": 412, "value": "Японский", "key": "spoken_languages_japanese", "data": []}, {"id": 388, "value": "Аф<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "spoken_languages_afrikaans", "data": []}, {"id": 389, "value": "Албанский", "key": "spoken_languages_albanian", "data": []}, {"id": 390, "value": "Амхарский", "key": "spoken_languages_amharic", "data": []}, {"id": 391, "value": "Арабский", "key": "spoken_languages_arabic", "data": []}, {"id": 392, "value": "Азербайджанский", "key": "spoken_languages_azerbaijani", "data": []}, {"id": 414, "value": "Казахский", "key": "spoken_languages_kazakh", "data": []}, {"id": 415, "value": "Корейский", "key": "spoken_languages_korean", "data": []}, {"id": 416, "value": "Курдский", "key": "spoken_languages_kurdi", "data": []}, {"id": 417, "value": "Латышский", "key": "spoken_languages_latvian", "data": []}, {"id": 418, "value": "Малайский", "key": "spoken_languages_malay", "data": []}, {"id": 419, "value": "Мал<PERSON><PERSON><PERSON>ам", "key": "spoken_languages_malayalam", "data": []}, {"id": 420, "value": "<PERSON><PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "spoken_languages_mandarin", "data": []}]}, "unit_furnishing": {"id": 330, "options": [{"id": 332, "value": "Без мебели", "key": "unit_furnishing_unfurnished", "data": []}, {"id": 333, "value": "Частично меблирована", "key": "unit_furnishings_partly_furnished", "data": []}, {"id": 331, "value": "Полная меблировка", "key": "unit_furnishing_furnished", "data": []}]}, "unit_status": {"id": 143, "options": [{"id": 145, "value": "Продана", "key": "unit_status_sold", "data": {"bg_color": "#DD6465"}}, {"id": 146, "value": "Забронирована", "key": "unit_status_reservation", "data": {"bg_color": "#8e8e8d"}}, {"id": 144, "value": "В продаже", "key": "unit_status_sale", "data": {"bg_color": "#20CB6A"}}]}, "project_media_type": {"id": 358, "options": [{"id": 359, "value": "Презентация", "key": "project_media_type_presentation", "data": []}]}, "project_badges": {"id": 103, "options": [{"id": 444, "value": "Эксклюзив", "key": "badge_exclusive", "data": {"bg_color": "#dfe4ff", "icon": "<PERSON><PERSON><PERSON>", "color": "#4F5FD9"}}, {"id": 366, "value": "Рекомендовано", "key": "badge_recommend", "data": {"bg_color": "#dfe4ff", "color": "#4F5FD9"}}, {"id": 385, "value": "<PERSON>н<PERSON><PERSON><PERSON>", "key": "badge_announcement", "data": {"bg_color": "#fff1f2", "color": "#d74652"}}, {"id": 361, "value": "Регистрация интереса (EOI)", "key": "badge_early_booking", "data": {"bg_color": "#E9FAF0", "color": "#20CB6A"}}, {"id": 163, "value": "Старт продаж", "key": "badge_start_sale", "data": {"bg_color": "#fffff1", "remove_days": "14", "color": "#c88902"}}, {"id": 161, "value": "Продано", "key": "badge_sold_out", "data": {"bg_color": "#fff1f2", "color": "#ff0015"}}, {"id": 105, "value": "Беспроцентная рассрочка", "key": "badge_free_installment", "data": {"bg_color": "#e7efff", "color": "#ff0015"}}, {"id": 384, "value": "Предстарт продаж", "key": "badge_prelaunch_sale", "data": {"bg_color": "#fff1f2", "color": "#ff0015"}}, {"id": 367, "value": "10% комиссия", "key": "badge_10_сommission", "data": {"bg_color": "#fff1f2", "color": "#ff0015"}}, {"id": 386, "value": "Цена по запросу", "key": "badge_price_on_request", "data": {"bg_color": "#e6fafa", "color": "#14a1a2"}}]}, "compound_villa_plot": {"id": 240, "options": [{"id": 242, "value": "Участок без дома", "key": "compound_villa_plot_without_house", "data": []}, {"id": 241, "value": "Участок с домом", "key": "compound_villa_plot_with_house", "data": []}, {"id": 243, "value": "Индивидуальный проект", "key": "compound_villa_plot_individual_project", "data": []}]}, "unit_type_room": {"id": 137, "options": [{"id": 138, "value": "Квартира", "key": "unit_type_room_unit", "data": []}, {"id": 462, "value": "Вилла", "key": "unit_type_room_villa", "data": []}, {"id": 445, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "unit_type_room_townhouse", "data": []}, {"id": 140, "value": "Дуплекс", "key": "unit_type_room_duplex", "data": []}, {"id": 362, "value": "Триплекс", "key": "unit_type_room_triplex", "data": []}, {"id": 357, "value": "Пен<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "unit_type_room_penthouse", "data": []}, {"id": 141, "value": "Виллы в ЖК", "key": "unit_type_room_villa_in_project", "data": []}, {"id": 346, "value": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "key": "unit_type_room_retail", "data": []}, {"id": 347, "value": "<PERSON><PERSON><PERSON><PERSON>", "key": "unit_type_room_office", "data": []}]}, "unit_media_type": {"id": 363, "options": [{"id": 365, "value": "Планировка", "key": "unit_media_type_plan", "data": []}, {"id": 364, "value": "Фото", "key": "unit_media_type_photo", "data": []}]}, "unit_complaints": {"id": 449, "options": [{"id": 451, "value": "Нет ответа от брокера", "key": "unit_complaints_no_response_broker", "data": []}, {"id": 453, "value": "Несоответствующие фотографии", "key": "unit_complaints_poor_quality", "data": []}, {"id": 450, "value": "Объект не доступен", "key": "unit_complaints_not_available", "data": []}, {"id": 452, "value": "Нет информации о недвижимости", "key": "unit_complaints_no_details", "data": []}, {"id": 454, "value": "Неточное местоположение", "key": "unit_complaints_locations", "data": []}, {"id": 455, "value": "Неточное количество спален", "key": "unit_complaints_bedrooms", "data": []}, {"id": 456, "value": "Неверный тип недвижимости", "key": "unit_complaints_property_type", "data": []}]}, "project_sales_status": {"id": 4, "options": [{"id": 335, "value": "Анонс продаж", "key": "project_sales_status_announcement", "data": {"color": "#d74652", "bg_color": "#fff1f2"}}, {"id": 338, "value": "Регистрация интереса (EOI)", "key": "project_sales_status_eoi", "data": {"color": "#20CB6A", "bg_color": "#E9FAF0"}}, {"id": 360, "value": "Старт продаж", "key": "project_sales_status_new_launch", "data": {"remove_days": "14", "next_value": "project_sales_status_on_sale", "color": "#c88902", "bg_color": "#fff6e9"}}, {"id": 5, "value": "В продаже", "key": "project_sales_status_on_sale", "data": {"color": "#20CB6A", "bg_color": "#E9FAF0"}}, {"id": 339, "value": "Нет в продаже", "key": "project_sales_status_sold_out", "data": {"color": "#12122D", "bg_color": "#DFDFEB"}}, {"id": 334, "value": "Приостановлен", "key": "project_sales_status_pending", "data": {"color": "#ff0015", "bg_color": "#fff1f2"}}]}, "payment_plan_when": {"id": 349, "options": [{"id": 350, "value": "При бронировании", "key": "payment_plan_when_booking", "data": []}, {"id": 351, "value": "Точная дата", "key": "payment_plan_when_exact_date", "data": []}, {"id": 352, "value": "Период после бронирования", "key": "payment_plan_when_days_after_booking", "data": []}, {"id": 353, "value": "% завершения", "key": "payment_plan_when_completion_rate", "data": []}, {"id": 354, "value": "При сдаче", "key": "payment_plan_when_handover", "data": []}, {"id": 355, "value": "Дней после сдачи", "key": "payment_plan_when_days_after_handover", "data": []}, {"id": 382, "value": "Налог", "key": "payment_plan_when_tax_fee", "data": []}, {"id": 381, "value": "Первый платеж", "key": "payment_plan_when_down_payment", "data": []}, {"id": 383, "value": "Дополнительные платежи", "key": "payment_plan_when_extra_fee", "data": []}, {"id": 465, "value": "Период после первого платежа", "key": "payment_plan_when_days_after_down_payment", "data": []}]}, "booking_payment_method": {"id": 376, "options": [{"id": 377, "value": "SWIFT", "key": "booking_payment_method_swift", "data": []}, {"id": 378, "value": "Карта не России", "key": "booking_status_card_not_russia", "data": []}, {"id": 379, "value": "Карта России", "key": "booking_status_card_russia", "data": []}, {"id": 380, "value": "Другое", "key": "booking_status_other", "data": []}]}, "rooms": {"id": 109, "options": [{"id": 110, "value": "Студия", "key": "rooms_studio", "data": {"short_value": "Ст"}}, {"id": 111, "value": "1 спальня", "key": "rooms_1", "data": {"short_value": "1К"}}, {"id": 112, "value": "2 спальни", "key": "rooms_2", "data": {"short_value": "2К"}}, {"id": 113, "value": "3 спальни", "key": "rooms_3", "data": {"short_value": "3К"}}, {"id": 114, "value": "4 спальни", "key": "rooms_4", "data": {"short_value": "4К"}}, {"id": 115, "value": "5 спален", "key": "rooms_5", "data": {"short_value": "5К"}}, {"id": 341, "value": "6 спален", "key": "rooms_6", "data": {"short_value": "6К"}}, {"id": 342, "value": "7 спален", "key": "rooms_7", "data": {"short_value": "7К"}}, {"id": 343, "value": "8 спален", "key": "rooms_8", "data": {"short_value": "8К"}}, {"id": 344, "value": "9 спален", "key": "rooms_9", "data": {"short_value": "9К"}}, {"id": 345, "value": "10 спален", "key": "rooms_10", "data": {"short_value": "10к"}}, {"id": 164, "value": "Свободная планировка", "key": "rooms_n", "data": {"short_value": "Св"}}]}, "selection_status": {"id": 181, "options": [{"id": 182, "value": "В работе", "key": "selection_status_progress", "data": []}, {"id": 183, "value": "Завершенные", "key": "selection_status_completed", "data": []}, {"id": 184, "value": "Удаленные", "key": "selection_status_removed", "data": []}]}, "development_stage": {"id": 116, "options": [{"id": 117, "value": "Запланировано", "key": "development_stage_scheduled", "data": []}, {"id": 120, "value": "Завершено", "key": "development_stage_finished", "data": []}, {"id": 119, "value": "Приостановлено", "key": "development_stage_stopped", "data": []}, {"id": 348, "value": "Строится", "key": "development_stage_progress", "data": []}]}, "project_facilities": {"id": 66, "options": [{"id": 457, "value": "Брендированный", "key": "project_facilities_branded", "data": {"icon": "branded"}}, {"id": 153, "value": "Консьерж-сервис", "key": "project_facilities_concierge", "data": {"icon": "concierge_service"}}, {"id": 286, "value": "Домашние животные разрешены", "key": "project_facilities_pets", "data": {"icon": "pets"}}, {"id": 281, "value": "Детская игровая площадка", "key": "project_facilities_kids", "data": {"icon": "kids_play_area"}}, {"id": 156, "value": "Зарядка для электроавтомобиля", "key": "project_facilities_car_charger", "data": {"icon": "electric_car_charger"}}, {"id": 446, "value": "Бесплатный A/C", "key": "project_facilities_free_ac", "data": {"icon": "central_ac"}}, {"id": 447, "value": "Частный пляж", "key": "project_facilities_private_beach", "data": {"icon": "private_beach"}}, {"id": 448, "value": "Краткосрочная аренда запрещена", "key": "project_facilities_short_rental_forbidden", "data": {"icon": "short_rental_forbidden"}}, {"id": 463, "value": "Приватный бассейн", "key": "project_facilities_private_pool", "data": {"icon": "private_pool"}}, {"id": 464, "value": "Комната Прислуги/Кабинет", "key": "project_facilities_maid_room", "data": {"icon": "maid_room"}}]}, "project_gallery_category": {"id": 92, "options": [{"id": 107, "value": "Презентация проекта", "key": "project_gallery_category_presentation", "data": []}, {"id": 93, "value": "Процесс строительства", "key": "project_gallery_category_construction_progress", "data": []}, {"id": 94, "value": "Варианты отделки", "key": "project_gallery_category_finishing_examples", "data": []}, {"id": 95, "value": "Инфраструктура", "key": "project_gallery_category_infrastructure", "data": []}, {"id": 96, "value": "Вид", "key": "project_gallery_category_view", "data": []}]}, "booking_status": {"id": 368, "options": [{"id": 374, "value": "Ожидание оплаты комиссии", "key": "booking_status_waiting_commission_payed", "data": []}, {"id": 369, "value": "Создан", "key": "booking_status_created", "data": []}, {"id": 370, "value": "Зафиксировано", "key": "booking_status_fixed", "data": []}, {"id": 371, "value": "Оплата бронирования", "key": "booking_status_booking_payment", "data": []}, {"id": 372, "value": "Авансовый платеж", "key": "booking_status_down_payment", "data": []}, {"id": 373, "value": "Забронировано", "key": "booking_status_booked", "data": []}, {"id": 375, "value": "Комиссия уплачена", "key": "booking_status_commission_payed", "data": []}]}}, "currencies": {"AED": {"EUR": 0.2389151408, "GBP": 0.2047898542, "IDR": 4576.545032812, "INR": 23.2526793174, "JPY": 38.7188313139, "OMR": 0.1046033215, "PKR": 76.4045016612, "RUB": 22.3552877632, "THB": 9.0851423425, "TRY": 10.3510507264, "USD": 0.2723051678}}, "statistics": {"projects": 2036, "units": 631833}, "priceRanges": [{"count": 2, "from": 265126, "to": 468953}, {"count": 535, "from": 468953, "to": 672780}, {"count": 1672, "from": 672780, "to": 876607}, {"count": 1465, "from": 876607, "to": 1080434}, {"count": 3346, "from": 1080434, "to": 1284261}, {"count": 1859, "from": 1284261, "to": 1488088}, {"count": 1434, "from": 1488088, "to": 1691915}, {"count": 1572, "from": 1691915, "to": 1895742}, {"count": 1459, "from": 1895742, "to": 2099569}, {"count": 1547, "from": 2099569, "to": 2303396}, {"count": 818, "from": 2303396, "to": 2507223}, {"count": 1008, "from": 2507223, "to": 2711050}, {"count": 524, "from": 2711050, "to": 2914877}, {"count": 328, "from": 2914877, "to": 3118704}, {"count": 475, "from": 3118704, "to": 3322531}, {"count": 434, "from": 3322531, "to": 3526358}, {"count": 271, "from": 3526358, "to": 3730185}, {"count": 448, "from": 3730185, "to": 3934012}, {"count": 378, "from": 3934012, "to": 4137839}, {"count": 404, "from": 4137839, "to": 4341666}, {"count": 250, "from": 4341666, "to": 4545493}, {"count": 210, "from": 4545493, "to": 4749320}, {"count": 139, "from": 4749320, "to": 4953147}, {"count": 108, "from": 4953147, "to": 5156974}, {"count": 101, "from": 5156974, "to": 5360801}, {"count": 68, "from": 5360801, "to": 5564628}, {"count": 67, "from": 5564628, "to": 5768455}, {"count": 34, "from": 5768455, "to": 5972282}, {"count": 42, "from": 5972282, "to": 6176109}, {"count": 2331, "from": 6176109, "to": 340500000}], "priceAreaRanges": [{"count": 3, "from": "1841.0", "to": "3465.0"}, {"count": 0, "from": "3465.0", "to": "5089.0"}, {"count": 2, "from": "5089.0", "to": "6713.0"}, {"count": 117, "from": "6713.0", "to": "8337.0"}, {"count": 115, "from": "8337.0", "to": "9961.0"}, {"count": 244, "from": "9961.0", "to": "11585.0"}, {"count": 735, "from": "11585.0", "to": "13209.0"}, {"count": 1439, "from": "13209.0", "to": "14833.0"}, {"count": 2230, "from": "14833.0", "to": "16457.0"}, {"count": 2421, "from": "16457.0", "to": "18081.0"}, {"count": 1889, "from": "18081.0", "to": "19705.0"}, {"count": 2313, "from": "19705.0", "to": "21329.0"}, {"count": 2907, "from": "21329.0", "to": "22953.0"}, {"count": 1111, "from": "22953.0", "to": "24577.0"}, {"count": 1103, "from": "24577.0", "to": "26201.0"}, {"count": 1396, "from": "26201.0", "to": "27825.0"}, {"count": 1123, "from": "27825.0", "to": "29449.0"}, {"count": 534, "from": "29449.0", "to": "31073.0"}, {"count": 341, "from": "31073.0", "to": "32697.0"}, {"count": 297, "from": "32697.0", "to": "34321.0"}, {"count": 244, "from": "34321.0", "to": "35945.0"}, {"count": 234, "from": "35945.0", "to": "37569.0"}, {"count": 189, "from": "37569.0", "to": "39193.0"}, {"count": 108, "from": "39193.0", "to": "40817.0"}, {"count": 137, "from": "40817.0", "to": "42441.0"}, {"count": 121, "from": "42441.0", "to": "44065.0"}, {"count": 106, "from": "44065.0", "to": "45689.0"}, {"count": 80, "from": "45689.0", "to": "47313.0"}, {"count": 69, "from": "47313.0", "to": "48937.0"}, {"count": 1671, "from": 48937, "to": 24000000}], "permission": [{"name": "villas_list", "title": "Villas List", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "villas_plan", "title": "Master Plan (Villas)", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "transactions", "title": "Transactions", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "construction_information", "title": "Extended Construction Progress Information", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "client_mode", "title": "Client Mode", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "booking", "title": "Booking", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "brochure", "title": "Brochures", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "assignment", "title": "Secondary", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "units_list", "title": "Units List", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "units_chess", "title": "Chessboard", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "units_floor_plan", "title": "Floor Layout", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "sales_selection_share", "title": "Sales Offer Share", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "promotions", "title": "Promotions", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "sales_selection_selections", "title": "Sales Offer Collections", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}, {"name": "builder_contacts", "title": "Developer Contacts", "is_create": false, "is_read": false, "is_update": false, "is_delete": false}], "user": null, "invites": [], "onboarding": []}